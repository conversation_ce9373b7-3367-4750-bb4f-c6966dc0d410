// This file was generated by the <PERSON><PERSON> wizard because we couldn't find a root layout file.
import * as <PERSON><PERSON> from '@sentry/nextjs';
import type { Metadata } from 'next';

export function generateMetadata(): Metadata {
  return {
    other: {
      ...Sentry.getTraceData(),
    }
  }
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}
