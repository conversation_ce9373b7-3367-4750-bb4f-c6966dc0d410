export type Profile = {
  id: string;
  username: string;
  display_name: string | null;
  bio: string | null;
  avatar_url: string | null;
  nationality: string | null;
  rank_level: number;
  total_study_time: number;
  created_at: string;
  updated_at: string;
};

export type StudyMaterial = {
  id: string;
  recipe_id: string;
  material_name: string;
  frequency: string;
  time_per_session: number;
  time_unit: string;
  order_index: number;
  created_at: string;
};

export type StudyRecipe = {
  id: string;
  user_id: string;
  title: string;
  estimated_duration?: string;
  tips?: string;
  tags: string[];
  upvote_count: number;
  comment_count: number;
  created_at: string;
  updated_at: string;
  materials?: StudyMaterial[];
  author?: Profile;
  is_bookmarked?: boolean;
  is_upvoted?: boolean;
};

export type StudyLog = {
  id: string;
  user_id: string;
  recipe_id: string;
  material_id?: string;
  title: string;
  duration_minutes: number;
  duration_seconds: number;
  notes?: string;
  timer_type: 'countdown' | 'stopwatch';
  completed_at: string;
  comment_count: number;
  recipe?: StudyRecipe;
  material?: StudyMaterial;
  author?: Profile;
};

export type Comment = {
  id: string;
  user_id: string;
  recipe_id?: string;
  log_id?: string;
  parent_id?: string;
  content: string;
  mentions: string[];
  created_at: string;
  author?: Profile;
  replies?: Comment[];
  parent_comment?: {
    author?: Profile;
  };
};

export type Reaction = {
  id: string;
  user_id: string;
  log_id: string;
  emoji: string;
  created_at: string;
};

export type ReactionSummary = {
  emoji: string;
  count: number;
  users: string[]; // user IDs who reacted with this emoji
};

export type LogReactions = {
  reactions: ReactionSummary[];
  userReactions: string[]; // emojis the current user has reacted with
};

// Gamification Types
export type UserStats = {
  id: string;
  user_id: string;
  total_study_sessions: number;
  current_streak_days: number;
  longest_streak_days: number;
  last_study_date: string | null;
  recipes_created: number;
  total_upvotes_received: number;
  total_comments_received: number;
  total_bookmarks_received: number;
  upvotes_given: number;
  comments_made: number;
  bookmarks_made: number;
  total_experience_points: number;
  created_at: string;
  updated_at: string;
};

export type Achievement = {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'study' | 'community' | 'consistency' | 'milestone';
  requirement_type: string;
  requirement_value: number;
  points_reward: number;
  created_at: string;
};

export type UserAchievement = {
  id: string;
  user_id: string;
  achievement_id: string;
  earned_at: string;
};

export type Level = {
  level: number;
  name: string;
  min_experience_points: number;
  max_experience_points: number | null;
  badge_color: string;
  created_at: string;
};

export type ActivityLog = {
  id: string;
  user_id: string;
  activity_type: string;
  points_earned: number;
  reference_id: string | null;
  metadata: Record<string, any>;
  created_at: string;
};

export type LeaderboardEntry = {
  id: string;
  username: string;
  display_name: string | null;
  avatar_url: string | null;
  rank_level: number;
  total_study_time: number;
  total_experience_points: number;
  current_streak_days: number;
  total_study_sessions: number;
  recipes_created: number;
  total_upvotes_received: number;
  global_rank: number;
};

export type UserWithStats = {
  id: string;
  username: string;
  display_name: string | null;
  avatar_url: string | null;
  rank_level: number;
  total_study_time: number;
  stats: UserStats;
  achievements: Achievement[];
  level_info: {
    currentLevel: Level;
    nextLevel: Level | null;
    progress: number;
    pointsToNext: number;
  };
};

// Study Categories Types
export type StudyCategory = {
  id: string;
  name: string;
  display_name: string;
  description: string | null;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
};

export type UserStudyCategory = {
  id: string;
  user_id: string;
  category_id: string;
  created_at: string;
  category?: StudyCategory;
};

// Onboarding Types
export type OnboardingStep = 'profile-image' | 'username-nationality' | 'study-categories';

export type OnboardingData = {
  profileImage?: File | null;
  username?: string;
  nationality?: string;
  studyCategories?: string[];
};
