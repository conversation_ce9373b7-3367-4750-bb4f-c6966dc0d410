# Supabase環境リセット・再構築ガイド

## 概要
既存のSupabaseプロジェクトがVercelと紐づいており、新しいプロジェクトが作成できない状況を解決するため、オーガニゼーションから作り直して3つの環境を構築します。

## 🎯 実行手順

### 1. 新しいSupabaseオーガニゼーション・プロジェクト作成

#### ステップ1: オーガニゼーション作成
1. https://supabase.com/dashboard にアクセス
2. 左上のオーガニゼーション名をクリック
3. 「Create a new organization」を選択
4. Organization name: `StudyShare` または `studyshare-org`
5. 「Create organization」をクリック

#### ステップ2: 2つのプロジェクト作成

**本番用プロジェクト:**
- Project name: `studyshare-production`
- Database Password: [強力なパスワード - メモ必須]
- Region: `Asia Northeast 1 (Tokyo)`

**ステージング用プロジェクト:**
- Project name: `studyshare-staging`
- Database Password: [強力なパスワード - メモ必須]
- Region: `Asia Northeast 1 (Tokyo)`

**開発環境:**
- ローカルSupabase (Supabase CLI + Docker)
- 完全無料、オフライン開発可能

#### ステップ3: プロジェクト情報の取得

各プロジェクトで「Settings」→「API」から以下を取得：

```
=== Production Project ===
Project URL: https://[PROD_PROJECT_ID].supabase.co
API Keys:
- anon: [PROD_ANON_KEY]
- service_role: [PROD_SERVICE_ROLE_KEY]
Database Password: [PROD_DB_PASSWORD]

=== Staging Project ===
Project URL: https://[STAGING_PROJECT_ID].supabase.co
API Keys:
- anon: [STAGING_ANON_KEY]
- service_role: [STAGING_SERVICE_ROLE_KEY]
Database Password: [STAGING_DB_PASSWORD]

=== Development Project ===
Project URL: https://[DEV_PROJECT_ID].supabase.co
API Keys:
- anon: [DEV_ANON_KEY]
- service_role: [DEV_SERVICE_ROLE_KEY]
Database Password: [DEV_DB_PASSWORD]
```

### 2. データベーススキーマの適用

#### 各プロジェクトで以下を実行：

1. **SQL Editorにアクセス**
   - 左サイドバー「SQL Editor」
   - 「New query」をクリック

2. **スキーマファイルの内容をコピー&ペースト**
   - `scripts/setup-staging-schema.sql` の内容を使用
   - 「Run」ボタンをクリック

3. **実行結果の確認**
   - エラーがないことを確認
   - 「Table Editor」でテーブルが作成されていることを確認

### 3. Vercel Environment Variables の更新

#### Vercelダッシュボードで設定：

**Production環境:**
```
NEXT_PUBLIC_SUPABASE_URL = https://[PROD_PROJECT_ID].supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY = [PROD_ANON_KEY]
SUPABASE_SERVICE_ROLE_KEY = [PROD_SERVICE_ROLE_KEY]
POSTGRES_URL = postgres://postgres.[PROD_PROJECT_ID]:[PROD_DB_PASSWORD]@aws-0-ap-northeast-1.pooler.supabase.com:6543/postgres?sslmode=require&supa=base-pooler.x
POSTGRES_HOST = db.[PROD_PROJECT_ID].supabase.co
POSTGRES_PASSWORD = [PROD_DB_PASSWORD]
NODE_ENV = production
NEXT_PUBLIC_APP_ENV = production
```

**Preview環境:**
```
NEXT_PUBLIC_SUPABASE_URL = https://[STAGING_PROJECT_ID].supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY = [STAGING_ANON_KEY]
SUPABASE_SERVICE_ROLE_KEY = [STAGING_SERVICE_ROLE_KEY]
POSTGRES_URL = postgres://postgres.[STAGING_PROJECT_ID]:[STAGING_DB_PASSWORD]@aws-0-ap-northeast-1.pooler.supabase.com:6543/postgres?sslmode=require&supa=base-pooler.x
POSTGRES_HOST = db.[STAGING_PROJECT_ID].supabase.co
POSTGRES_PASSWORD = [STAGING_DB_PASSWORD]
NODE_ENV = production
NEXT_PUBLIC_APP_ENV = staging
ENABLE_TEST_FEATURES = true
```

### 4. ローカル環境設定の更新

#### .env.local の更新：
```env
# Development Environment
NEXT_PUBLIC_SUPABASE_URL=https://[DEV_PROJECT_ID].supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=[DEV_ANON_KEY]
SUPABASE_SERVICE_ROLE_KEY=[DEV_SERVICE_ROLE_KEY]
POSTGRES_URL=postgres://postgres.[DEV_PROJECT_ID]:[DEV_DB_PASSWORD]@aws-0-ap-northeast-1.pooler.supabase.com:6543/postgres?sslmode=require&supa=base-pooler.x
POSTGRES_HOST=db.[DEV_PROJECT_ID].supabase.co
POSTGRES_PASSWORD=[DEV_DB_PASSWORD]
NODE_ENV=development
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_DEBUG_MODE=true
ENABLE_TEST_FEATURES=true
```

#### next.config.js の更新：
```javascript
images: {
  domains: [
    '[PROD_PROJECT_ID].supabase.co', // Production
    '[STAGING_PROJECT_ID].supabase.co', // Staging
    '[DEV_PROJECT_ID].supabase.co', // Development
  ],
},
```

### 5. 動作確認

#### ローカル環境：
```bash
npm run check:env
npm run dev
```

#### ステージング環境：
```bash
git checkout staging
git push origin staging
# Vercel Preview環境で確認
```

#### 本番環境：
```bash
git checkout main
git push origin main
# Vercel Production環境で確認
```

## 📋 チェックリスト

### Supabase設定
- [ ] 新しいオーガニゼーション作成
- [ ] 本番用プロジェクト作成・情報取得
- [ ] ステージング用プロジェクト作成・情報取得
- [ ] 開発用プロジェクト作成・情報取得
- [ ] 各プロジェクトにスキーマ適用

### Vercel設定
- [ ] 既存環境変数の削除/更新
- [ ] Production環境変数設定
- [ ] Preview環境変数設定

### ローカル設定
- [ ] .env.local 更新
- [ ] next.config.js 更新

### 動作確認
- [ ] ローカル環境での接続確認
- [ ] ステージング環境での接続確認
- [ ] 本番環境での接続確認

## ⚠️ 注意事項

1. **データの移行**: 既存データは失われるため、必要に応じて事前にエクスポート
2. **API Keys**: 新しいAPIキーは安全に管理し、古いキーは無効化
3. **環境変数**: すべての環境で正しいプロジェクトに接続されていることを確認
4. **テスト**: 各環境でユーザー登録・ログイン・データ操作が正常に動作することを確認

この手順により、完全に分離された3つの環境が構築されます。
