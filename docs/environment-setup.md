# Environment Setup Guide
StudyShare プロジェクトの環境設定ガイド

## 概要
このプロジェクトは以下の3つの環境で運用されます：
- **Development (開発環境)**: ローカル開発用
- **Staging (ステージング環境)**: テスト・検証用
- **Production (本番環境)**: 実際のユーザー向け

## 環境設定ファイル

### 1. 開発環境 (.env.development)
ローカル開発用の設定
- 開発用Supabaseプロジェクトを使用
- デバッグモード有効
- テスト機能有効

### 2. ステージング環境 (.env.staging)
テスト・検証用の設定
- ステージング用Supabaseプロジェクトを使用
- 本番に近い設定だがテスト機能有効
- デバッグモード無効

### 3. 本番環境 (.env.production)
実際のユーザー向け設定
- 本番用Supabaseプロジェクトを使用
- テスト機能無効
- デバッグモード無効

## Supabase プロジェクト設定

### 必要なプロジェクト
1. **本番用プロジェクト** (既存)
   - プロジェクト名: studyshare-production
   - URL: https://tyfcubkezcnnekyvkzii.supabase.co

2. **ステージング用プロジェクト** (要作成)
   - プロジェクト名: studyshare-staging
   - 本番と同じスキーマを適用

3. **開発用プロジェクト** (要作成)
   - プロジェクト名: studyshare-development
   - 本番と同じスキーマを適用

### プロジェクト作成手順
1. https://supabase.com にアクセス
2. "New Project" をクリック
3. プロジェクト名を入力
4. リージョンを選択 (Asia Northeast 1 - Tokyo)
5. データベースパスワードを設定
6. プロジェクト作成完了後、以下の情報を取得：
   - Project URL
   - API Keys (anon, service_role)
   - Database URL

## Vercel デプロイメント設定

### ブランチ戦略
- `main` ブランチ → 本番環境
- `staging` ブランチ → ステージング環境
- `develop` ブランチ → 開発環境 (プレビュー)

### Vercel プロジェクト設定
1. Vercel ダッシュボードで環境変数を設定
2. 各環境に対応する環境変数を設定：
   - Production: `.env.production` の内容
   - Preview (Staging): `.env.staging` の内容

## ローカル開発環境セットアップ

### 1. 環境設定ファイルの準備
```bash
# .env.example をコピーして .env.local を作成
cp .env.example .env.local

# 開発用の設定値を入力
# 開発用Supabaseプロジェクトの情報を設定
```

### 2. 依存関係のインストール
```bash
npm install
```

### 3. 開発サーバーの起動
```bash
# 開発環境で起動
npm run dev

# ステージング環境設定で起動
npm run dev:staging

# 本番環境設定で起動
npm run dev:production
```

## 環境別コマンド

### 開発
```bash
npm run dev              # 開発環境
npm run dev:staging      # ステージング設定
npm run dev:production   # 本番設定
```

### ビルド
```bash
npm run build            # デフォルト
npm run build:staging    # ステージング用
npm run build:production # 本番用
```

### テスト
```bash
npm run test:staging     # ステージング環境でテスト
npm run test:production-like # 本番環境でテスト
```

## 環境変数の管理

### 必須環境変数
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`
- `POSTGRES_URL`
- `NODE_ENV`

### 環境固有変数
- `NEXT_PUBLIC_APP_ENV`: 環境識別用
- `NEXT_PUBLIC_DEBUG_MODE`: デバッグモード
- `ENABLE_TEST_FEATURES`: テスト機能の有効/無効
- `APP_URL`: アプリケーションのベースURL

## セキュリティ考慮事項

### 1. 環境変数の保護
- `.env.*` ファイルは `.gitignore` に追加済み
- 本番環境の秘密情報は Vercel の環境変数で管理

### 2. データベース分離
- 各環境で独立したSupabaseプロジェクトを使用
- 本番データが開発・ステージング環境に影響しない

### 3. アクセス制御
- 各環境のSupabaseプロジェクトで適切なRLSポリシーを設定
- ステージング環境はIP制限やBasic認証を検討

## トラブルシューティング

### 環境変数が読み込まれない
1. ファイル名を確認 (`.env.local`, `.env.staging`, etc.)
2. 変数名のプレフィックスを確認 (`NEXT_PUBLIC_` が必要な場合)
3. サーバーを再起動

### Supabase接続エラー
1. プロジェクトURLとAPIキーを確認
2. Supabaseプロジェクトの状態を確認
3. ネットワーク接続を確認

### ビルドエラー
1. 環境変数が正しく設定されているか確認
2. 依存関係を再インストール: `npm ci`
3. キャッシュをクリア: `rm -rf .next`
