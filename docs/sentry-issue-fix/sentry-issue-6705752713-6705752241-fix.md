# Sentry Issue 6705752713 & 6705752241 修正レポート

## 📊 Issue概要

### Issue 6705752713
- **エラー内容**: `Error: Error fetching user upvoted recipes: {"code":"42703","details":null,"hint":null,"message":"column study_recipes.comment_count does not exist"}`
- **発生場所**: `/profile/[username]` ページ
- **発生回数**: 1回（2025-06-24 14:22:59）

### Issue 6705752241
- **エラー内容**: `Error: Error fetching user bookmarked recipes: {"code":"42703","details":null,"hint":null,"message":"column study_recipes.comment_count does not exist"}`
- **発生場所**: `/profile/[username]` ページ
- **発生回数**: 5回（2025-06-23 16:50:05 〜 2025-06-24 14:23:11）

## 🔍 根本原因の分析

### 1. データベーススキーマの不整合
- **問題**: `study_recipes`テーブルに`comment_count`カラムが存在しない
- **影響範囲**: プロフィールページでのUpvoted RecipesとBookmarked Recipesの取得処理

### 2. コードとスキーマの不一致
- **コード側**: `lib/api/profiles.ts`の`fetchUserUpvotedRecipes`と`fetchUserBookmarkedRecipes`関数で`comment_count`カラムを参照
- **データベース側**: `study_recipes`テーブルに`comment_count`カラムが定義されていない

### 3. 具体的な問題箇所

#### `fetchUserUpvotedRecipes`関数（Line 139-145）
```typescript
const { data: recipes, error: recipesError } = await supabase
  .from('study_recipes')
  .select(`
    *,
    comment_count,  // ← このカラムが存在しない
    study_materials(*)
  `)
  .in('id', recipeIds)
```

#### `fetchUserBookmarkedRecipes`関数（Line 220-226）
```typescript
const { data: recipes, error: recipesError } = await supabase
  .from('study_recipes')
  .select(`
    *,
    comment_count,  // ← このカラムが存在しない
    study_materials(*)
  `)
  .in('id', recipeIds)
```

## 💡 `comment_count`の重要性

### UIでの使用箇所
1. **RecipeCard.tsx**: コメントボタンに「Comment 5」のように数字を表示
2. **StudyLogCard.tsx**: 学習ログのコメント数表示
3. **CommentList.tsx**: 初期コメント数の設定
4. **pages/recipes/[id].tsx**: レシピ詳細ページでのコメント数表示

### パフォーマンス上の利点
- **事前計算**: コメント数を毎回計算する必要がない
- **高速表示**: フィード表示時にJOINクエリが不要
- **スケーラビリティ**: コメント数が多くなってもパフォーマンスが劣化しない

### ユーザー体験への影響
- **即座の表示**: コメント数がすぐに表示される
- **リアルタイム更新**: コメント追加時に数字が即座に更新
- **エンゲージメント指標**: ユーザーが人気のコンテンツを判断できる

## 🛠️ 修正方針

### A案: データベーススキーマ修正（強く推奨）
**理由**: `comment_count`はUI表示とパフォーマンスに重要
1. `study_recipes`テーブルに`comment_count`カラムを追加
2. 既存データの`comment_count`を計算して更新
3. コメント追加・削除時に`comment_count`を自動更新するトリガーを作成

### B案: コード修正（非推奨）
**デメリット**: パフォーマンス劣化とUI表示の遅延
1. `comment_count`カラムの参照を削除
2. 動的にコメント数を計算する処理に変更（既存の`fetchUserRecipes`関数と同様）

## 📋 推奨する修正手順

### Step 1: データベーススキーマ修正
```sql
-- study_recipesテーブルにcomment_countカラムを追加
ALTER TABLE public.study_recipes 
ADD COLUMN comment_count INTEGER DEFAULT 0;

-- 既存データのcomment_countを計算して更新
UPDATE public.study_recipes 
SET comment_count = (
  SELECT COUNT(*) 
  FROM public.comments 
  WHERE comments.recipe_id = study_recipes.id
);

-- コメント追加時のトリガー関数
CREATE OR REPLACE FUNCTION update_recipe_comment_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE public.study_recipes 
    SET comment_count = comment_count + 1 
    WHERE id = NEW.recipe_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE public.study_recipes 
    SET comment_count = comment_count - 1 
    WHERE id = OLD.recipe_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- トリガーの作成
CREATE TRIGGER recipe_comment_count_trigger
  AFTER INSERT OR DELETE ON public.comments
  FOR EACH ROW
  EXECUTE FUNCTION update_recipe_comment_count();
```

### Step 2: マイグレーションファイル作成 ✅
- `supabase/migrations/20250625000000_add_comment_count_to_recipes.sql`を作成済み
- レシピ専用のコメント数更新（学習ログのコメントは除外）
- INSERT/UPDATE/DELETE全てに対応するトリガー
- 負の値を防ぐGREATEST関数を使用

### Step 3: 実行スクリプト作成 ✅
- `scripts/apply-comment-count-migration.js`を作成済み
- ローカル環境とステージング環境に対応
- 実行前の確認とエラーハンドリング
- 実行後の検証手順を含む

### Step 4: 環境別適用手順
#### ローカル環境での適用
```bash
# ローカルデータベースをリセット（全マイグレーション適用）
node scripts/apply-comment-count-migration.js local
```

#### ステージング環境での適用
```bash
# ステージング環境にマイグレーションをプッシュ
node scripts/apply-comment-count-migration.js staging
```

## 📊 期待される効果

### 1. エラーの解消
- Sentry Issue 6705752713 & 6705752241の完全解決
- プロフィールページでのUpvoted/Bookmarked Recipes表示の正常化

### 2. パフォーマンス向上
- `comment_count`の事前計算により、動的計算が不要
- データベースクエリの最適化

### 3. データ整合性の向上
- トリガーによる自動更新でデータの一貫性を保証

## ⚠️ 注意事項

### 1. データ移行時の注意
- 大量データがある場合、`UPDATE`処理に時間がかかる可能性
- 本番環境では低負荷時間帯での実行を推奨

### 2. 既存機能への影響
- `fetchUserRecipes`関数は動的計算を使用しているため、整合性確認が必要
- 全ての`comment_count`参照箇所の統一を検討

### 3. テスト項目
- プロフィールページでのUpvoted/Bookmarked Recipes表示
- コメント追加・削除時の`comment_count`自動更新
- 既存のレシピ一覧表示への影響確認

## 📝 実装後の確認項目

1. **機能テスト**
   - [ ] プロフィールページでUpvoted Recipesが正常表示される
   - [ ] プロフィールページでBookmarked Recipesが正常表示される
   - [ ] コメント追加時に`comment_count`が増加する
   - [ ] コメント削除時に`comment_count`が減少する

2. **Sentry監視**
   - [ ] Issue 6705752713の再発がない
   - [ ] Issue 6705752241の再発がない
   - [ ] 新たなデータベースエラーが発生していない

3. **パフォーマンス確認**
   - [ ] プロフィールページの読み込み速度
   - [ ] データベースクエリの実行時間

---

**作成日**: 2025-06-25  
**担当者**: Cline AI Assistant  
**ステータス**: 実装完了・マイグレーション適用済み

## 🎯 実装完了報告

### ✅ マイグレーション実行結果
- **ローカル環境**: 正常適用完了（2025-06-25 14:18）
- **ステージング環境**: 正常適用完了（2025-06-25 14:20）

### 📊 適用されたマイグレーション
- `20250625000000_add_comment_count_to_recipes.sql`
- `comment_count`カラム追加
- 既存データの`comment_count`値計算・更新
- 自動更新トリガー関数作成

### 🔍 次のステップ
1. **機能テスト実施**
   - プロフィールページでのUpvoted/Bookmarked Recipes表示確認
   - コメント追加・削除時の`comment_count`自動更新確認
2. **Sentry監視**
   - Issue 6705752713 & 6705752241の再発監視
   - 新たなエラーの発生有無確認



