# Sentry Issue 6703393345 修正レポート

## 📋 Issue詳細

- **Issue ID**: 6703393345
- **Short ID**: FORPLE-STG-6
- **エラーメッセージ**: `Module.c` / `Object captured as exception with keys: code, details, hint, message`
- **レベル**: error
- **発生回数**: 1回
- **発生日時**: 2025-06-23 16:36:43 UTC
- **ステータス**: 未解決（new）
- **優先度**: High
- **プラットフォーム**: javascript
- **発生場所**: `/profile/[username]` ページ
- **ファイル**: `app:///_next/static/chunks/pages/_app-25fcc0f3989812ef.js`
- **関数**: `Module.c`

## 🔍 根本原因分析

### 1. エラーの特定
このエラーは**Supabaseのエラーオブジェクトが適切にハンドリングされていない**ことが原因です。

**根本的な問題**:
1. **Supabaseエラーオブジェクトの不適切な処理**: Supabaseから返されるエラーオブジェクト（`{code, details, hint, message}`）がそのままSentryに送信されている
2. **グローバルエラーハンドラーの問題**: `lib/monitoring/global-error-handler.ts`でSupabaseエラーオブジェクトが適切に変換されていない
3. **プロフィールページでのAPI呼び出しエラー**: `/profile/[username]`ページでのデータ取得時にエラーが発生

### 2. エラーの詳細分析
- **発生場所**: プロフィールページ（`/profile/[username]`）
- **原因**: `lib/api/profiles.ts`内のAPI呼び出しでSupabaseエラーが発生
- **問題**: エラーオブジェクトがそのままthrowされ、グローバルエラーハンドラーで適切に処理されていない

### 3. 影響範囲
- **プロフィールページ**: ユーザープロフィール表示時のエラー
- **API呼び出し**: `fetchProfileByUsername`, `fetchUserRecipes`, `fetchUserLogs`等
- **エラー監視**: Sentryでの適切なエラー情報表示ができない

## 🛠️ 修正方針

### 1. グローバルエラーハンドラーの改善
Supabaseエラーオブジェクトを適切に処理するよう修正します。

**修正内容**:
```typescript
// lib/monitoring/global-error-handler.ts の改善
function sendToSentry(error: any, context: string, level: 'error' | 'warning' = 'error') {
  try {
    // エラーフィルタリング
    if (shouldIgnoreError(error)) {
      return
    }

    const errorHash = generateErrorHash(error, context)

    // 重複チェック
    if (sentErrorCache.has(errorHash)) {
      return
    }
    sentErrorCache.add(errorHash)

    // Supabaseエラーオブジェクトの処理を追加
    let sentryError: Error
    if (error instanceof Error) {
      sentryError = error
    } else if (typeof error === 'string') {
      sentryError = new Error(error)
    } else if (error && typeof error === 'object' && error.message) {
      // Supabaseエラーオブジェクトの場合
      const supabaseError = error as { code?: string, message: string, details?: string, hint?: string }
      sentryError = new Error(supabaseError.message)
      sentryError.name = `SupabaseError${supabaseError.code ? `_${supabaseError.code}` : ''}`
    } else {
      sentryError = new Error(JSON.stringify(error))
    }

    // Sentryに送信
    Sentry.captureException(sentryError, {
      level,
      tags: {
        component: 'GlobalErrorHandler',
        errorType: error?.code ? 'supabase_error' : 'console_error',
        context,
        supabaseCode: error?.code || undefined
      },
      extra: {
        originalError: error,
        errorType: typeof error,
        supabaseDetails: error?.details || undefined,
        supabaseHint: error?.hint || undefined,
        timestamp: new Date().toISOString()
      }
    })

    console.log(`🚀 Sent ${level} to Sentry:`, errorHash)
  } catch (sentryError) {
    originalConsoleError('Failed to send error to Sentry:', sentryError)
  }
}
```

### 2. プロフィールAPI関数の改善
エラーハンドリングを強化し、より詳細なエラー情報を提供します。

**修正内容**:
```typescript
// lib/api/profiles.ts の改善
export const fetchProfileByUsername = async (username: string): Promise<Profile | null> => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('username', username)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null
      }
      // エラーオブジェクトを適切なErrorインスタンスに変換
      const profileError = new Error(`Failed to fetch profile: ${error.message}`)
      profileError.name = 'ProfileFetchError'
      throw profileError
    }

    return data
  } catch (error) {
    console.error('Error fetching profile by username:', error)
    
    // Supabaseエラーの場合は適切に変換
    if (error && typeof error === 'object' && 'code' in error) {
      const supabaseError = error as any
      const convertedError = new Error(`Supabase Error: ${supabaseError.message}`)
      convertedError.name = `SupabaseError_${supabaseError.code}`
      throw convertedError
    }
    
    throw error
  }
}
```

### 3. エラーフィルタリングの追加
不要なSupabaseエラーをフィルタリングします。

**修正内容**:
```typescript
// shouldIgnoreError関数に追加
export function shouldIgnoreError(error: any): boolean {
  const errorStr = typeof error === 'string' ? error : error?.message || error?.toString() || ''

  // 既存のフィルタリング...

  // Supabaseの一般的なエラーをフィルタリング
  if (error?.code === 'PGRST116' && errorStr.includes('not found')) {
    return true // プロフィール未発見エラー（正常な動作）
  }

  // 一時的なネットワークエラー
  if (error?.code === 'PGRST301' || error?.code === 'PGRST302') {
    return true // 一時的なSupabaseエラー
  }

  return false
}
```

## 📊 期待される効果

### 1. エラー情報の改善
- **適切なエラーメッセージ**: 「Module.c」ではなく具体的なエラー内容が表示
- **Supabaseエラーの詳細**: コード、詳細、ヒントが適切に記録
- **デバッグ情報の充実**: エラーの原因特定が容易になる

### 2. 監視精度の向上
- **重複エラーの削減**: 同じSupabaseエラーの重複送信を防止
- **エラー分類の改善**: Supabaseエラーとアプリケーションエラーの区別
- **不要なアラートの削減**: 正常な動作（プロフィール未発見等）のフィルタリング

### 3. 開発効率の向上
- **問題の早期発見**: 具体的なエラー情報による迅速な問題特定
- **デバッグ時間の短縮**: 詳細なエラー情報による効率的なデバッグ
- **ユーザー体験の改善**: エラー処理の改善によるアプリの安定性向上

## 🎯 実装手順

### Phase 1: グローバルエラーハンドラーの修正
1. `lib/monitoring/global-error-handler.ts`のSupabaseエラー処理を改善
2. エラーフィルタリング機能を強化
3. テスト環境での動作確認

### Phase 2: API関数の改善
1. `lib/api/profiles.ts`のエラーハンドリングを強化
2. 他のAPI関数も同様に修正
3. エラーメッセージの統一化

### Phase 3: 検証とモニタリング
1. ステージング環境でのテスト実施
2. Sentryでのエラー情報確認
3. 本番環境への段階的適用

## 🚨 注意点

### 1. 既存機能への影響
- **エラーハンドリングの変更**: 既存のエラー処理ロジックに影響する可能性
- **Sentryログの変化**: エラーメッセージ形式が変更される
- **デバッグ情報**: より詳細な情報が記録されるため、ログ量が増加する可能性

### 2. パフォーマンスへの影響
- **エラー処理のオーバーヘッド**: わずかな処理時間の増加
- **Sentryイベント数**: 適切なフィルタリングにより、むしろ削減される見込み

### 3. 運用上の考慮事項
- **ログ監視**: 新しいエラー形式に対応した監視設定
- **アラート設定**: Supabaseエラーに対する適切なアラート設定
- **ドキュメント更新**: エラーハンドリングガイドラインの更新

## 📝 推奨アクション

### 即座に実施
1. **グローバルエラーハンドラーの修正**: Supabaseエラー処理の改善
2. **テスト実施**: ステージング環境での動作確認
3. **監視設定**: 新しいエラー形式に対応した監視

### 段階的に実施
1. **API関数の改善**: 全てのSupabase API呼び出しの見直し
2. **エラーメッセージの統一**: 一貫したエラーメッセージ形式の適用
3. **ドキュメント整備**: エラーハンドリングガイドラインの作成

### 長期的な改善
1. **エラー処理の標準化**: プロジェクト全体でのエラーハンドリング統一
2. **自動テストの追加**: エラーケースのテストカバレッジ向上
3. **監視ダッシュボードの改善**: Supabaseエラーの可視化

## 📋 関連Issue

- **Issue 6700459048**: Sentry UI内部エラー（修正済み）
- **Issue 6702984347**: テストメッセージ問題（修正済み）
- **Issue 6703440058**: 環境変数未定義エラー（修正済み）

## 📈 結論

**Issue 6703393345は、Supabaseエラーオブジェクトの不適切な処理が原因です。**

- **根本原因**: グローバルエラーハンドラーでのSupabaseエラー処理不備
- **影響度**: 中（エラー監視の精度に影響）
- **修正方針**: エラーハンドリングの改善とフィルタリング強化
- **優先度**: 中（監視精度向上のため早期対応推奨）

この修正により、より具体的で有用なエラー情報がSentryに記録され、問題の早期発見と迅速な対応が可能になります。

---

**分析日**: 2025-06-25  
**担当者**: Cline AI Assistant  
**ステータス**: 分析完了・修正方針確定  
**分類**: Supabaseエラーハンドリング問題（アプリケーション内部要因）
