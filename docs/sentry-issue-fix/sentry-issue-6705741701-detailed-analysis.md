# Sentry Issue 6705741701 詳細調査レポート

## 🔍 詳細調査結果

### 📊 重要な発見

#### 1. Next.js設定の問題発見
`next.config.js`で環境変数を`env`セクションで再定義している：

```javascript
env: {
  // Ensure Supabase environment variables are available at build time
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
  NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,
  // ...
}
```

**これが問題の根本原因の可能性が高い**

#### 2. 初期化順序の問題
1. `instrumentation.ts`でSentryが初期化される
2. `lib/supabase.ts`で環境変数検証が実行される
3. この時点で環境変数がまだ利用できない可能性

#### 3. ビルド時の環境変数アクセス
- Next.js 14.2.30使用
- `NEXT_PUBLIC_`プレフィックスの環境変数はビルド時に埋め込まれる
- サーバーサイドでの初期化時にアクセス順序の問題

## 🚨 根本原因の特定

### 問題1: Next.js環境変数の二重定義
`next.config.js`の`env`セクションで環境変数を再定義することで、以下の問題が発生：

1. **ビルド時の評価**: `process.env.NEXT_PUBLIC_SUPABASE_URL`がビルド時に評価される
2. **undefined値の固定化**: ビルド時に環境変数が利用できない場合、`undefined`が固定される
3. **ランタイムでの上書き不可**: 一度`undefined`で固定されると、ランタイムで変更できない

### 問題2: 初期化タイミング
```
1. Next.js起動
2. instrumentation.ts実行
3. Sentry初期化
4. lib/supabase.ts読み込み
5. validateEnvironmentVariables()実行 ← ここでエラー
6. グローバルエラーハンドラーがキャッチ
7. Sentryに送信
```

## 🔧 修正方針

### 1. 【高優先度】next.config.jsの修正
環境変数の二重定義を削除：

```javascript
// ❌ 削除すべき部分
env: {
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
  NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,
  // ...
}

// ✅ 修正後（削除）
// env セクション自体を削除または必要最小限に
```

### 2. 【中優先度】環境変数検証の改善
より堅牢な検証ロジック：

```typescript
// lib/supabase.ts - 改善版
function validateEnvironmentVariables() {
  // ビルド時とランタイムの区別
  const isBuildTime = process.env.NODE_ENV === undefined || process.env.NEXT_PHASE === 'phase-production-build';
  
  if (isBuildTime) {
    console.log('🏗️ Build time - skipping environment validation');
    return {
      supabaseUrl: 'build-time-placeholder',
      supabaseAnonKey: 'build-time-placeholder',
      serviceRoleKey: undefined
    };
  }
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  console.log('🔍 Runtime Environment Variables Check:');
  console.log('- NODE_ENV:', process.env.NODE_ENV);
  console.log('- VERCEL_ENV:', process.env.VERCEL_ENV);
  console.log('- NEXT_PHASE:', process.env.NEXT_PHASE);
  console.log('- Runtime:', typeof window !== 'undefined' ? 'client' : 'server');
  
  // 詳細なデバッグ情報
  if (process.env.NODE_ENV !== 'production') {
    console.log('- Available SUPABASE env vars:', 
      Object.keys(process.env)
        .filter(key => key.includes('SUPABASE'))
        .map(key => `${key}: ${process.env[key] ? 'SET' : 'NOT_SET'}`)
    );
  }
  
  if (!supabaseUrl) {
    const errorMessage = `NEXT_PUBLIC_SUPABASE_URL is not defined.
      Environment: ${process.env.NODE_ENV}
      Vercel Env: ${process.env.VERCEL_ENV}
      Next Phase: ${process.env.NEXT_PHASE}
      Runtime: ${typeof window !== 'undefined' ? 'client' : 'server'}
      Timestamp: ${new Date().toISOString()}`;
    
    console.error('❌', errorMessage);
    throw new Error(errorMessage);
  }
  
  // 残りの検証...
  return { supabaseUrl, supabaseAnonKey, serviceRoleKey };
}
```

### 3. 【中優先度】遅延初期化の実装
環境変数が確実に利用可能になってから初期化：

```typescript
// lib/supabase.ts - 遅延初期化
let supabaseClient: any = null;
let initPromise: Promise<any> | null = null;

export function getSupabaseClient() {
  if (supabaseClient) {
    return supabaseClient;
  }
  
  if (!initPromise) {
    initPromise = initializeSupabase();
  }
  
  return initPromise;
}

async function initializeSupabase() {
  // 環境変数が利用可能になるまで待機
  let retries = 0;
  const maxRetries = 3;
  
  while (retries < maxRetries) {
    try {
      const { supabaseUrl, supabaseAnonKey } = validateEnvironmentVariables();
      
      if (supabaseUrl === 'build-time-placeholder') {
        throw new Error('Build time placeholder detected');
      }
      
      supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
        // 設定...
      });
      
      console.log('✅ Supabase client initialized successfully');
      return supabaseClient;
    } catch (error) {
      retries++;
      if (retries >= maxRetries) {
        console.error('❌ Failed to initialize Supabase after retries:', error);
        throw error;
      }
      
      console.log(`⏳ Retrying Supabase initialization (${retries}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}
```

## 📊 修正の優先度と影響

### 即座の修正（高優先度）
1. **next.config.jsのenv削除**: 根本原因の解決
2. **影響**: ビルド時の環境変数固定化を防ぐ
3. **リスク**: 低（既存機能への影響なし）

### 短期修正（中優先度）
1. **環境変数検証の改善**: より詳細なデバッグ情報
2. **影響**: 問題特定の容易化
3. **リスク**: 低

### 長期修正（低優先度）
1. **遅延初期化**: 堅牢性の向上
2. **影響**: システム全体の安定性向上
3. **リスク**: 中（既存コードの変更が必要）

## 🧪 検証方法

### 1. ローカル検証
```bash
# 1. next.config.js修正後
npm run build

# 2. 環境変数確認
npm run check:env

# 3. ビルド成功確認
npm run start
```

### 2. Vercelデプロイ検証
```bash
# 1. 修正をデプロイ
git add next.config.js
git commit -m "Fix environment variable double definition in next.config.js"
git push

# 2. デプロイログ確認
vercel logs --since=now-10m

# 3. Sentryエラー監視
# 同様のエラーが発生しないことを確認
```

## 📝 予想される結果

### 修正前
- ビルド時に`process.env.NEXT_PUBLIC_SUPABASE_URL`が`undefined`
- `next.config.js`の`env`で`undefined`が固定化
- ランタイムで環境変数が利用できずエラー

### 修正後
- ビルド時の環境変数固定化を回避
- ランタイムで正しい環境変数にアクセス可能
- Sentry Issue 6705741701の再発防止

---

**作成日**: 2025-06-25  
**分析者**: Cline AI Assistant  
**ステータス**: 根本原因特定完了・修正方針確定
