# Sentry Issue 6705741701 - Vercel環境変数分析

## 📊 Vercel設定確認結果

### ✅ 設定済み環境変数
- **NEXT_PUBLIC_SUPABASE_URL**: Production/Preview両方に設定済み
- **NEXT_PUBLIC_SUPABASE_ANON_KEY**: Production/Preview両方に設定済み
- **SUPABASE_SERVICE_ROLE_KEY**: Production/Preview両方に設定済み

### 🔍 問題分析

環境変数は正しく設定されているが、2025-06-24 14:18:34にエラーが発生。

## 🚨 推定される問題

### 1. デプロイタイミング問題
- ビルド開始時の環境変数読み込みタイミング
- 新しいデプロイでの一時的な環境変数アクセス失敗

### 2. 環境変数値の検証が必要
以下の確認を推奨：

```bash
# Vercel CLIでの確認
vercel env ls

# 環境変数の値確認（マスクされた状態）
vercel env pull .env.vercel
```

### 3. URL形式の検証
NEXT_PUBLIC_SUPABASE_URLの形式確認：
- ✅ 正しい形式: `https://your-project.supabase.co`
- ❌ 間違った形式: `https://your-project.supabase.co/` (末尾スラッシュ)
- ❌ 間違った形式: `your-project.supabase.co` (プロトコル不備)

## 🔧 推奨する修正手順

### 1. 環境変数値の再確認
```bash
# 1. 現在の環境変数をローカルに取得
vercel env pull .env.vercel

# 2. 値の確認（機密情報に注意）
cat .env.vercel | grep SUPABASE
```

### 2. URL形式の検証
```javascript
// lib/supabase.ts - URL検証の強化
function validateEnvironmentVariables() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  console.log('🔍 Environment Variables Validation:');
  console.log('- NODE_ENV:', process.env.NODE_ENV);
  console.log('- VERCEL_ENV:', process.env.VERCEL_ENV);
  console.log('- URL exists:', !!supabaseUrl);
  console.log('- URL length:', supabaseUrl?.length || 0);
  console.log('- Key exists:', !!supabaseAnonKey);
  console.log('- Key length:', supabaseAnonKey?.length || 0);
  
  if (!supabaseUrl) {
    const errorMessage = `NEXT_PUBLIC_SUPABASE_URL is not defined.
      Environment: ${process.env.NODE_ENV}
      Vercel Env: ${process.env.VERCEL_ENV}
      Timestamp: ${new Date().toISOString()}`;
    console.error('❌', errorMessage);
    throw new Error(errorMessage);
  }
  
  // URL形式の詳細検証
  try {
    const url = new URL(supabaseUrl);
    console.log('✅ URL validation passed:', {
      protocol: url.protocol,
      hostname: url.hostname,
      pathname: url.pathname
    });
    
    // Supabase URL形式の検証
    if (!url.hostname.includes('supabase.co')) {
      throw new Error(`Invalid Supabase URL hostname: ${url.hostname}`);
    }
    
    // 末尾スラッシュの警告
    if (url.pathname !== '/') {
      console.warn('⚠️ Supabase URL should not have trailing path:', url.pathname);
    }
    
  } catch (urlError) {
    const errorMessage = `Invalid NEXT_PUBLIC_SUPABASE_URL format: ${supabaseUrl}`;
    console.error('❌', errorMessage, urlError);
    throw new Error(errorMessage);
  }
  
  // 残りの検証...
}
```

### 3. デプロイ時の環境変数確認
```javascript
// デプロイ時の詳細ログ追加
if (process.env.VERCEL_ENV) {
  console.log('🚀 Vercel Deployment Info:');
  console.log('- VERCEL_ENV:', process.env.VERCEL_ENV);
  console.log('- VERCEL_URL:', process.env.VERCEL_URL);
  console.log('- VERCEL_REGION:', process.env.VERCEL_REGION);
  console.log('- Available env vars:', Object.keys(process.env).filter(k => k.includes('SUPABASE')));
}
```

## 🎯 次のアクション

### 即座の対応
1. **環境変数値の確認**: Vercel CLIで実際の値を確認
2. **URL形式の検証**: 末尾スラッシュや形式の確認
3. **デプロイログの確認**: 該当時刻のデプロイログを確認

### 予防策
1. **環境変数検証の強化**: より詳細なバリデーション追加
2. **デプロイ時ログの充実**: 問題特定のための情報追加
3. **リトライ機能**: 一時的な失敗に対する再試行機能

## 📝 確認コマンド

```bash
# 1. 環境変数の確認
vercel env ls

# 2. 特定環境の環境変数取得
vercel env pull .env.production --environment=production
vercel env pull .env.preview --environment=preview

# 3. デプロイログの確認
vercel logs --since=2025-06-24T14:00:00Z --until=2025-06-24T15:00:00Z
```

---

**作成日**: 2025-06-25  
**分析者**: Cline AI Assistant  
**ステータス**: Vercel設定確認完了・詳細調査が必要
