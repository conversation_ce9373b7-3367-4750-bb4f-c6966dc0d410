# Sentry Issue 6704437237 修正レポート

## 📊 Issue概要
- **Issue ID**: 6704437237
- **タイトル**: `Error: Cannot attach interceptor to iframe (cross-origin?): TypeError: null is not an object (evaluating 'iframeElement.contentWindow.document')`
- **発生場所**: `/` (ホームページ)
- **発生回数**: 6回
- **発生期間**: 2025-06-24 02:27:45 〜 12:27:29（約10時間）
- **影響ユーザー**: 1名
- **優先度**: Medium
- **レベル**: Warning
- **ステータス**: Unresolved (new)

## 🔍 問題分析

### エラーの詳細
```
Error: Cannot attach interceptor to iframe (cross-origin?): 
TypeError: null is not an object (evaluating 'iframeElement.contentWindow.document')
```

### 発生パターンの特徴
1. **ホームページでの発生**: ルートパス（`/`）での発生
2. **iframe関連エラー**: クロスオリジンiframeへのアクセス試行
3. **単一ユーザー**: 1名のユーザーによる6回の発生
4. **長時間分散**: 約10時間にわたって断続的に発生

### 推定される原因
1. **自動テストツール**: Playwright、Selenium等のiframe操作
2. **ブラウザ拡張機能**: 広告ブロッカー、セキュリティ拡張機能
3. **埋め込みコンテンツ**: 外部サービスのiframe（YouTube、Twitter等）
4. **クロスオリジン制約**: Same-Origin Policyによるアクセス制限

## 🔧 技術的分析

### iframe操作の問題点
```javascript
// 問題のあるコード例
try {
  const iframeDoc = iframeElement.contentWindow.document;
  // クロスオリジンの場合、contentWindow.documentはnullになる
} catch (error) {
  // SecurityError: Blocked a frame with origin "..." from accessing a cross-origin frame
}
```

### クロスオリジン制約
- **Same-Origin Policy**: 異なるオリジンのiframeへのアクセスは制限される
- **contentWindow.document**: クロスオリジンの場合はnullまたはアクセス不可
- **セキュリティ制約**: ブラウザによる意図的な制限

## 🔧 推奨する修正方針

### 1. iframe操作の安全性チェック強化
iframe操作前の安全性確認を追加：

```typescript
// lib/utils/iframe-utils.ts
const canAccessIframe = (iframe: HTMLIFrameElement): boolean => {
  try {
    // クロスオリジンチェック
    const iframeSrc = iframe.src;
    const currentOrigin = window.location.origin;
    
    if (iframeSrc && !iframeSrc.startsWith(currentOrigin)) {
      return false; // クロスオリジン
    }
    
    // contentWindowアクセステスト
    return iframe.contentWindow?.document !== null;
  } catch (error) {
    return false; // アクセス不可
  }
};

const safeIframeOperation = (iframe: HTMLIFrameElement, operation: () => void) => {
  if (canAccessIframe(iframe)) {
    try {
      operation();
    } catch (error) {
      console.warn('Iframe operation failed:', error);
    }
  } else {
    console.warn('Cannot access cross-origin iframe');
  }
};
```

### 2. グローバルエラーハンドラーでのフィルタリング
自動ツール・拡張機能由来のエラーをフィルタリング：

```typescript
// lib/monitoring/global-error-handler.ts
const shouldFilterError = (error: Error): boolean => {
  const message = error.message || '';
  
  // iframe関連エラーのフィルタリング
  const iframeErrors = [
    'Cannot attach interceptor to iframe',
    'cross-origin',
    'contentWindow.document',
    'Blocked a frame with origin',
    'Permission denied to access property'
  ];
  
  return iframeErrors.some(pattern => message.includes(pattern));
};
```

### 3. 外部iframe埋め込みの安全な実装
外部コンテンツ埋め込み時の安全な実装：

```typescript
// components/common/SafeIframe.tsx
interface SafeIframeProps {
  src: string;
  title: string;
  allowFullScreen?: boolean;
}

const SafeIframe: React.FC<SafeIframeProps> = ({ src, title, allowFullScreen }) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  
  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe) return;
    
    // クロスオリジンiframeには操作を試行しない
    const handleLoad = () => {
      if (!canAccessIframe(iframe)) {
        console.log('Cross-origin iframe loaded safely');
        return;
      }
      
      // 同一オリジンの場合のみ操作実行
      safeIframeOperation(iframe, () => {
        // iframe内部の操作
      });
    };
    
    iframe.addEventListener('load', handleLoad);
    return () => iframe.removeEventListener('load', handleLoad);
  }, []);
  
  return (
    <iframe
      ref={iframeRef}
      src={src}
      title={title}
      allowFullScreen={allowFullScreen}
      sandbox="allow-scripts allow-same-origin allow-popups"
      loading="lazy"
    />
  );
};
```

## 📊 期待される効果

### 1. エラー削減
- クロスオリジンiframe関連エラーの大幅削減
- 自動ツール由来のエラー報告削減

### 2. セキュリティ向上
- iframe操作の安全性確保
- クロスオリジン攻撃の防止

### 3. 監視精度向上
- 実際の問題のみを監視対象に
- デバッグ効率の向上

## 🎯 実装優先度

### 高優先度
1. **グローバルエラーハンドラーでのフィルタリング追加**
   - 即座にSentryノイズを削減
   - 実装コスト: 低

### 中優先度
2. **iframe操作の安全性チェック実装**
   - 根本的な問題解決
   - 実装コスト: 中

### 低優先度
3. **SafeIframeコンポーネントの実装**
   - 将来的な拡張性向上
   - 実装コスト: 高

## 📝 監視ポイント

### 修正後の確認事項
1. **エラー発生頻度の変化**
   - Issue 6704437237の再発確認
   - 類似iframe関連エラーの監視

2. **外部コンテンツの動作**
   - 埋め込みコンテンツの正常表示
   - ユーザー体験への影響確認

3. **自動テスト環境での動作**
   - Playwright MCP Serverでのiframe操作
   - テスト実行時のエラー状況

## 🔄 次のアクション

1. **即座の対応**: グローバルエラーハンドラーでのiframe関連エラーフィルタリング
2. **中期対応**: iframe操作の安全性チェック実装
3. **長期対応**: 外部コンテンツ埋め込みの標準化

## 📋 関連する可能性のあるコード箇所

### 確認が必要なファイル
- `pages/index.tsx` (ホームページ)
- `components/` 内のiframe使用コンポーネント
- 外部サービス埋め込み関連コード
- 自動テスト関連スクリプト

---

**作成日**: 2025-06-25  
**分析者**: Cline AI Assistant  
**ステータス**: 分析完了・修正方針策定済み
