# StudyShare Platform Development

## Project Overview
Build a user membership-based study sharing platform called "StudyShare" where users can share study recipes, track their learning progress, and engage with the community.

## Core Features

### 1. Study Recipe System
Create a **feed-based interface** where users can post structured study plans with the following fields:
- **Recipe Title**: Target objective and title combined (e.g., "JLPT N4 Preparation Plan - Pass JLPT N4 exam in 3 months")
- **Tags**: Categorization labels
- **Estimated Duration**: Expected time to achieve goal
- **Study Materials**: Multiple materials can be added (similar to Cookpad recipe ingredients)
  - **Material Name**: Study resource name
  - **Frequency**: How often to study with this material (e.g., "2 times per week")
  - **Time**: Duration per session (e.g., "10 minutes")
  - **Unit**: Time measurement unit
- **User's Tips/Notes**: Personal advice and observations

**Study Materials Interface Requirements:**
- "Add Material" button to dynamically add multiple study materials
- Each material entry should be removable/editable
- Drag-and-drop reordering of materials (optional)
- Similar to Cookpad's ingredient addition interface
- Example: Material 1: "Grammar Book A" - 3x/week - 15 minutes, Material 2: "Listening App B" - daily - 5 minutes

**Feed Display Requirements:**
- Card-based layout for easy browsing
- Infinite scroll or pagination for better performance
- Filtering options (tags, popularity, recent)
- Search functionality within the feed

### 2. Study Log System
Implement a **feed-based progress tracking feature** that allows users to:
- Select from their posted study recipes or bookmarked recipes
- Choose between two timer modes:
  - **Countdown Timer**: When study duration is predetermined
  - **Stopwatch Timer**: When study duration is flexible
- **Notes Section**: For reflections, comments, and observations
- Link each log entry to a specific study recipe

**Feed Display Requirements:**
- Timeline-style layout showing chronological study progress
- Filter by user, recipe type, or time period
- Visual progress indicators and achievements
- Quick actions for reactions and comments

**Home Page Integration:**
- Main page (Home) should have tab switching functionality
- Tabs: "Study Recipes" and "Study Logs" timeline
- Users can view both their own and community study logs on the Home timeline
- Separate "My Logs" section in user profile for personal tracking

### 3. Voting System (Upvote)
- Users can upvote study recipes only (not study logs)
- Popular recipes appear at the top
- Implement filtering options: "Latest" and "Trending"
- Similar to ProductHunt's voting mechanism

**Upvote Button Design Requirements:**
- Clear visual indication that it's an "Upvote" button (not just a generic like/heart)
- Display upvote count prominently next to the button
- Use ProductHunt-style design: upward arrow icon + "Upvote" text + count
- Visual feedback when clicked (color change, animation)
- Disabled state for users who already upvoted
- Example: "▲ Upvote 23" or "↑ Upvote 15"

### 4. Bookmark System
- Users can bookmark study recipes for future reference
- Bookmarks are private to each user
- Accessible from user profile

### 5. Comment System
- Available on both study recipes and study logs
- Support user mentions (@ functionality) with autocomplete
- Threaded comment structure with reply functionality
- **Reply System**: Users can reply directly to specific comments
- **User Mentions**: @username tagging with notification system
- **Comment Threading**: Nested replies up to 3 levels deep
- Real-time comment updates using Supabase subscriptions

### 6. Reaction System
- Available only on study logs
- Emoji-based reactions (similar to Facebook/Slack)
- Multiple reaction types

### 7. User Authentication & Profile
**Authentication Requirements (Supabase Auth):**
- User registration and login using Supabase Auth
- Social login options (Google, GitHub, etc.)
- Secure password handling and session management
- Email verification and password reset

**Profile Features:**
- Display user's posted study recipes and study logs
- Show upvoted content
- Private bookmark section (only visible to owner)
- Self-introduction section
- Unique @username identifier
- Portfolio view of study progress based on study logs
- Achievement/progress visualization

### 8. Ranking System
Implement a level-up mechanism based on:
- Study log frequency and duration
- Quality and popularity of posted study recipes
- Community engagement (comments received, upvotes)
- Consistent study habits
- Algorithm should encourage both learning and community participation

## Technical Requirements

### Technology Stack
- **Database**: Supabase (PostgreSQL with real-time capabilities)
- **Authentication**: Supabase Auth (built-in user management and social logins)
- **Hosting**: Vercel (with automatic deployments and serverless functions)
- **Frontend**: Next.js/React (recommended for Vercel integration)
- **API**: Supabase REST API / GraphQL
- **Real-time**: Supabase Real-time subscriptions for live updates
- **Storage**: Supabase Storage (for user avatars and attachments)

**Real-time Features Implementation:**
- **Live Voting Updates**: Upvote counts update instantly across all users
- **Real-time Comments**: New comments appear immediately without page refresh
- **Live Reactions**: Emoji reactions on study logs update in real-time
- **Push Notifications**: Live notification system for mentions, comments, and interactions
- **Activity Feed**: Real-time updates on user activity and community engagement

### Design Guidelines
- **Color Scheme**: Black and white monochrome design
- **UX Principle**: Simple, intuitive interface accessible to all users
- **Language**: English system language throughout
- **UI Layout**: Feed-based interface for Study Recipes and Study Logs display
  - Infinite scroll or pagination for better performance
  - Card-based design for easy scanning
  - Chronological or popularity-based sorting options

### Development Standards
- **Comments**: All code comments must be in English
- **Documentation**: Maintain development progress log in markdown format
- **Logging**: Include timestamps for all development milestones
- **Version Control**: Track all changes with meaningful commit messages

## Implementation Phases

### Phase 1: Supabase & Vercel Setup
1. **Supabase Project Setup**
   - Create new Supabase project
   - Configure database schema and RLS policies
   - Set up Supabase Auth with social providers
   - Configure real-time subscriptions

2. **Vercel Deployment Setup**
   - Connect GitHub repository to Vercel
   - Configure environment variables
   - Set up automatic deployments
   - Configure custom domain (optional)

3. **Basic Infrastructure**
   - Next.js project setup with Supabase integration
   - Authentication flow implementation
   - Basic user profile system

### Phase 2: Content Management with Feed UI
1. **Study Recipe Feed**
   - Create feed-based study recipe display
   - Implement infinite scroll/pagination
   - Add filtering and search functionality
   - CRUD operations for study recipes

2. **Study Log Feed**
   - Timeline-style study log display
   - Timer functionality integration
   - Progress tracking visualization
   - CRUD operations for study logs

### Phase 3: Social Features
1. Voting system implementation
2. Comment system with mentions
3. Bookmark functionality
4. Reaction system for study logs

### Phase 4: Gamification & Analytics
1. Ranking algorithm development
2. User progress visualization
3. Achievement system
4. Analytics dashboard

### Phase 5: Polish & Optimization
1. UI/UX refinement
2. Performance optimization
3. Mobile responsiveness
4. Final testing and debugging

## Supabase Database Schema

### Users Table (extends Supabase auth.users)
```sql
CREATE TABLE public.profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  username TEXT UNIQUE NOT NULL,
  display_name TEXT,
  bio TEXT,
  avatar_url TEXT,
  rank_level INTEGER DEFAULT 1,
  total_study_time INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Study Recipes Table
```sql
CREATE TABLE public.study_recipes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) NOT NULL,
  title TEXT NOT NULL,
  estimated_duration TEXT,
  tips TEXT,
  tags TEXT[] DEFAULT '{}',
  upvote_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Study Materials Table (for multiple materials per recipe)
CREATE TABLE public.study_materials (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  recipe_id UUID REFERENCES public.study_recipes(id) ON DELETE CASCADE,
  material_name TEXT NOT NULL,
  frequency TEXT NOT NULL,
  time_per_session INTEGER NOT NULL,
  time_unit TEXT DEFAULT 'minutes',
  order_index INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Study Logs Table
```sql
CREATE TABLE public.study_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) NOT NULL,
  recipe_id UUID REFERENCES public.study_recipes(id) NOT NULL,
  duration_minutes INTEGER NOT NULL,
  notes TEXT,
  timer_type TEXT CHECK (timer_type IN ('countdown', 'stopwatch')),
  completed_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Additional Tables
```sql
-- Votes table
CREATE TABLE public.votes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) NOT NULL,
  recipe_id UUID REFERENCES public.study_recipes(id) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, recipe_id)
);

-- Bookmarks table
CREATE TABLE public.bookmarks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) NOT NULL,
  recipe_id UUID REFERENCES public.study_recipes(id) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, recipe_id)
);

-- Comments table
CREATE TABLE public.comments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) NOT NULL,
  recipe_id UUID REFERENCES public.study_recipes(id),
  log_id UUID REFERENCES public.study_logs(id),
  content TEXT NOT NULL,
  mentions UUID[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  CHECK ((recipe_id IS NOT NULL) OR (log_id IS NOT NULL))
);

-- Reactions table
CREATE TABLE public.reactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) NOT NULL,
  log_id UUID REFERENCES public.study_logs(id) NOT NULL,
  emoji TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, log_id, emoji)
);
```

## Vercel Configuration

### Environment Variables
Set up the following environment variables in Vercel dashboard:
```
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### Deployment Settings
- **Framework Preset**: Next.js
- **Build Command**: `npm run build`
- **Output Directory**: `.next`
- **Install Command**: `npm install`
- **Node.js Version**: 18.x or later

## Success Metrics
- User engagement rates
- Study consistency tracking
- Community interaction levels
- Recipe sharing and usage
- Platform retention rates

## Project Documentation Requirements

### Development Memories Template
Create a memories file to record important decisions, insights, and key implementation details:

```markdown
# StudyShare Development Memories

## [YYYY-MM-DD] - Memory Title
### Key Decision/Insight:
-

### Implementation Details:
-

### Lessons Learned:
-

### Important Notes:
-

### Future Considerations:
-

---
```

### Task Management (Progress Tracking)
Create a separate task management file in markdown format **in Japanese language**:

```markdown
# StudyShare タスク管理・進捗状況

## 現在の進捗状況
### 完了済みタスク
- [ ] タスク名1 - 完了日: YYYY/MM/DD
- [ ] タスク名2 - 完了日: YYYY/MM/DD

### 進行中タスク
- [ ] タスク名 - 開始日: YYYY/MM/DD - 予定終了日: YYYY/MM/DD
  - 詳細:
  - 進捗率: XX%
  - 課題・問題点:

### 未着手タスク（優先度順）
1. [ ] 高優先度タスク
   - 予定開始日: YYYY/MM/DD
   - 見積もり工数: X日
   - 依存関係:

## フェーズ別進捗
### Phase 1: Supabase & Vercel セットアップ
- 進捗: XX%
- 完了予定: YYYY/MM/DD

### Phase 2: Feed UI実装
- 進捗: XX%
- 完了予定: YYYY/MM/DD

## 課題・リスク管理
### 現在の課題
- 課題内容:
- 影響度: 高/中/低
- 対応策:
- 期限:

---
更新日: YYYY/MM/DD
```

Please implement this system following these specifications, utilizing Supabase for backend services and Vercel for hosting, with a focus on feed-based UI design for optimal user experience.
