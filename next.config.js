const path = require('path')
const { withSentryConfig } = require('@sentry/nextjs')

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: [
      // Local development
      '127.0.0.1',
      'localhost',
      // Supabase project domains
      '[PROD_PROJECT_ID].supabase.co', // Production
      '[STAGING_PROJECT_ID].supabase.co', // Staging
    ],
  },
  // Ensure compatibility with Node.js 18
  swcMinify: true,

  // Environment-specific configuration
  env: {
    CUSTOM_KEY: process.env.NODE_ENV,
    APP_ENV: process.env.NEXT_PUBLIC_APP_ENV || process.env.NODE_ENV,
    // Note: NEXT_PUBLIC_ prefixed variables are automatically available in the browser
    // and don't need to be redefined here. Removing Supabase env vars to prevent
    // build-time undefined value fixation (fixes Sentry Issue 6705741701)
    
    // Debug and monitoring (non-NEXT_PUBLIC_ variables only)
    ENABLE_TEST_FEATURES: process.env.ENABLE_TEST_FEATURES,
  },

  // Enable experimental features for development
  experimental: {
    // Add experimental features if needed
  },

  // Custom webpack config to exclude test files
  webpack: (config, { isServer, dev }) => {
    // Add path alias resolution for @ symbol
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(__dirname),
    }

    // Ignore test files during build
    if (!dev) {
      config.module.rules.push({
        test: /\.(spec|test)\.(js|ts|tsx)$/,
        loader: 'ignore-loader'
      })
    }

    return config
  },
}

// Sentry configuration with enhanced options
const sentryWebpackPluginOptions = {
  // Organization and project settings
  org: "forple",
  project: "forple-stg",

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
  // This can increase your server load as well as your hosting bill.
  tunnelRoute: "/monitoring",

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Enables automatic instrumentation of Vercel Cron Monitors
  automaticVercelMonitors: true,

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/
}

// Make sure adding Sentry options is the last code to run before exporting
// Disable Sentry webpack plugin in development environment
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 Sentry Webpack Plugin: Disabled in development environment')
  module.exports = nextConfig
} else {
  module.exports = withSentryConfig(nextConfig, sentryWebpackPluginOptions)
}
