import Head from 'next/head'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import AuthModal from '../../components/auth/AuthModal'
import UserMenu from '../../components/layout/UserMenu'
import ProfileEditModal from '../../components/profile/ProfileEditModal'
import ProfileHeader from '../../components/profile/ProfileHeader'
import ProfileTabs from '../../components/profile/ProfileTabs'
import { SimpleProgressSection } from '../../components/profile/SimpleProgressSection'
import UserBookmarks from '../../components/profile/UserBookmarks'
import UserLogs from '../../components/profile/UserLogs'
import UserRecipes from '../../components/profile/UserRecipes'
import UserUpvotes from '../../components/profile/UserUpvotes'
import { fetchProfileByUsername, fetchUserBookmarkedRecipes, fetchUserLogs, fetchUserRecipes, fetchUserUpvotedRecipes } from '../../lib/api/profiles'
import { RecipeWithMaterials } from '../../lib/api/recipes'
import { StudyLogWithRecipe } from '../../lib/api/studyLogs'
import { useAuth } from '../../lib/auth'
import { Profile } from '../../types'

type TabType = 'recipes' | 'logs' | 'progress' | 'upvoted' | 'bookmarks'

export default function ProfilePage() {
  const router = useRouter()
  const { username } = router.query
  const { user } = useAuth()
  
  // Profile state
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Tab state
  const [activeTab, setActiveTab] = useState<TabType>('recipes')
  
  // Content state
  const [recipes, setRecipes] = useState<RecipeWithMaterials[]>([])
  const [logs, setLogs] = useState<StudyLogWithRecipe[]>([])
  const [upvotedRecipes, setUpvotedRecipes] = useState<RecipeWithMaterials[]>([])
  const [bookmarkedRecipes, setBookmarkedRecipes] = useState<RecipeWithMaterials[]>([])
  
  // Loading states for each tab
  const [recipesLoading, setRecipesLoading] = useState(false)
  const [logsLoading, setLogsLoading] = useState(false)
  const [upvotedLoading, setUpvotedLoading] = useState(false)
  const [bookmarksLoading, setBookmarksLoading] = useState(false)
  
  // Auth modal state
  const [authModalOpen, setAuthModalOpen] = useState(false)
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin')

  // Profile edit modal state
  const [editModalOpen, setEditModalOpen] = useState(false)

  const isOwnProfile = user?.id === profile?.id

  // Load profile data
  useEffect(() => {
    if (username && typeof username === 'string') {
      loadProfile(username)
    }
  }, [username])

  // Load tab content when tab changes
  useEffect(() => {
    if (profile) {
      loadTabContent()
    }
  }, [activeTab, profile])

  const loadProfile = async (usernameParam: string) => {
    try {
      setLoading(true)
      setError(null)
      
      const profileData = await fetchProfileByUsername(usernameParam)
      
      if (!profileData) {
        setError('User not found')
        return
      }
      
      setProfile(profileData)
    } catch (err) {
      console.error('Error loading profile:', err)
      setError('Failed to load profile')
    } finally {
      setLoading(false)
    }
  }

  const loadTabContent = async () => {
    if (!profile) return

    try {
      switch (activeTab) {
        case 'recipes':
          setRecipesLoading(true)
          const recipesData = await fetchUserRecipes(profile.id)
          setRecipes(recipesData)
          setRecipesLoading(false)
          break
          
        case 'logs':
          setLogsLoading(true)
          const logsData = await fetchUserLogs(profile.id)
          setLogs(logsData)
          setLogsLoading(false)
          break

        case 'progress':
          // Progress tab doesn't need to load additional data
          break

        case 'upvoted':
          if (isOwnProfile) {
            setUpvotedLoading(true)
            const upvotedData = await fetchUserUpvotedRecipes(profile.id)
            setUpvotedRecipes(upvotedData)
            setUpvotedLoading(false)
          }
          break
          
        case 'bookmarks':
          if (isOwnProfile) {
            setBookmarksLoading(true)
            const bookmarksData = await fetchUserBookmarkedRecipes(profile.id)
            setBookmarkedRecipes(bookmarksData)
            setBookmarksLoading(false)
          }
          break
      }
    } catch (err) {
      console.error(`Error loading ${activeTab}:`, err)
    }
  }

  const handleAuthClick = (mode: 'signin' | 'signup') => {
    setAuthMode(mode)
    setAuthModalOpen(true)
  }

  const handleProfileUpdate = (updatedProfile: Profile) => {
    setProfile(updatedProfile)
    // If username changed, redirect to new URL
    if (updatedProfile.username !== username) {
      router.push(`/profile/${updatedProfile.username}`)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-4"></div>
          <p className="text-gray-600">Loading profile...</p>
        </div>
      </div>
    )
  }

  if (error || !profile) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-black mb-2">Profile Not Found</h1>
          <p className="text-gray-600 mb-4">{error || 'The user you are looking for does not exist.'}</p>
          <button
            onClick={() => router.push('/')}
            className="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
          >
            Go Home
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>{profile.display_name || profile.username} - StudyShare</title>
        <meta name="description" content={`${profile.display_name || profile.username}'s profile on StudyShare`} />
      </Head>

      <main className="min-h-screen bg-white">
        {/* Timeline Container - Twitter-style layout */}
        <div className="timeline-container">
          {/* Header - Sticky */}
          <div className="bg-white sticky top-0 z-50">
            <div className="px-4 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => router.push('/')}
                    className="text-xl hover:bg-gray-100 p-2 rounded-full transition-colors"
                    aria-label="Back to home"
                  >
                    ←
                  </button>
                  <div>
                    <h1 className="text-xl font-bold">
                      {profile?.display_name || profile?.username || 'Profile'}
                    </h1>
                    {profile && (
                      <p className="text-sm text-gray-500">
                        @{profile.username}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  {user ? (
                    <UserMenu />
                  ) : (
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleAuthClick('signin')}
                        className="btn-secondary text-sm px-3 py-1.5"
                      >
                        Sign In
                      </button>
                      <button
                        onClick={() => handleAuthClick('signup')}
                        className="btn-primary text-sm px-3 py-1.5"
                      >
                        Sign Up
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div>
            {/* Profile Header */}
            <ProfileHeader
              profile={profile}
              isOwnProfile={isOwnProfile}
              onEditClick={() => setEditModalOpen(true)}
            />

            {/* Profile Tabs */}
            <ProfileTabs
              activeTab={activeTab}
              onTabChange={setActiveTab}
              isOwnProfile={isOwnProfile}
            />

            {/* Tab Content */}
            <div>
            {activeTab === 'recipes' && (
              <UserRecipes
                recipes={recipes}
                loading={recipesLoading}
                error={null}
                onRefresh={() => loadTabContent()}
                isOwnProfile={isOwnProfile}
                username={profile.username}
              />
            )}

            {activeTab === 'logs' && (
              <UserLogs
                logs={logs}
                loading={logsLoading}
                error={null}
                onRefresh={() => loadTabContent()}
                isOwnProfile={isOwnProfile}
                username={profile.username}
              />
            )}

            {activeTab === 'progress' && (
              <SimpleProgressSection
                userId={profile.id}
                isOwnProfile={isOwnProfile}
              />
            )}

            {activeTab === 'upvoted' && isOwnProfile && (
              <UserUpvotes
                recipes={upvotedRecipes}
                loading={upvotedLoading}
                error={null}
                onRefresh={() => loadTabContent()}
              />
            )}

            {activeTab === 'bookmarks' && isOwnProfile && (
              <UserBookmarks
                recipes={bookmarkedRecipes}
                loading={bookmarksLoading}
                error={null}
                onRefresh={() => loadTabContent()}
              />
            )}
            </div>
          </div>
        </div>

        {/* Profile Edit Modal */}
        {profile && (
          <ProfileEditModal
            isOpen={editModalOpen}
            onClose={() => setEditModalOpen(false)}
            profile={profile}
            onProfileUpdate={handleProfileUpdate}
          />
        )}

        {/* Auth Modal */}
        <AuthModal
          isOpen={authModalOpen}
          onClose={() => setAuthModalOpen(false)}
          initialMode={authMode}
        />
      </main>
    </>
  )
}
