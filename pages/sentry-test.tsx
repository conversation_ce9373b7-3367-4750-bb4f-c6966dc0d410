import Head from 'next/head'
import { useState } from 'react'

export default function SentryTestPage() {
  const [error, setError] = useState<string>('')

  const triggerClientError = () => {
    throw new Error('Test client-side error for Sentry')
  }

  const triggerAsyncError = async () => {
    try {
      throw new Error('Test async error for Sentry')
    } catch (err) {
      setError('Async error triggered - check Sentry dashboard')
      throw err
    }
  }

  return (
    <>
      <Head>
        <title>Sentry Test - StudyShare</title>
        <meta name="description" content="Test page for Sentry error monitoring" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="min-h-screen bg-white">
        {/* Header */}
        <header className="bg-white border-b border-gray-200">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-bold">StudyShare</h1>
              <a 
                href="/"
                className="px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition-colors"
              >
                Back to Home
              </a>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-8 max-w-2xl">
          <h1 className="text-3xl font-bold mb-8">Sentry Test Page</h1>
          
          <div className="space-y-6">
            <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
              <h2 className="text-xl font-semibold mb-3 text-red-800">Test Client-side Error</h2>
              <p className="text-red-700 mb-4">
                This will trigger an immediate JavaScript error that Sentry should capture.
              </p>
              <button 
                onClick={triggerClientError}
                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
              >
                Trigger Client Error
              </button>
            </div>

            <div className="p-6 bg-orange-50 border border-orange-200 rounded-lg">
              <h2 className="text-xl font-semibold mb-3 text-orange-800">Test Async Error</h2>
              <p className="text-orange-700 mb-4">
                This will trigger an async error that Sentry should capture.
              </p>
              <button 
                onClick={triggerAsyncError}
                className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
              >
                Trigger Async Error
              </button>
              {error && <p className="mt-3 text-red-600 font-medium">{error}</p>}
            </div>

            <div className="p-6 bg-green-50 border border-green-200 rounded-lg">
              <h2 className="text-xl font-semibold mb-3 text-green-800">Instructions</h2>
              <ol className="list-decimal list-inside space-y-2 text-green-700">
                <li>Click either button above to trigger a test error</li>
                <li>Check your browser console for error logs</li>
                <li>Go to <strong>Sentry dashboard → Issues</strong> to see the error</li>
                <li>The error should appear within a few seconds</li>
                <li>Verify error details include stack trace and user context</li>
              </ol>
            </div>

            <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h2 className="text-xl font-semibold mb-3 text-blue-800">Expected Results</h2>
              <ul className="list-disc list-inside space-y-2 text-blue-700">
                <li>Errors appear in Sentry Issues dashboard</li>
                <li>Stack traces show exact error location</li>
                <li>Browser and user information is captured</li>
                <li>Error grouping works correctly</li>
                <li>Performance data is collected</li>
              </ul>
            </div>
          </div>
        </div>
      </main>
    </>
  )
}
