import Head from 'next/head'
import { useRouter } from 'next/router'
import { useCallback, useEffect, useState } from 'react'
import AuthModal from '../components/auth/AuthModal'
import RecipeFeed from '../components/feed/RecipeFeed'
import StudyLogFeed from '../components/feed/StudyLogFeed'
import TimelineLayout from '../components/feed/TimelineLayout'
import UserMenu from '../components/layout/UserMenu'
import { fetchStudyRecipes, RecipeWithMaterials } from '../lib/api/recipes'
import { fetchStudyLogs, StudyLogWithRecipe } from '../lib/api/studyLogs'
import { useAuth } from '../lib/auth'


type TabType = 'logs' | 'recipes'

export default function Home() {
  const router = useRouter()
  const { user, loading: authLoading } = useAuth()
  const [activeTab, setActiveTab] = useState<TabType>('logs')

  // Recipe feed state
  const [recipes, setRecipes] = useState<RecipeWithMaterials[]>([])
  const [recipesLoading, setRecipesLoading] = useState(true)
  const [recipesError, setRecipesError] = useState<string | null>(null)
  const [recipeSortBy, setRecipeSortBy] = useState<'latest' | 'trending'>('latest')

  // Study log feed state
  const [studyLogs, setStudyLogs] = useState<StudyLogWithRecipe[]>([])
  const [logsLoading, setLogsLoading] = useState(true)
  const [logsError, setLogsError] = useState<string | null>(null)

  // Modal states
  const [authModalOpen, setAuthModalOpen] = useState(false)
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin')
  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null)

  const loadRecipes = useCallback(async () => {
    try {
      console.log('🔄 HomePage: Starting to load recipes with sortBy:', recipeSortBy)
      setRecipesLoading(true)
      setRecipesError(null)

      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), 30000)
      )

      const fetchedRecipes = await Promise.race([
        fetchStudyRecipes(recipeSortBy),
        timeoutPromise
      ]) as RecipeWithMaterials[]

      console.log('✅ HomePage: Successfully loaded recipes:', fetchedRecipes.length)
      setRecipes(fetchedRecipes)
    } catch (err) {
      console.error('❌ HomePage: Error loading recipes:', err)
      setRecipesError('Failed to load recipes. Please try again.')

      // Send critical homepage errors to Sentry
      if (err && typeof err === 'object' && 'message' in err) {
        const errorMessage = (err as any).message
        if (errorMessage.includes('does not exist') || errorMessage.includes('column') || errorMessage.includes('timeout')) {
          // Import Sentry dynamically to avoid SSR issues
          import('@sentry/nextjs').then(Sentry => {
            Sentry.captureException(err, {
              tags: {
                component: 'HomePage',
                errorType: 'recipe_loading_error'
              },
              extra: {
                function: 'loadRecipes',
                sortBy: recipeSortBy,
                errorMessage
              }
            })
          })
        }
      }
    } finally {
      setRecipesLoading(false)
    }
  }, [recipeSortBy])

  const loadStudyLogs = useCallback(async () => {
    try {
      setLogsLoading(true)
      setLogsError(null)

      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), 30000)
      )

      const fetchedLogs = await Promise.race([
        fetchStudyLogs(),
        timeoutPromise
      ]) as StudyLogWithRecipe[]

      setStudyLogs(fetchedLogs)
    } catch (err) {
      console.error('Error loading study logs:', err)
      setLogsError('Failed to load study logs. Please try again.')
    } finally {
      setLogsLoading(false)
    }
  }, [])

  // URL state management
  useEffect(() => {
    const tab = router.query.tab as string
    if (tab === 'recipes') {
      setActiveTab('recipes')
    } else {
      setActiveTab('logs')
    }
  }, [router.query.tab])

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab)
    router.push(`/?tab=${tab}`, undefined, { shallow: true })
  }

  // Fetch data on component mount and tab change (only after auth is loaded)
  useEffect(() => {
    if (!authLoading) {
      console.log('🔄 HomePage: Auth loaded, user:', !!user, 'tab:', activeTab)

      // Add a small delay to ensure auth state is fully stabilized
      const timer = setTimeout(() => {
        if (activeTab === 'recipes') {
          loadRecipes()
        } else if (activeTab === 'logs') {
          loadStudyLogs()
        }
      }, 100)

      return () => clearTimeout(timer)
    }
  }, [activeTab, authLoading, user, loadRecipes, loadStudyLogs])

  const handleAuthClick = (mode: 'signin' | 'signup') => {
    setAuthMode(mode)
    setAuthModalOpen(true)
  }

  const handleCreateClick = (type: 'log' | 'recipe') => {
    if (type === 'log') {
      router.push('/create/study-log')
    } else {
      router.push('/create/recipe')
    }
  }

  const handleAuthRequired = (action: () => void) => {
    setPendingAction(() => action)
    setAuthModalOpen(true)
  }

  const handleAuthModalClose = () => {
    setAuthModalOpen(false)
    setPendingAction(null)
  }

  const handleAuthSuccess = () => {
    setAuthModalOpen(false)
    if (pendingAction) {
      // Execute the pending action after a short delay to ensure auth state is updated
      setTimeout(() => {
        pendingAction()
        setPendingAction(null)
      }, 100)
    }
  }



  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-4"></div>
          <p className="text-gray-600">Initializing...</p>
          <p className="text-sm text-gray-400 mt-2">Checking authentication status</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>StudyShare - Share Your Learning Journey</title>
        <meta name="description" content="A platform for sharing study recipes and tracking learning progress" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="min-h-screen bg-white">
        {/* Main Content - Twitter-style Timeline */}
        <TimelineLayout>
          {/* Header - Always sticky */}
          <div className="bg-white sticky top-0 z-50">
            <div className="px-4 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">StudyShare</h1>
                <div className="flex items-center space-x-4">
                  {user ? (
                    <UserMenu />
                  ) : (
                    <>
                      <button
                        onClick={() => handleAuthClick('signin')}
                        className="btn-secondary"
                      >
                        Sign In
                      </button>
                      <button
                        onClick={() => handleAuthClick('signup')}
                        className="btn-primary"
                      >
                        Sign Up
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Tab Navigation - Desktop: sticky, Mobile: scrolls */}
          <div className="header-tabs-container bg-white">
            <div className="timeline-tabs">
              {/* Main Tabs */}
              <div className="flex">
                <button
                  onClick={() => handleTabChange('logs')}
                  className={`timeline-tab ${activeTab === 'logs' ? 'active' : ''}`}
                >
                  Study Log
                </button>
                <button
                  onClick={() => handleTabChange('recipes')}
                  className={`timeline-tab ${activeTab === 'recipes' ? 'active' : ''}`}
                >
                  Study Recipe
                </button>
              </div>

              {/* Stylish Toggle Filter for Recipes Tab */}
              {activeTab === 'recipes' && (
                <div className="flex items-center justify-center py-3 border-t border-gray-100">
                  <div className="relative inline-flex bg-gray-100 rounded-full p-1">
                    <button
                      onClick={() => setRecipeSortBy('latest')}
                      className={`relative px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 ${
                        recipeSortBy === 'latest'
                          ? 'bg-white text-black shadow-sm'
                          : 'text-gray-600 hover:text-gray-800'
                      }`}
                    >
                      Latest
                    </button>
                    <button
                      onClick={() => setRecipeSortBy('trending')}
                      className={`relative px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 ${
                        recipeSortBy === 'trending'
                          ? 'bg-white text-black shadow-sm'
                          : 'text-gray-600 hover:text-gray-800'
                      }`}
                    >
                      Trending
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Create Post Section - Not sticky, scrolls with content */}
          {user && (
            <div className="border-t border-b border-gray-100 px-4 py-6">
              {/* Description text - subtle */}
              <p className="text-sm text-gray-500 mb-4 text-center">
                {activeTab === 'logs' 
                  ? 'Share your study progress with the community' 
                  : 'Share your study methods and knowledge'
                }
              </p>
              
              {/* Create button */}
              <div className="flex justify-center">
                <button
                  onClick={() => handleCreateClick(activeTab === 'logs' ? 'log' : 'recipe')}
                  className="bg-black text-white px-6 py-2 rounded-full text-sm font-medium hover:bg-gray-800 transition-colors flex items-center space-x-2"
                >
                  <span className="text-lg">+</span>
                  <span>
                    {activeTab === 'logs' ? 'Post Study Log' : 'Post Study Recipe'}
                  </span>
                </button>
              </div>
            </div>
          )}

          {/* Feed Content */}
          <div>
            {activeTab === 'recipes' && (
              <RecipeFeed
                recipes={recipes}
                loading={recipesLoading}
                error={recipesError}
                onRefresh={loadRecipes}
                sortBy={recipeSortBy}
                onSortChange={setRecipeSortBy}
                onAuthRequired={handleAuthRequired}
              />
            )}

            {activeTab === 'logs' && (
              <StudyLogFeed
                logs={studyLogs}
                loading={logsLoading}
                error={logsError}
                onRefresh={loadStudyLogs}
                onAuthRequired={handleAuthRequired}
              />
            )}
          </div>
        </TimelineLayout>

        {/* Modals */}
        <AuthModal
          isOpen={authModalOpen}
          onClose={handleAuthModalClose}
          initialMode={authMode}
          onSuccess={handleAuthSuccess}
        />
      </main>
    </>
  )
}
