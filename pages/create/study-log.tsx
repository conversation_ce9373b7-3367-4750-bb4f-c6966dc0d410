import Head from 'next/head'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { HiArrowLeft } from 'react-icons/hi2'
import { ActionFeedback, ExperienceGainAnimation } from '../../components/gamification/NotificationSystem'
import { useGamificationActions } from '../../components/providers/GamificationProvider'
import TimerStopwatch from '../../components/timer/TimerStopwatch'
import { getUserBookmarks } from '../../lib/api/bookmarks'
import { RecipeWithMaterials } from '../../lib/api/recipes'
import { createStudyLog } from '../../lib/api/studyLogs'
import { useAuth } from '../../lib/auth'
import { calculateStudyExperience } from '../../lib/gamification/rankingSystem'
import { supabase } from '../../lib/supabase'

export default function CreateStudyLogPage() {
  const router = useRouter()
  const { user, loading: authLoading } = useAuth()
  const { triggerExperienceGain } = useGamificationActions()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Gamification state
  const [showExperienceGain, setShowExperienceGain] = useState(false)
  const [experienceGained, setExperienceGained] = useState(0)
  const [showSuccessFeedback, setShowSuccessFeedback] = useState(false)

  // Form state
  const [selectedRecipeId, setSelectedRecipeId] = useState('')
  const [selectedMaterialId, setSelectedMaterialId] = useState('')
  const [title, setTitle] = useState('')
  const [notes, setNotes] = useState('')
  
  // Duration state - simplified to single seconds value
  const [durationSeconds, setDurationSeconds] = useState(0)

  // Recipe options
  const [availableRecipes, setAvailableRecipes] = useState<(RecipeWithMaterials & { isOwnRecipe: boolean })[]>([])
  const [loadingRecipes, setLoadingRecipes] = useState(false)

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/')
    }
  }, [user, authLoading, router])

  // Load user's recipes
  useEffect(() => {
    if (user) {
      loadAvailableRecipes()
    }
  }, [user])


  const loadAvailableRecipes = async () => {
    setLoadingRecipes(true)
    try {
      // Get user's own recipes
      const { data: userRecipes, error: userRecipesError } = await supabase
        .from('study_recipes')
        .select('*')
        .eq('user_id', user!.id)

      if (userRecipesError) throw userRecipesError

      // Get bookmarked recipe IDs
      let bookmarkedRecipeIds: string[] = []
      try {
        bookmarkedRecipeIds = await getUserBookmarks()
      } catch (bookmarkError) {
        console.warn('Failed to load bookmarks, continuing with user recipes only:', bookmarkError)
      }

      // Get bookmarked recipes if any exist
      let bookmarkedRecipes: any[] = []
      if (bookmarkedRecipeIds.length > 0) {
        const { data: recipes, error: bookmarkedRecipesError } = await supabase
          .from('study_recipes')
          .select('*')
          .in('id', bookmarkedRecipeIds)

        if (bookmarkedRecipesError) {
          console.warn('Failed to load bookmarked recipes:', bookmarkedRecipesError)
        } else {
          bookmarkedRecipes = recipes || []
        }
      }

      // Combine all recipes and remove duplicates
      const allRecipes = [...(userRecipes || []), ...bookmarkedRecipes]
      const uniqueRecipes = allRecipes.filter((recipe, index, self) =>
        index === self.findIndex(r => r.id === recipe.id)
      )

      if (uniqueRecipes.length === 0) {
        setAvailableRecipes([])
        return
      }

      // Get user profiles for all recipe authors
      const allUserIds = [...new Set(uniqueRecipes.map(r => r.user_id))]
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('id, username, display_name')
        .in('id', allUserIds)

      if (profilesError) throw profilesError

      // Get materials for all recipes
      const recipeIds = uniqueRecipes.map(r => r.id)
      const { data: materials, error: materialsError } = await supabase
        .from('study_materials')
        .select('*')
        .in('recipe_id', recipeIds)
        .order('order_index')

      if (materialsError) throw materialsError

      // Combine recipes with author info and materials
      const recipesWithDetails = uniqueRecipes.map(recipe => {
        const profile = profiles?.find(p => p.id === recipe.user_id)
        const recipeMaterials = materials?.filter(m => m.recipe_id === recipe.id) || []
        const isOwnRecipe = recipe.user_id === user!.id

        return {
          ...recipe,
          author: {
            username: profile?.username || 'Unknown',
            display_name: profile?.display_name || null
          },
          materials: recipeMaterials.sort((a, b) => a.order_index - b.order_index),
          isOwnRecipe // Add flag to identify own recipes
        }
      })

      // Sort recipes: own recipes first, then bookmarked recipes
      const sortedRecipes = recipesWithDetails.sort((a, b) => {
        if (a.isOwnRecipe && !b.isOwnRecipe) return -1
        if (!a.isOwnRecipe && b.isOwnRecipe) return 1
        return 0
      })

      setAvailableRecipes(sortedRecipes)
    } catch (error: any) {
      console.error('Error loading recipes:', error)
      setError('Failed to load available recipes')
    } finally {
      setLoadingRecipes(false)
    }
  }

  // Handle timer duration change from TimerStopwatch component
  const handleDurationChange = (totalSeconds: number) => {
    setDurationSeconds(totalSeconds)
  }

  // Convert seconds to hours, minutes, seconds for display
  const getDurationComponents = (totalSeconds: number) => {
    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = totalSeconds % 60
    return { hours, minutes, seconds }
  }

  // Handle manual duration input changes
  const handleManualDurationChange = (field: 'hours' | 'minutes' | 'seconds', value: number) => {
    const { hours, minutes, seconds } = getDurationComponents(durationSeconds)
    
    let newHours = hours
    let newMinutes = minutes
    let newSeconds = seconds

    switch (field) {
      case 'hours':
        newHours = Math.max(0, Math.min(23, value))
        break
      case 'minutes':
        newMinutes = Math.max(0, Math.min(59, value))
        break
      case 'seconds':
        newSeconds = Math.max(0, Math.min(59, value))
        break
    }

    const totalSeconds = (newHours * 3600) + (newMinutes * 60) + newSeconds
    setDurationSeconds(totalSeconds)
  }

  // Generate title based on recipe selection (without material name)
  const generateTitle = (recipeId: string) => {
    const selectedRecipe = availableRecipes.find(recipe => recipe.id === recipeId)
    if (!selectedRecipe) return ''

    if (selectedRecipe.isOwnRecipe) {
      return selectedRecipe.title
    } else {
      return `${selectedRecipe.title} (@${selectedRecipe.author.username})`
    }
  }

  // Handle recipe selection change
  const handleRecipeChange = (recipeId: string) => {
    setSelectedRecipeId(recipeId)
    setSelectedMaterialId('') // Reset material selection when recipe changes
    if (recipeId) {
      const generatedTitle = generateTitle(recipeId)
      setTitle(generatedTitle)
    } else {
      setTitle('')
    }
  }

  // Handle material selection change
  const handleMaterialChange = (materialId: string) => {
    setSelectedMaterialId(materialId)
    // Title doesn't change when material is selected
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user) {
      setError('You must be logged in to create a study log')
      return
    }

    if (!selectedRecipeId) {
      setError('Please select a study recipe')
      return
    }

    // Check if selected recipe has materials and if material is selected
    const selectedRecipe = availableRecipes.find(recipe => recipe.id === selectedRecipeId)
    if (selectedRecipe && selectedRecipe.materials.length > 0 && !selectedMaterialId) {
      setError('Please select a study material')
      return
    }

    if (!title.trim()) {
      setError('Title is required')
      return
    }

    if (durationSeconds <= 0) {
      setError('Please set a valid study duration')
      return
    }

    setLoading(true)
    setError(null)

    try {
      // Use the stored duration seconds directly
      const totalDurationSeconds = durationSeconds

      const logData = {
        recipe_id: selectedRecipeId,
        material_id: selectedMaterialId || null,
        title: title.trim(),
        duration_minutes: Math.max(1, Math.ceil(totalDurationSeconds / 60)), // For compatibility, round up to minutes
        duration_seconds: totalDurationSeconds, // Store precise seconds
        notes: notes.trim() || undefined,
        timer_type: 'stopwatch' as const,
        // completed_at will be set automatically by the database default (NOW())
      }

      console.log('📝 Submitting study log data:', logData)

      const result = await createStudyLog(logData)
      
      if (!result) {
        throw new Error('Failed to create study log')
      }

      // Calculate and show experience gained (convert to total minutes for XP calculation)
      const totalMinutes = Math.max(1, Math.ceil(totalDurationSeconds / 60))
      const xpGained = calculateStudyExperience(totalMinutes)
      setExperienceGained(xpGained)
      setShowExperienceGain(true)

      // Gamification updates disabled to prevent RLS errors
      // The database trigger will handle stats updates automatically
      console.log('Experience gained:', xpGained, 'points')

      // Show success feedback
      setShowSuccessFeedback(true)

      // Redirect to home after showing experience gain animation
      setTimeout(() => {
        router.push('/')
      }, 2500)
    } catch (error: any) {
      console.error('Error creating study log:', error)
      setError(error.message || 'Failed to create study log')
    } finally {
      setLoading(false)
    }
  }

  // Show loading while checking auth
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated (will redirect)
  if (!user) {
    return null
  }

  return (
    <>
      <Head>
        <title>Create Study Log - StudyShare</title>
        <meta name="description" content="Create a new study log to track your learning progress" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <main className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 sticky top-0 z-50">
          <div className="max-w-2xl mx-auto px-4 py-4">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <HiArrowLeft className="w-5 h-5" />
              </button>
              <h1 className="text-xl font-bold">Create Study Log</h1>
            </div>
          </div>
        </div>

        <div className="max-w-2xl mx-auto px-4 py-6 space-y-6">
          {/* Timer Section - Outside of form to prevent event bubbling issues */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <TimerStopwatch onDurationChange={handleDurationChange} />
          </div>

          {/* Form Container */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* Error Message */}
              {error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-800 text-sm">{error}</p>
                </div>
              )}

              {/* Recipe Selection */}
              <div>
                <label htmlFor="recipe" className="block text-sm font-medium text-gray-700 mb-1">
                  Study Recipe *
                </label>
                {loadingRecipes ? (
                  <div className="text-sm text-gray-500">Loading recipes...</div>
                ) : (
                  <select
                    id="recipe"
                    value={selectedRecipeId}
                    onChange={(e) => handleRecipeChange(e.target.value)}
                    className="input"
                    required
                  >
                    <option value="">Select a recipe</option>
                    {availableRecipes.map((recipe) => (
                      <option key={recipe.id} value={recipe.id}>
                        {recipe.isOwnRecipe ? '📝 ' : '🔖 '}{recipe.title}
                        {!recipe.isOwnRecipe && ` (by @${recipe.author.username})`}
                      </option>
                    ))}
                  </select>
                )}
                <p className="text-xs text-gray-500 mt-1">Choose from your recipes or bookmarked recipes</p>
              </div>

              {/* Material Selection */}
              {selectedRecipeId && (
                <div>
                  <label htmlFor="material" className="block text-sm font-medium text-gray-700 mb-1">
                    Study Material *
                  </label>
                  {(() => {
                    const selectedRecipe = availableRecipes.find(recipe => recipe.id === selectedRecipeId)
                    if (!selectedRecipe) return null

                    if (selectedRecipe.materials.length === 0) {
                      return (
                        <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                          <p className="text-yellow-800 text-sm">
                            This recipe has no study materials. Please add materials to the recipe first.
                          </p>
                        </div>
                      )
                    }

                    return (
                      <>
                        <select
                          id="material"
                          value={selectedMaterialId}
                          onChange={(e) => handleMaterialChange(e.target.value)}
                          className="input"
                          required
                        >
                          <option value="">Select a material</option>
                          {selectedRecipe.materials.map((material) => (
                            <option key={material.id} value={material.id}>
                              📚 {material.material_name} ({material.frequency}, {material.time_per_session} {material.time_unit})
                            </option>
                          ))}
                        </select>
                        <p className="text-xs text-gray-500 mt-1">
                          Choose which material you studied in this session
                        </p>
                      </>
                    )
                  })()}
                </div>
              )}

              {/* Hidden Title Field - Auto-generated */}
              <input
                type="hidden"
                value={title}
                readOnly
              />

              {/* Manual Duration Input */}
              <div>
                <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-1">
                  Study Duration *
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    id="duration-hours"
                    type="number"
                    value={getDurationComponents(durationSeconds).hours || 0}
                    onChange={(e) => handleManualDurationChange('hours', parseInt(e.target.value) || 0)}
                    className="input w-16 text-center"
                    placeholder="0"
                    min="0"
                    max="23"
                    step="1"
                  />
                  <span className="text-sm text-gray-600">h</span>
                  <input
                    id="duration-minutes"
                    type="number"
                    value={getDurationComponents(durationSeconds).minutes || 0}
                    onChange={(e) => handleManualDurationChange('minutes', parseInt(e.target.value) || 0)}
                    className="input w-16 text-center"
                    placeholder="0"
                    min="0"
                    max="59"
                    step="1"
                  />
                  <span className="text-sm text-gray-600">m</span>
                  <input
                    id="duration-seconds"
                    type="number"
                    value={getDurationComponents(durationSeconds).seconds || 0}
                    onChange={(e) => handleManualDurationChange('seconds', parseInt(e.target.value) || 0)}
                    className="input w-16 text-center"
                    placeholder="0"
                    min="0"
                    max="59"
                    step="1"
                  />
                  <span className="text-sm text-gray-600">s</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">Duration will be set automatically when you stop the timer, or you can edit manually</p>
              </div>

              {/* Notes */}
              <div>
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                  Notes & Reflections
                </label>
                <textarea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="input resize-y"
                  style={{ minHeight: '100px' }}
                  placeholder="How did the study session go? Any insights or observations..."
                  maxLength={1000}
                />
              </div>

              {/* Submit Buttons */}
              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => router.back()}
                  className="btn-secondary flex-1"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn-primary flex-1"
                  disabled={loading || !selectedRecipeId || !title.trim() || durationSeconds <= 0}
                >
                  {loading ? 'Creating...' : 'Create Log'}
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* Gamification Notifications */}
        <ExperienceGainAnimation
          points={experienceGained}
          show={showExperienceGain}
          onComplete={() => setShowExperienceGain(false)}
        />

        <ActionFeedback
          message="Study session completed! Keep up the great work!"
          type="success"
          show={showSuccessFeedback}
          onComplete={() => setShowSuccessFeedback(false)}
        />
      </main>
    </>
  )
}
