import type { AppProps } from 'next/app'
import ErrorBoundary from '../components/ErrorBoundary'
import { GamificationProvider } from '../components/providers/GamificationProvider'
import { AuthProvider } from '../lib/auth'
import '../styles/globals.css'

// グローバルエラーハンドラーを初期化
import '../lib/monitoring/global-error-handler'

export default function App({ Component, pageProps }: AppProps) {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <GamificationProvider>
          <Component {...pageProps} />
        </GamificationProvider>
      </AuthProvider>
    </ErrorBoundary>
  )
}
