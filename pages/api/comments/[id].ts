import { supabase } from '@/lib/supabase'
import { createClient } from '@supabase/supabase-js'
import { NextApiRequest, NextApiResponse } from 'next'

// Create admin client for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (!['PUT', 'DELETE'].includes(req.method!)) {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { id: commentId } = req.query
    const authHeader = req.headers.authorization

    if (!authHeader) {
      return res.status(401).json({ error: 'No authorization header' })
    }

    // Verify user authentication
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      console.log('❌ API: Auth error:', authError)
      return res.status(401).json({ error: 'User not authenticated' })
    }

    console.log('✅ API: User authenticated:', user.id)

    // Verify comment exists and user owns it
    const { data: comment, error: commentError } = await supabaseAdmin
      .from('comments')
      .select('*')
      .eq('id', commentId)
      .single()

    if (commentError || !comment) {
      console.log('❌ API: Comment not found:', commentError)
      return res.status(404).json({ error: 'Comment not found' })
    }

    if (comment.user_id !== user.id) {
      console.log('❌ API: User not authorized to modify this comment')
      return res.status(403).json({ error: 'Not authorized to modify this comment' })
    }

    if (req.method === 'PUT') {
      // Update comment
      const { content } = req.body

      if (!content || content.trim().length === 0) {
        return res.status(400).json({ error: 'Comment content is required' })
      }

      if (content.length > 1000) {
        return res.status(400).json({ error: 'Comment content too long (max 1000 characters)' })
      }

      // Parse mentions from content
      const mentionRegex = /@(\w+)/g
      const mentions: string[] = []
      let match

      while ((match = mentionRegex.exec(content)) !== null) {
        mentions.push(match[1])
      }

      // Get user IDs for mentioned usernames
      let mentionUserIds: string[] = []
      if (mentions.length > 0) {
        const { data: mentionedUsers } = await supabaseAdmin
          .from('profiles')
          .select('id')
          .in('username', mentions)

        mentionUserIds = mentionedUsers?.map(u => u.id) || []
      }

      // Update comment
      const { data: updatedComment, error: updateError } = await supabaseAdmin
        .from('comments')
        .update({
          content: content.trim(),
          mentions: mentionUserIds
        })
        .eq('id', commentId)
        .select(`
          *,
          author:profiles(id, username, display_name)
        `)
        .single()

      if (updateError) {
        console.error('❌ API: Update comment error:', updateError)
        return res.status(500).json({ error: 'Failed to update comment' })
      }

      console.log('✅ API: Comment updated successfully:', commentId)
      res.status(200).json({ 
        comment: updatedComment,
        message: 'Comment updated successfully' 
      })

    } else if (req.method === 'DELETE') {
      console.log('🔄 API: Starting comment deletion process for:', commentId)

      // Check if comment has replies
      const { data: replies, error: repliesCheckError } = await supabaseAdmin
        .from('comments')
        .select('id')
        .eq('parent_id', commentId)

      if (repliesCheckError) {
        console.error('❌ API: Error checking replies:', repliesCheckError)
        return res.status(500).json({
          error: 'Failed to check comment replies',
          details: repliesCheckError.message
        })
      }

      console.log(`🔍 API: Found ${replies?.length || 0} replies to delete`)

      // Delete the main comment (CASCADE should handle replies automatically)
      console.log('🔄 API: Deleting comment with CASCADE...')
      const { error: deleteError } = await supabaseAdmin
        .from('comments')
        .delete()
        .eq('id', commentId)

      if (deleteError) {
        console.error('❌ API: Delete comment error:', deleteError)
        return res.status(500).json({
          error: 'Failed to delete comment',
          details: deleteError.message,
          code: deleteError.code
        })
      }

      console.log('✅ API: Comment deleted successfully:', commentId)
      res.status(200).json({
        message: 'Comment deleted successfully',
        deletedReplies: replies?.length || 0
      })
    }

  } catch (error) {
    console.error('❌ API: Unexpected error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
}
