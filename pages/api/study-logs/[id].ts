import { NextApiRequest, NextApiResponse } from 'next'
import { supabase } from '../../../lib/supabase'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const { id } = req.query

  // Validate study log ID
  if (!id || typeof id !== 'string' || id.trim() === '') {
    return res.status(400).json({ error: 'Invalid study log ID' })
  }

  // Additional validation for UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  if (!uuidRegex.test(id.trim())) {
    return res.status(400).json({ error: 'Invalid study log ID format' })
  }

  try {
    // Fetch study log with related data
    const { data: logData, error: logError } = await supabase
      .from('study_logs')
      .select(`
        *,
        study_recipes(
          id,
          title,
          tags
        ),
        study_materials(
          id,
          material_name,
          frequency,
          time_per_session,
          time_unit
        ),
        profiles!study_logs_user_id_fkey(
          id,
          username,
          display_name,
          rank_level
        ),
        comments(
          id
        )
      `)
      .eq('id', id)
      .single()

    if (logError) {
      if (logError.code === 'PGRST116') {
        return res.status(404).json({ error: 'Study log not found' })
      }
      throw logError
    }

    if (!logData) {
      return res.status(404).json({ error: 'Study log not found' })
    }

    // Transform the data to match the expected interface
    const transformedData = {
      ...logData,
      recipe: logData.study_recipes,
      material: logData.study_materials,
      author: logData.profiles || {
        id: logData.user_id,
        username: 'Unknown User',
        display_name: null,
        rank_level: 1
      },
      comment_count: logData.comments?.length || 0
    }

    res.status(200).json(transformedData)
  } catch (error) {
    console.error('Error fetching study log:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
}
