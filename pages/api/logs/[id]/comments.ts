import { supabase } from '@/lib/supabase'
import { createClient } from '@supabase/supabase-js'
import { NextApiRequest, NextApiResponse } from 'next'

// Create admin client for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { id: logId } = req.query
    const { content, parentId } = req.body
    const authHeader = req.headers.authorization

    if (!authHeader) {
      return res.status(401).json({ error: 'No authorization header' })
    }

    if (!content || content.trim().length === 0) {
      return res.status(400).json({ error: 'Comment content is required' })
    }

    if (content.length > 1000) {
      return res.status(400).json({ error: 'Comment content too long (max 1000 characters)' })
    }

    // Verify user authentication
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      console.log('❌ API: Auth error:', authError)
      return res.status(401).json({ error: 'User not authenticated' })
    }

    console.log('✅ API: User authenticated:', user.id)

    // Verify study log exists
    const { data: log, error: logError } = await supabaseAdmin
      .from('study_logs')
      .select('id')
      .eq('id', logId)
      .single()

    if (logError || !log) {
      console.log('❌ API: Study log not found:', logError)
      return res.status(404).json({ error: 'Study log not found' })
    }

    // If parentId is provided, verify parent comment exists and belongs to this log
    if (parentId) {
      const { data: parentComment, error: parentError } = await supabaseAdmin
        .from('comments')
        .select('id, log_id')
        .eq('id', parentId)
        .single()

      if (parentError || !parentComment || parentComment.log_id !== logId) {
        console.log('❌ API: Parent comment not found or invalid:', parentError)
        return res.status(400).json({ error: 'Invalid parent comment' })
      }
    }

    // Parse mentions from content
    const mentionRegex = /@(\w+)/g
    const mentions: string[] = []
    let match

    while ((match = mentionRegex.exec(content)) !== null) {
      mentions.push(match[1])
    }

    // Get user IDs for mentioned usernames
    let mentionUserIds: string[] = []
    if (mentions.length > 0) {
      const { data: mentionedUsers } = await supabaseAdmin
        .from('profiles')
        .select('id')
        .in('username', mentions)

      mentionUserIds = mentionedUsers?.map(u => u.id) || []
    }

    // Insert comment
    const { data: commentData, error: insertError } = await supabaseAdmin
      .from('comments')
      .insert([{
        user_id: user.id,
        log_id: logId,
        parent_id: parentId || null,
        content: content.trim(),
        mentions: mentionUserIds
      }])
      .select(`
        *,
        author:profiles(id, username, display_name),
        parent_comment:comments!parent_id(
          author:profiles(id, username, display_name)
        )
      `)
      .single()

    if (insertError) {
      console.log('❌ API: Insert comment error:', insertError)
      return res.status(500).json({ error: 'Failed to add comment' })
    }

    console.log('✅ API: Comment added successfully:', commentData.id)

    // Return the comment with author information
    res.status(201).json({ 
      comment: commentData,
      message: 'Comment added successfully' 
    })

  } catch (error) {
    console.error('❌ API: Unexpected error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
}
