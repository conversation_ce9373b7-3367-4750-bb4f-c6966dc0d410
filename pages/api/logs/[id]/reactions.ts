import { NextApiRequest, NextApiResponse } from 'next'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Admin client with service role
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Regular client for auth
const supabase = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!)

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { id: logId } = req.query
    const { emoji, action } = req.body
    const authHeader = req.headers.authorization

    if (!authHeader) {
      return res.status(401).json({ error: 'No authorization header' })
    }

    if (!emoji || !action) {
      return res.status(400).json({ error: 'Missing emoji or action' })
    }

    if (!['add', 'remove'].includes(action)) {
      return res.status(400).json({ error: 'Invalid action. Must be "add" or "remove"' })
    }

    // Verify user authentication
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      console.log('❌ API: Auth error:', authError)
      return res.status(401).json({ error: 'User not authenticated' })
    }

    console.log('✅ API: User authenticated:', user.id)

    // Verify the study log exists
    const { data: log, error: logError } = await supabaseAdmin
      .from('study_logs')
      .select('id, user_id')
      .eq('id', logId)
      .single()

    if (logError) {
      console.log('❌ API: Study log fetch error:', logError)
      return res.status(404).json({ error: 'Study log not found' })
    }

    console.log('📊 API: Study log data:', log)

    if (action === 'add') {
      console.log('➕ API: Adding reaction')
      
      // Check if user already reacted with this emoji
      const { data: existingReaction } = await supabaseAdmin
        .from('reactions')
        .select('id')
        .eq('user_id', user.id)
        .eq('log_id', logId)
        .eq('emoji', emoji)
        .single()

      if (existingReaction) {
        return res.status(400).json({ error: 'User already reacted with this emoji' })
      }

      // Add reaction
      const { error: insertError } = await supabaseAdmin
        .from('reactions')
        .insert([{ 
          user_id: user.id, 
          log_id: logId, 
          emoji 
        }])

      if (insertError) {
        console.log('❌ API: Insert reaction error:', insertError)
        return res.status(500).json({ error: 'Failed to add reaction' })
      }

      console.log('✅ API: Reaction added successfully')
      return res.status(200).json({ success: true, action: 'added', emoji })

    } else if (action === 'remove') {
      console.log('➖ API: Removing reaction')
      
      // Remove reaction
      const { error: deleteError } = await supabaseAdmin
        .from('reactions')
        .delete()
        .eq('user_id', user.id)
        .eq('log_id', logId)
        .eq('emoji', emoji)

      if (deleteError) {
        console.log('❌ API: Delete reaction error:', deleteError)
        return res.status(500).json({ error: 'Failed to remove reaction' })
      }

      console.log('✅ API: Reaction removed successfully')
      return res.status(200).json({ success: true, action: 'removed', emoji })
    }

  } catch (error) {
    console.error('❌ API: Error handling reaction:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
