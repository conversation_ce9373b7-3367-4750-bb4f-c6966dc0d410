import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    try {
      const { data: profiles, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching profiles:', error);
        return res.status(500).json({ error: error.message });
      }

      res.status(200).json(profiles || []);
    } catch (error) {
      console.error('Profiles API error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  } else if (req.method === 'POST') {
    try {
      const { username, display_name, bio, avatar_url } = req.body;

      // Get user from Authorization header
      const authHeader = req.headers.authorization;
      if (!authHeader) {
        return res.status(401).json({ error: 'No authorization header' });
      }

      const token = authHeader.replace('Bearer ', '');
      const { data: { user }, error: userError } = await supabase.auth.getUser(token);

      if (userError || !user) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      // Create or update profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .upsert([{
          id: user.id,
          username,
          display_name,
          bio,
          avatar_url
        }])
        .select()
        .single();

      if (profileError) {
        console.error('Error creating/updating profile:', profileError);
        return res.status(500).json({ error: profileError.message });
      }

      res.status(200).json(profile);
    } catch (error) {
      console.error('Create profile API error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
