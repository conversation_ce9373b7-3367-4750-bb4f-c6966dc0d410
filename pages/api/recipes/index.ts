import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    try {
      const { data: recipes, error } = await supabase
        .from('study_recipes')
        .select(`
          *,
          profiles:user_id (
            username,
            display_name,
            avatar_url
          ),
          study_materials (*)
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching recipes:', error);
        return res.status(500).json({ error: error.message });
      }

      res.status(200).json(recipes || []);
    } catch (error) {
      console.error('Recipes API error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  } else if (req.method === 'POST') {
    try {
      const { title, estimated_duration, tips, tags, materials } = req.body;

      // Get user from Authorization header
      const authHeader = req.headers.authorization;
      if (!authHeader) {
        return res.status(401).json({ error: 'No authorization header' });
      }

      const token = authHeader.replace('Bearer ', '');
      const { data: { user }, error: userError } = await supabase.auth.getUser(token);

      if (userError || !user) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      // Create recipe
      const { data: recipe, error: recipeError } = await supabase
        .from('study_recipes')
        .insert([{
          user_id: user.id,
          title,
          estimated_duration,
          tips,
          tags: tags || []
        }])
        .select()
        .single();

      if (recipeError) {
        console.error('Error creating recipe:', recipeError);
        return res.status(500).json({ error: recipeError.message });
      }

      // Create materials if provided
      if (materials && materials.length > 0) {
        const materialsToInsert = materials.map((material: any, index: number) => ({
          recipe_id: recipe.id,
          material_name: material.material_name,
          frequency: material.frequency,
          time_per_session: material.time_per_session,
          time_unit: material.time_unit || 'minutes',
          order_index: index
        }));

        const { error: materialsError } = await supabase
          .from('study_materials')
          .insert(materialsToInsert);

        if (materialsError) {
          console.error('Error creating materials:', materialsError);
          // Don't fail the whole request, just log the error
        }
      }

      res.status(201).json(recipe);
    } catch (error) {
      console.error('Create recipe API error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
