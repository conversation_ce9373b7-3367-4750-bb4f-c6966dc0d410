import { supabase } from '@/lib/supabase'
import { createClient } from '@supabase/supabase-js'
import { NextApiRequest, NextApiResponse } from 'next'

// Create admin client for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const startTime = Date.now()
  console.log('🔄 API: Recipe comments endpoint called', {
    method: req.method,
    query: req.query,
    hasAuth: !!req.headers.authorization,
    timestamp: startTime
  })

  if (req.method !== 'POST') {
    console.log('❌ API: Method not allowed:', req.method)
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { id: recipeId } = req.query
    const { content, parentId } = req.body
    const authHeader = req.headers.authorization

    console.log('🔍 API: Request details:', {
      recipeId,
      content: content?.substring(0, 50) + '...',
      parentId,
      hasAuthHeader: !!authHeader
    })

    if (!authHeader) {
      console.log('❌ API: No authorization header')
      return res.status(401).json({ error: 'No authorization header' })
    }

    if (!content || content.trim().length === 0) {
      console.log('❌ API: Empty content')
      return res.status(400).json({ error: 'Comment content is required' })
    }

    if (content.length > 1000) {
      console.log('❌ API: Content too long:', content.length)
      return res.status(400).json({ error: 'Comment content too long (max 1000 characters)' })
    }

    // Verify user authentication
    const token = authHeader.replace('Bearer ', '')
    console.log('🔍 API: Verifying user authentication...')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      console.log('❌ API: Auth error:', authError)
      return res.status(401).json({ error: 'User not authenticated' })
    }

    console.log('✅ API: User authenticated:', user.id)

    // Skip recipe existence check for performance - foreign key constraint will handle invalid recipe IDs
    // If recipe doesn't exist, the insert will fail with a proper error
    console.log('⚡ API: Skipping recipe existence check for performance')

    // If parentId is provided, verify parent comment exists and belongs to this recipe
    if (parentId) {
      const { data: parentComment, error: parentError } = await supabaseAdmin
        .from('comments')
        .select('id, recipe_id')
        .eq('id', parentId)
        .single()

      if (parentError || !parentComment || parentComment.recipe_id !== recipeId) {
        console.log('❌ API: Parent comment not found or invalid:', parentError)
        return res.status(400).json({ error: 'Invalid parent comment' })
      }
    }

    // Parse mentions from content (only if @ symbol exists)
    let mentionUserIds: string[] = []
    if (content.includes('@')) {
      const mentionCheckStart = Date.now()
      const mentionRegex = /@(\w+)/g
      const mentions: string[] = []
      let match

      while ((match = mentionRegex.exec(content)) !== null) {
        mentions.push(match[1])
      }

      // Get user IDs for mentioned usernames
      if (mentions.length > 0) {
        const { data: mentionedUsers } = await supabaseAdmin
          .from('profiles')
          .select('id')
          .in('username', mentions)

        mentionUserIds = mentionedUsers?.map(u => u.id) || []
      }
      console.log(`⏱️ API: Mention processing took ${Date.now() - mentionCheckStart}ms`)
    } else {
      console.log('⚡ API: No mentions found, skipping mention processing')
    }

    // Insert comment
    console.log('🔄 API: Inserting comment into database...')
    const insertStart = Date.now()
    const insertData = {
      user_id: user.id,
      recipe_id: recipeId,
      parent_id: parentId || null,
      content: content.trim(),
      mentions: mentionUserIds
    }
    console.log('🔍 API: Insert data:', insertData)

    const { data: commentData, error: insertError } = await supabaseAdmin
      .from('comments')
      .insert([insertData])
      .select(`
        *,
        author:profiles(id, username, display_name),
        parent_comment:comments!parent_id(
          author:profiles(id, username, display_name)
        )
      `)
      .single()

    console.log(`⏱️ API: Comment insert took ${Date.now() - insertStart}ms`)

    if (insertError) {
      console.log('❌ API: Insert comment error:', insertError)
      return res.status(500).json({ error: 'Failed to add comment' })
    }

    const totalTime = Date.now() - startTime
    console.log('✅ API: Comment added successfully:', commentData.id)
    console.log(`⏱️ API: Total processing time: ${totalTime}ms`)

    // Return the comment with author information
    res.status(201).json({
      comment: commentData,
      message: 'Comment added successfully'
    })

  } catch (error) {
    console.error('❌ API: Unexpected error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
}
