import { createClient } from '@supabase/supabase-js'
import { NextApiRequest, NextApiResponse } from 'next'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Admin client with service role
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Regular client for auth
const supabase = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!)

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { id: recipeId } = req.query
    const authHeader = req.headers.authorization

    if (!authHeader) {
      return res.status(401).json({ error: 'No authorization header' })
    }

    // Verify user authentication
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      console.log('❌ API: Auth error:', authError)
      return res.status(401).json({ error: 'User not authenticated' })
    }

    console.log('✅ API: User authenticated:', user.id)

    // Check if recipe exists
    const { data: recipe, error: recipeError } = await supabaseAdmin
      .from('study_recipes')
      .select('id')
      .eq('id', recipeId)
      .single()

    if (recipeError) {
      console.log('❌ API: Recipe fetch error:', recipeError)
      return res.status(404).json({ error: 'Recipe not found' })
    }

    console.log('📊 API: Recipe found:', recipe.id)

    // Check if user already bookmarked
    const { data: existingBookmark } = await supabaseAdmin
      .from('bookmarks')
      .select('id')
      .eq('user_id', user.id)
      .eq('recipe_id', recipeId)
      .single()

    console.log('🔖 API: Existing bookmark check:', existingBookmark)

    if (existingBookmark) {
      console.log('➖ API: Removing existing bookmark')
      
      // Remove bookmark
      const { error: deleteError } = await supabaseAdmin
        .from('bookmarks')
        .delete()
        .eq('user_id', user.id)
        .eq('recipe_id', recipeId)

      if (deleteError) {
        console.log('❌ API: Delete bookmark error:', deleteError)
        return res.status(500).json({ error: 'Failed to remove bookmark' })
      }

      console.log('✅ API: Bookmark removed')
      return res.status(200).json({ isBookmarked: false })

    } else {
      console.log('➕ API: Adding new bookmark')
      
      // Add bookmark
      const { error: insertError } = await supabaseAdmin
        .from('bookmarks')
        .insert([{ user_id: user.id, recipe_id: recipeId }])

      if (insertError) {
        console.log('❌ API: Insert bookmark error:', insertError)
        return res.status(500).json({ error: 'Failed to add bookmark' })
      }

      console.log('✅ API: Bookmark added')
      return res.status(200).json({ isBookmarked: true })
    }

  } catch (error) {
    console.error('❌ API: Error bookmarking recipe:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
