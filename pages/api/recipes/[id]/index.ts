import { createClient } from '@supabase/supabase-js'
import { NextApiRequest, NextApiResponse } from 'next'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Admin client with service role
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Regular client for auth
const supabase = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!)

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { id: recipeId } = req.query
    const authHeader = req.headers.authorization

    console.log('🔄 API: Fetching recipe details for ID:', recipeId)

    // Get current user if authenticated
    let currentUser = null
    if (authHeader) {
      try {
        const token = authHeader.replace('Bearer ', '')
        const { data: { user }, error: authError } = await supabase.auth.getUser(token)
        if (!authError && user) {
          currentUser = user
          console.log('✅ API: User authenticated:', user.id)
        }
      } catch (authError) {
        console.log('⚠️ API: Auth check failed, continuing without user context')
      }
    }

    // Get recipe data
    const { data: recipe, error: recipeError } = await supabaseAdmin
      .from('study_recipes')
      .select('*')
      .eq('id', recipeId)
      .single()

    if (recipeError || !recipe) {
      console.log('❌ API: Recipe not found:', recipeError)
      return res.status(404).json({ error: 'Recipe not found' })
    }

    console.log('✅ API: Recipe found:', recipe.title)

    // Get author profile
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('id, username, display_name, rank_level')
      .eq('id', recipe.user_id)
      .single()

    if (profileError) {
      console.log('❌ API: Profile fetch error:', profileError)
      return res.status(500).json({ error: 'Failed to fetch author profile' })
    }

    console.log('✅ API: Profile found:', profile.username)

    // Get study materials
    const { data: materials, error: materialsError } = await supabaseAdmin
      .from('study_materials')
      .select('*')
      .eq('recipe_id', recipeId)
      .order('order_index')

    if (materialsError) {
      console.log('❌ API: Materials fetch error:', materialsError)
      return res.status(500).json({ error: 'Failed to fetch materials' })
    }

    console.log('✅ API: Materials found:', materials?.length || 0)

    // Get comment count
    const { count: commentCount, error: commentCountError } = await supabaseAdmin
      .from('comments')
      .select('*', { count: 'exact', head: true })
      .eq('recipe_id', recipeId)

    if (commentCountError) {
      console.log('❌ API: Comment count error:', commentCountError)
      // Don't fail the request for comment count error
    }

    console.log('✅ API: Comment count:', commentCount || 0)

    // Get user interaction status if authenticated
    let userHasUpvoted = false
    let userHasBookmarked = false

    if (currentUser) {
      // Check if user has upvoted
      const { data: upvoteData } = await supabaseAdmin
        .from('votes')
        .select('id')
        .eq('user_id', currentUser.id)
        .eq('recipe_id', recipeId)
        .single()

      userHasUpvoted = !!upvoteData

      // Check if user has bookmarked
      const { data: bookmarkData } = await supabaseAdmin
        .from('bookmarks')
        .select('id')
        .eq('user_id', currentUser.id)
        .eq('recipe_id', recipeId)
        .single()

      userHasBookmarked = !!bookmarkData

      console.log('✅ API: User interaction status - upvoted:', userHasUpvoted, 'bookmarked:', userHasBookmarked)
    }

    // Combine the data
    const result = {
      ...recipe,
      author: {
        username: profile.username,
        display_name: profile.display_name,
        rank_level: profile.rank_level || 1
      },
      materials: materials || [],
      comment_count: commentCount || 0,
      user_has_upvoted: userHasUpvoted,
      user_has_bookmarked: userHasBookmarked
    }

    console.log('✅ API: Recipe details compiled successfully')
    res.status(200).json(result)

  } catch (error) {
    console.error('❌ API: Unexpected error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
}
