import { NextApiRequest, NextApiResponse } from 'next'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Admin client with service role
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Regular client for auth
const supabase = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!)

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { id: recipeId } = req.query
    const authHeader = req.headers.authorization

    if (!authHeader) {
      return res.status(401).json({ error: 'No authorization header' })
    }

    // Verify user authentication
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      console.log('❌ API: Auth error:', authError)
      return res.status(401).json({ error: 'User not authenticated' })
    }

    console.log('✅ API: User authenticated:', user.id)

    // Get current recipe data
    const { data: recipe, error: recipeError } = await supabaseAdmin
      .from('study_recipes')
      .select('user_id, upvote_count')
      .eq('id', recipeId)
      .single()

    if (recipeError) {
      console.log('❌ API: Recipe fetch error:', recipeError)
      return res.status(404).json({ error: 'Recipe not found' })
    }

    console.log('📊 API: Recipe data:', recipe)

    // Allow users to upvote their own recipes
    console.log('✅ API: Self-upvote allowed for user:', user.id)

    // Check if user already voted
    const { data: existingVote } = await supabaseAdmin
      .from('votes')
      .select('id')
      .eq('user_id', user.id)
      .eq('recipe_id', recipeId)
      .single()

    console.log('🗳️ API: Existing vote check:', existingVote)

    if (existingVote) {
      console.log('➖ API: Removing existing vote')
      
      // Remove vote
      const { error: deleteError } = await supabaseAdmin
        .from('votes')
        .delete()
        .eq('user_id', user.id)
        .eq('recipe_id', recipeId)

      if (deleteError) {
        console.log('❌ API: Delete vote error:', deleteError)
        return res.status(500).json({ error: 'Failed to remove vote' })
      }

      // Decrement upvote count
      const newCount = Math.max(0, (recipe.upvote_count || 0) - 1)
      console.log('🔄 API: Updating recipe count from', recipe.upvote_count, 'to', newCount)
      
      const { error: updateError } = await supabaseAdmin
        .from('study_recipes')
        .update({ upvote_count: newCount })
        .eq('id', recipeId)

      if (updateError) {
        console.error('❌ API: Update error details:', updateError)
        return res.status(500).json({ error: 'Failed to update vote count' })
      }

      console.log('✅ API: Vote removed, new count:', newCount)
      return res.status(200).json({ voted: false, newCount })

    } else {
      console.log('➕ API: Adding new vote')
      
      // Add vote
      const { error: insertError } = await supabaseAdmin
        .from('votes')
        .insert([{ user_id: user.id, recipe_id: recipeId }])

      if (insertError) {
        console.log('❌ API: Insert vote error:', insertError)
        return res.status(500).json({ error: 'Failed to add vote' })
      }

      // Increment upvote count
      const newCount = (recipe.upvote_count || 0) + 1
      console.log('🔄 API: Updating recipe count from', recipe.upvote_count, 'to', newCount)
      
      const { error: updateError } = await supabaseAdmin
        .from('study_recipes')
        .update({ upvote_count: newCount })
        .eq('id', recipeId)

      if (updateError) {
        console.error('❌ API: Update error details:', updateError)
        return res.status(500).json({ error: 'Failed to update vote count' })
      }

      console.log('✅ API: Vote added, new count:', newCount)
      return res.status(200).json({ voted: true, newCount })
    }

  } catch (error) {
    console.error('❌ API: Error voting on recipe:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
