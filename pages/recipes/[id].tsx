import Head from 'next/head'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { HiOutlineArrowLeft, HiOutlineFire } from 'react-icons/hi2'
import CommentList from '../../components/comments/CommentList'
import { CompactRankBadge } from '../../components/gamification/RankBadge'
import { RecipeWithMaterials } from '../../lib/api/recipes'
import { useAuth } from '../../lib/auth'

export default function RecipeDetailPage() {
  const router = useRouter()
  const { id } = router.query
  const { user } = useAuth()
  
  const [recipe, setRecipe] = useState<RecipeWithMaterials | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [commentCount, setCommentCount] = useState(0)
  const [showComments, setShowComments] = useState(false)
  const [isUpvoted, setIsUpvoted] = useState(false)
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [isUpvoting, setIsUpvoting] = useState(false)
  const [isBookmarking, setIsBookmarking] = useState(false)
  const [authModalOpen, setAuthModalOpen] = useState(false)
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin')
  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null)

  useEffect(() => {
    if (id && typeof id === 'string') {
      loadRecipe(id)
    }
  }, [id])

  // Handle anchor link to comments section
  useEffect(() => {
    if (router.asPath.includes('#comments') && recipe) {
      setTimeout(() => {
        const commentsElement = document.getElementById('comments')
        if (commentsElement) {
          commentsElement.scrollIntoView({ behavior: 'smooth' })
        }
      }, 100)
    }
  }, [router.asPath, recipe])

  const loadRecipe = async (recipeId: string) => {
    try {
      setLoading(true)
      setError(null)
      
      console.log('🔄 RecipeDetail: Loading recipe:', recipeId)
      
      // Validate recipe ID format
      if (!recipeId || typeof recipeId !== 'string' || recipeId.trim() === '') {
        throw new Error('Invalid recipe ID')
      }
      
      // Additional validation for UUID format (if recipes use UUID)
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
      if (!uuidRegex.test(recipeId.trim())) {
        throw new Error('Invalid recipe ID format')
      }
      
      // Prepare headers with authentication if user is logged in
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      }
      
      if (user) {
        try {
          const { createClient } = await import('@supabase/supabase-js')
          const supabase = createClient(
            process.env.NEXT_PUBLIC_SUPABASE_URL!,
            process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
          )
          
          const { data: { session } } = await supabase.auth.getSession()
          if (session) {
            headers['Authorization'] = `Bearer ${session.access_token}`
          }
        } catch (authError) {
          console.log('⚠️ RecipeDetail: Auth header setup failed:', authError)
        }
      }
      
      // Fetch recipe data from API
      const response = await fetch(`/api/recipes/${encodeURIComponent(recipeId)}`, {
        method: 'GET',
        headers
      })
      
      if (!response.ok) {
        if (response.status === 404) {
          setError('Recipe not found')
          return
        }
        if (response.status >= 500) {
          throw new Error('Server error occurred. Please try again later.')
        }
        throw new Error(`Failed to load recipe (${response.status})`)
      }
      
      const recipeData = await response.json()
      
      // Validate response data structure
      if (!recipeData || typeof recipeData !== 'object') {
        throw new Error('Invalid recipe data received')
      }
      
      if (!recipeData.id || !recipeData.title) {
        throw new Error('Incomplete recipe data received')
      }
      
      console.log('✅ RecipeDetail: Recipe loaded:', recipeData)
      
      setRecipe(recipeData)
      setCommentCount(recipeData.comment_count || 0)
      
      // Initialize user interaction states from API response
      setIsUpvoted(recipeData.user_has_upvoted || false)
      setIsBookmarked(recipeData.user_has_bookmarked || false)
      
      console.log('✅ RecipeDetail: Initial states set - upvoted:', recipeData.user_has_upvoted, 'bookmarked:', recipeData.user_has_bookmarked)
    } catch (err) {
      console.error('❌ RecipeDetail: Error loading recipe:', err)
      
      // Enhanced error handling with specific error messages
      if (err instanceof Error) {
        setError(err.message)
      } else {
        setError('An unexpected error occurred. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  // Handle upvote functionality
  const handleUpvoteClick = async () => {
    if (!user || !recipe) return
    
    console.log('🖱️ Detail: Upvote button clicked for recipe:', recipe.id)
    
    try {
      setIsUpvoting(true)
      
      // Optimistic update
      const wasUpvoted = isUpvoted
      setIsUpvoted(!wasUpvoted)
      setRecipe(prev => prev ? {
        ...prev,
        upvote_count: wasUpvoted ? prev.upvote_count - 1 : prev.upvote_count + 1
      } : prev)
      
      // Get session token from Supabase
      const { createClient } = await import('@supabase/supabase-js')
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      )
      
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        throw new Error('No session found')
      }
      
      const response = await fetch(`/api/recipes/${recipe.id}/upvote`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
      })
      
      if (!response.ok) {
        throw new Error('Failed to update upvote')
      }
      
      const result = await response.json()
      console.log('✅ Detail: Upvote updated:', result)
      
      // Update with server response
      setIsUpvoted(result.voted)
      setRecipe(prev => prev ? {
        ...prev,
        upvote_count: result.newCount
      } : prev)
      
    } catch (error) {
      console.error('❌ Detail: Error updating upvote:', error)
      
      // Revert optimistic update
      setIsUpvoted(isUpvoted)
      setRecipe(prev => prev ? {
        ...prev,
        upvote_count: recipe.upvote_count
      } : prev)
    } finally {
      setIsUpvoting(false)
    }
  }

  // Handle bookmark functionality
  const handleBookmarkClick = async () => {
    if (!user || !recipe) return
    
    console.log('🖱️ Detail: Bookmark button clicked for recipe:', recipe.id)
    
    try {
      setIsBookmarking(true)
      
      // Optimistic update
      const wasBookmarked = isBookmarked
      setIsBookmarked(!wasBookmarked)
      
      // Get session token from Supabase
      const { createClient } = await import('@supabase/supabase-js')
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      )
      
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        throw new Error('No session found')
      }
      
      const response = await fetch(`/api/recipes/${recipe.id}/bookmark`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
      })
      
      if (!response.ok) {
        throw new Error('Failed to update bookmark')
      }
      
      const result = await response.json()
      console.log('✅ Detail: Bookmark updated:', result)
      
      // Update with server response
      setIsBookmarked(result.isBookmarked)
      
    } catch (error) {
      console.error('❌ Detail: Error updating bookmark:', error)
      
      // Revert optimistic update
      setIsBookmarked(isBookmarked)
    } finally {
      setIsBookmarking(false)
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'now'
    if (diffInHours < 24) return `${diffInHours}h`
    return `${Math.floor(diffInHours / 24)}d`
  }

  // Convert frequency to English
  const convertFrequencyToEnglish = (frequency: string) => {
    const frequencyMap: { [key: string]: string } = {
      'daily': 'daily',
      '毎日': 'daily',
      'weekly': 'weekly',
      '6_times_week': '6x/week',
      '6x/week': '6x/week',
      '週6回': '6x/week',
      '5_times_week': '5x/week',
      '5x/week': '5x/week',
      '週5回': '5x/week',
      '4_times_week': '4x/week',
      '4x/week': '4x/week',
      '週4回': '4x/week',
      '3_times_week': '3x/week',
      '3x/week': '3x/week',
      '週3回': '3x/week',
      '2_times_week': '2x/week',
      '2x/week': '2x/week',
      '週2回': '2x/week',
      '1_time_week': 'weekly',
      '1x/week': 'weekly',
      '週1回': 'weekly',
      '4_times_month': '4x/month',
      '月4回': '4x/month',
      '3_times_month': '3x/month',
      '月3回': '3x/month',
      '2_times_month': '2x/month',
      '月2回': '2x/month'
    }
    return frequencyMap[frequency] || frequency
  }

  // Format estimated duration to short form (e.g., "1時間30分" -> "1h 30m")
  const formatEstimatedDuration = (duration: string | undefined) => {
    if (!duration) return 'Flexible'
    
    let result = duration
    
    // First, handle Japanese units and convert to English
    result = result.replace(/(\d+)時間(\d+)分/g, '$1 hours $2 minutes')
    result = result.replace(/(\d+)時間/g, '$1 hours')
    result = result.replace(/(\d+)分/g, '$1 minutes')
    result = result.replace(/ヶ月/g, ' months')
    result = result.replace(/か月/g, ' months')
    result = result.replace(/月/g, ' months')
    result = result.replace(/週間/g, ' weeks')
    result = result.replace(/週/g, ' weeks')
    result = result.replace(/日/g, ' days')
    result = result.replace(/年/g, ' years')
    
    // Then, convert English to short form
    result = result.replace(/(\d+)\s*hours\s*(\d+)\s*minutes/gi, '$1h $2m')
    result = result.replace(/(\d+)\s*hours/gi, '$1h')
    result = result.replace(/(\d+)\s*minutes/gi, '$1m')
    result = result.replace(/(\d+)\s*mins/gi, '$1m')
    
    // Handle special formats like "25分×8セット"
    result = result.replace(/(\d+)\s*m×(\d+)セット/gi, '$1m×$2 sets')
    result = result.replace(/×(\d+)セット/gi, '×$1 sets')
    
    return result.trim()
  }

  // Calculate total study time per week
  const calculateWeeklyTime = () => {
    if (!recipe?.materials || recipe.materials.length === 0) return null

    let totalMinutesPerWeek = 0
    recipe.materials.forEach(material => {
      const timePerSession = material.time_per_session
      let sessionsPerWeek = 0

      switch (material.frequency.toLowerCase()) {
        case 'daily':
          sessionsPerWeek = 7
          break
        case '6_times_week':
        case '6x/week':
          sessionsPerWeek = 6
          break
        case '5_times_week':
        case '5x/week':
          sessionsPerWeek = 5
          break
        case '4_times_week':
        case '4x/week':
          sessionsPerWeek = 4
          break
        case '3_times_week':
        case '3x/week':
          sessionsPerWeek = 3
          break
        case '2_times_week':
        case '2x/week':
          sessionsPerWeek = 2
          break
        case '1_time_week':
        case 'weekly':
        case '1x/week':
          sessionsPerWeek = 1
          break
        case '4_times_month':
          sessionsPerWeek = 1 // 月4回 ≈ 週1回
          break
        case '3_times_month':
          sessionsPerWeek = 0.75 // 月3回 ≈ 週0.75回
          break
        case '2_times_month':
          sessionsPerWeek = 0.5 // 月2回 ≈ 週0.5回
          break
        default:
          sessionsPerWeek = 1
      }

      totalMinutesPerWeek += timePerSession * sessionsPerWeek
    })

    const hours = Math.floor(totalMinutesPerWeek / 60)
    const minutes = totalMinutesPerWeek % 60

    if (hours > 0 && minutes > 0) {
      return `${hours}h ${minutes}m`
    } else if (hours > 0) {
      return `${hours}h`
    } else {
      return `${minutes}m`
    }
  }

  const handleAuthRequired = (action: () => void) => {
    setPendingAction(() => action)
    setAuthModalOpen(true)
  }

  const handleAuthModalClose = () => {
    setAuthModalOpen(false)
    setPendingAction(null)
  }

  const handleAuthSuccess = () => {
    setAuthModalOpen(false)
    if (pendingAction) {
      setTimeout(() => {
        pendingAction()
        setPendingAction(null)
      }, 100)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="w-8 h-8 border-2 border-gray-300 border-t-black rounded-full animate-spin" />
      </div>
    )
  }

  if (error || !recipe) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-black mb-2">Recipe Not Found</h1>
          <p className="text-gray-600 mb-4">{error || 'The recipe you are looking for does not exist.'}</p>
          <button
            onClick={() => router.push('/')}
            className="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
          >
            Go Home
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>{recipe.title} - StudyShare</title>
        <meta name="description" content={`Study recipe: ${recipe.title}`} />
      </Head>

      <main className="min-h-screen bg-white">
        {/* Timeline Container - Same as HomePage */}
        <div className="timeline-container">
          {/* Header - Always sticky, same as HomePage */}
          <div className="bg-white sticky top-0 z-50">
            <div className="px-4 py-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => router.back()}
                  className="text-gray-600 hover:text-black transition-colors"
                >
                  <HiOutlineArrowLeft className="w-5 h-5" />
                </button>
                <div>
                  <div className="font-bold text-lg">{recipe.title}</div>
                  <div className="text-sm text-gray-500">@{recipe.author.username}</div>
                </div>
              </div>
            </div>
          </div>
          {/* Timeline Item */}
          <div className="timeline-item">
            {/* Avatar */}
            <div className="timeline-avatar">
              <span className="text-sm font-medium text-gray-700">
                {recipe.author.display_name?.charAt(0) || recipe.author.username.charAt(0)}
              </span>
            </div>

            {/* Content */}
            <div className="timeline-content">
              {/* Header */}
              <div className="timeline-header">
                <span className="timeline-username">
                  {recipe.author.display_name || recipe.author.username}
                </span>
                <span className="timeline-handle">@{recipe.author.username}</span>
                <span className="timeline-time">·</span>
                <span className="timeline-time">{formatTimeAgo(recipe.created_at)}</span>
                <CompactRankBadge level={recipe.author.rank_level || 1} />
              </div>

              <div className="space-y-3">
                {/* Recipe Title */}
                <h2
                  className="text-xl font-bold text-gray-900 leading-tight cursor-pointer hover:text-gray-700 transition-colors"
                  onClick={() => router.push(`/recipes/${recipe.id}`)}
                  data-testid="recipe-title"
                >
                  {recipe.title}
                </h2>

                {/* Popular Recipe Indicator */}
                {recipe.upvote_count >= 10 && (
                  <div className="flex items-center space-x-1 mb-2">
                    <HiOutlineFire className="w-4 h-4 text-orange-500" />
                    <span className="text-sm text-orange-600 font-medium">Popular Recipe</span>
                  </div>
                )}

                {/* Meta Information */}
                <div className="grid grid-cols-2 gap-4">
                  {/* Estimated Duration */}
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">🎯</span>
                    <div>
                      <div className="text-sm text-gray-600">To Goal</div>
                      <div className="font-medium text-black">{formatEstimatedDuration(recipe.estimated_duration)}</div>
                    </div>
                  </div>

                  {/* Time per week */}
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">⏱️</span>
                    <div>
                      <div className="text-sm text-gray-600">Per Week</div>
                      <div className="font-medium text-black">{calculateWeeklyTime() || '2h'}</div>
                    </div>
                  </div>
                </div>

                {/* Materials Section */}
                {recipe.materials && recipe.materials.length > 0 && (
                  <div className="space-y-3">
                    {recipe.materials.map((material) => (
                      <div key={material.id} className="flex justify-between items-center py-2 border-b border-gray-100">
                        <span className="text-gray-900 font-medium">{material.material_name}</span>
                        <span className="text-gray-600">
                          {material.time_per_session} {material.time_unit === 'minutes' ? 'min' : material.time_unit === 'hours' ? 'h' : material.time_unit}/{convertFrequencyToEnglish(material.frequency)}
                        </span>
                      </div>
                    ))}
                  </div>
                )}

                {/* Tips Section */}
                {recipe.tips && (
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-sm font-medium text-gray-800">Tips & Notes:</span>
                    </div>
                    <div className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                      {recipe.tips}
                    </div>
                  </div>
                )}

                {/* Tags - Moved after Tips & Notes */}
                {recipe.tags && recipe.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {recipe.tags.map((tag) => (
                      <span key={tag} className="px-2 py-1 bg-black text-white text-xs rounded-full">
                        #{tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Timeline Actions */}
              <div className="timeline-actions">
                <div className="flex items-center space-x-2">
                  {/* Upvote Action - Using HeroIcons like TimelineActions */}
                  <button
                    onClick={handleUpvoteClick}
                    disabled={!user || isUpvoting}
                    className={`flex items-center space-x-2 px-3 py-1.5 rounded-full transition-all duration-200 ${
                      isUpvoted 
                        ? 'bg-orange-50 text-orange-600 hover:bg-orange-100' 
                        : 'text-gray-600 hover:bg-gray-50 hover:text-orange-600'
                    }`}
                    data-testid="upvote-button"
                  >
                    {isUpvoting ? (
                      <div className="w-4 h-4 border-2 border-gray-300 border-t-orange-600 rounded-full animate-spin" />
                    ) : isUpvoted ? (
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path fillRule="evenodd" d="M11.47 2.47a.75.75 0 011.06 0l7.5 7.5a.75.75 0 11-1.06 1.06l-6.22-6.22V21a.75.75 0 01-1.5 0V4.81l-6.22 6.22a.75.75 0 01-1.06-1.06l7.5-7.5z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={1.5}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 10.5L12 3m0 0l7.5 7.5M12 3v18" />
                      </svg>
                    )}
                    <span className="text-sm font-medium">
                      {isUpvoted ? 'Upvoted' : 'Upvote'}
                    </span>
                    {recipe.upvote_count > 0 && (
                      <span className="text-sm font-medium">{recipe.upvote_count}</span>
                    )}
                  </button>

                  {/* Comment Action - Using HeroIcons like TimelineActions */}
                  <button
                    onClick={() => setShowComments(!showComments)}
                    disabled={!user}
                    className="flex items-center space-x-2 text-gray-600 hover:bg-gray-50 hover:text-blue-600 px-3 py-1.5 rounded-full transition-all duration-200"
                    data-testid="comment-button"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={1.5}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 01-2.555-.337A5.972 5.972 0 015.41 20.97a5.969 5.969 0 01-.474-.065 4.48 4.48 0 00.978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25z" />
                    </svg>
                    <span className="text-sm font-medium hidden md:inline">Comment</span>
                    {commentCount > 0 && <span className="text-sm font-medium">{commentCount}</span>}
                  </button>

                  {/* Bookmark Action - Using HeroIcons like TimelineActions */}
                  <button
                    onClick={handleBookmarkClick}
                    disabled={!user || isBookmarking}
                    className={`flex items-center space-x-2 px-3 py-1.5 rounded-full transition-all duration-200 ${
                      isBookmarked 
                        ? 'text-green-600 hover:bg-green-50' 
                        : 'text-gray-600 hover:bg-gray-50 hover:text-green-600'
                    }`}
                    data-testid="bookmark-button"
                  >
                    {isBookmarking ? (
                      <div className="w-4 h-4 border-2 border-gray-300 border-t-green-600 rounded-full animate-spin" />
                    ) : isBookmarked ? (
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path fillRule="evenodd" d="M6.32 2.577a49.255 49.255 0 0111.36 0c1.497.174 2.57 1.46 2.57 2.93V21a.75.75 0 01-1.085.67L12 18.089l-7.165 3.583A.75.75 0 013.75 21V5.507c0-1.47 1.073-2.756 2.57-2.93z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={1.5}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0111.186 0z" />
                      </svg>
                    )}
                    <span className="text-sm font-medium hidden md:inline">Bookmark</span>
                  </button>
                </div>
              </div>

            </div>
          </div>

          {/* Comments Section - Full Width, Outside Timeline Item */}
          <div id="comments" className="px-4 pb-4">
            <CommentList
              recipeId={recipe.id}
              currentUser={user}
              initialCommentCount={recipe.comment_count || 0}
              isOpen={true}
              onCommentCountChange={setCommentCount}
              onAuthRequired={handleAuthRequired}
            />
          </div>
        </div>

        {/* Auth Modal - Using proper AuthModal component */}
        {(() => {
          const AuthModal = require('../../components/auth/AuthModal').default
          return (
            <AuthModal
              isOpen={authModalOpen}
              onClose={handleAuthModalClose}
              initialMode={authMode}
              onSuccess={handleAuthSuccess}
            />
          )
        })()}
      </main>
    </>
  )
}
