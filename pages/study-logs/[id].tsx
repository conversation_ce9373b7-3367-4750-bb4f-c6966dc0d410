import Head from 'next/head'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { HiOutlineArrowLeft } from 'react-icons/hi2'
import StudyLogTimeline from '../../components/feed/StudyLogTimeline'
import { StudyLogWithRecipe } from '../../lib/api/studyLogs'
import { useAuth } from '../../lib/auth'

export default function StudyLogDetailPage() {
  const router = useRouter()
  const { id } = router.query
  const { user } = useAuth()
  
  const [log, setLog] = useState<StudyLogWithRecipe | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [authModalOpen, setAuthModalOpen] = useState(false)
  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null)

  useEffect(() => {
    if (id && typeof id === 'string') {
      loadStudyLog(id)
    }
  }, [id])

  // Handle anchor link to comments section
  useEffect(() => {
    if (router.asPath.includes('#comments') && log) {
      setTimeout(() => {
        const commentsElement = document.getElementById('comments')
        if (commentsElement) {
          commentsElement.scrollIntoView({ behavior: 'smooth' })
        }
      }, 100)
    }
  }, [router.asPath, log])

  const loadStudyLog = async (logId: string) => {
    try {
      setLoading(true)
      setError(null)
      
      console.log('🔄 StudyLogDetail: Loading study log:', logId)
      
      // Validate study log ID format
      if (!logId || typeof logId !== 'string' || logId.trim() === '') {
        throw new Error('Invalid study log ID')
      }
      
      // Additional validation for UUID format
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
      if (!uuidRegex.test(logId.trim())) {
        throw new Error('Invalid study log ID format')
      }
      
      // Fetch study log data from API
      const response = await fetch(`/api/study-logs/${encodeURIComponent(logId)}`)
      
      if (!response.ok) {
        if (response.status === 404) {
          setError('Study log not found')
          return
        }
        if (response.status >= 500) {
          throw new Error('Server error occurred. Please try again later.')
        }
        throw new Error(`Failed to load study log (${response.status})`)
      }
      
      const logData = await response.json()
      
      // Validate response data structure
      if (!logData || typeof logData !== 'object') {
        throw new Error('Invalid study log data received')
      }
      
      if (!logData.id || !logData.title) {
        throw new Error('Incomplete study log data received')
      }
      
      console.log('✅ StudyLogDetail: Study log loaded:', logData)
      
      setLog(logData)
      
    } catch (err) {
      console.error('❌ StudyLogDetail: Error loading study log:', err)
      
      // Enhanced error handling with specific error messages
      if (err instanceof Error) {
        setError(err.message)
      } else {
        setError('An unexpected error occurred. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  const handleAuthRequired = (action: () => void) => {
    setPendingAction(() => action)
    setAuthModalOpen(true)
  }

  const handleAuthModalClose = () => {
    setAuthModalOpen(false)
    setPendingAction(null)
  }

  const handleAuthSuccess = () => {
    setAuthModalOpen(false)
    if (pendingAction) {
      setTimeout(() => {
        pendingAction()
        setPendingAction(null)
      }, 100)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="w-8 h-8 border-2 border-gray-300 border-t-black rounded-full animate-spin" />
      </div>
    )
  }

  if (error || !log) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-black mb-2">Study Log Not Found</h1>
          <p className="text-gray-600 mb-4">{error || 'The study log you are looking for does not exist.'}</p>
          <button
            onClick={() => router.push('/')}
            className="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors"
          >
            Go Home
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>{log.title} - StudyShare</title>
        <meta name="description" content={`Study log: ${log.title}`} />
      </Head>

      <main className="min-h-screen bg-white">
        {/* Timeline Container - Same as HomePage */}
        <div className="timeline-container">
          {/* Header - Always sticky, same as HomePage */}
          <div className="bg-white sticky top-0 z-50">
            <div className="px-4 py-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => router.back()}
                  className="text-gray-600 hover:text-black transition-colors"
                >
                  <HiOutlineArrowLeft className="w-5 h-5" />
                </button>
                <div>
                  <div className="font-bold text-lg">{log.title}</div>
                  <div className="text-sm text-gray-500">@{log.author.username}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Use StudyLogTimeline Component in Detail Page Mode */}
          <StudyLogTimeline
            log={log}
            currentUser={user}
            showExperienceGained={true}
            onAuthRequired={handleAuthRequired}
            isDetailPage={true}
          />

          {/* Comments Section - Full Width, Outside Timeline Item */}
          <div id="comments" className="px-4 pb-4">
            {/* Comments are now handled by StudyLogTimeline component */}
          </div>
        </div>

        {/* Auth Modal - Using proper AuthModal component */}
        {(() => {
          const AuthModal = require('../../components/auth/AuthModal').default
          return (
            <AuthModal
              isOpen={authModalOpen}
              onClose={handleAuthModalClose}
              initialMode="signin"
              onSuccess={handleAuthSuccess}
            />
          )
        })()}
      </main>
    </>
  )
}
