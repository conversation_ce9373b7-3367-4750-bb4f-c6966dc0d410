import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { supabase } from '../../lib/supabase'

export default function AuthCallback() {
  const router = useRouter()
  const [isProcessing, setIsProcessing] = useState(true)

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get redirect destination from URL params or default to home
        const redirectTo = router.query.redirect_to as string || '/'
        
        // Handle the auth callback with session exchange
        const { data, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Auth callback error:', error)
          // Redirect with specific error context
          const errorUrl = `/?error=auth_error&message=${encodeURIComponent(error.message)}`
          router.replace(errorUrl)
          return
        }

        if (data.session) {
          // Successful authentication - redirect to intended destination
          console.log('Authentication successful, redirecting to:', redirectTo)
          router.replace(redirectTo)
        } else {
          // No session found - redirect to home with info
          console.log('No session found, redirecting to home')
          router.replace('/?info=no_session')
        }
      } catch (error) {
        console.error('Unexpected error in auth callback:', error)
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        router.replace(`/?error=unexpected_error&message=${encodeURIComponent(errorMessage)}`)
      } finally {
        setIsProcessing(false)
      }
    }

    // Only run if router is ready to avoid hydration issues
    if (router.isReady) {
      handleAuthCallback()
    }
  }, [router, router.isReady])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-4"></div>
        <p className="text-gray-600">Completing authentication...</p>
      </div>
    </div>
  )
}
