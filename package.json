{"name": "study-share", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:staging": "NODE_ENV=staging next dev", "dev:production": "NODE_ENV=production next dev", "build": "next build", "build:staging": "NODE_ENV=staging next build", "build:production": "NODE_ENV=production next build", "type-check": "tsc --noEmit", "start": "next start", "start:staging": "NODE_ENV=staging next start", "start:production": "NODE_ENV=production next start", "lint": "next lint", "setup:env": "node scripts/setup-environment.js", "setup:dev": "cp .env.example .env.local && echo 'Please update .env.local with your development Supabase credentials'", "check:env": "node -e \"console.log('Environment:', process.env.NODE_ENV || 'development'); console.log('App Env:', process.env.NEXT_PUBLIC_APP_ENV || 'not set'); console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL || 'not set');\"", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:restart": "supabase stop && supabase start", "supabase:reset": "supabase db reset", "supabase:studio": "open http://localhost:54323", "supabase:status": "supabase status", "dev:local": "supabase start && npm run dev"}, "engines": {"node": ">=18.17.0"}, "dependencies": {"@sentry/nextjs": "^8.42.0", "@supabase/auth-helpers-nextjs": "^0.8.1", "@supabase/supabase-js": "^2.38.4", "@types/node": "^20.19.1", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "autoprefixer": "^10", "dotenv": "^16.5.0", "next": "^14.2.30", "postcss": "^8", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.4.1", "react-icons": "^4.11.0", "tailwindcss": "^3", "typescript": "^5.8.3"}, "devDependencies": {"@types/dotenv": "^6.1.1", "eslint": "^8", "eslint-config-next": "14.0.0"}}