@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for StudyShare monochrome design */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-white text-black antialiased;
  }
}

@layer components {
  /* Button styles */
  .btn-primary {
    @apply bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 transition-colors;
  }
  
  .btn-secondary {
    @apply bg-white text-black border border-black px-4 py-2 rounded-md hover:bg-gray-50 transition-colors;
  }
  
  /* Card styles */
  .card {
    @apply bg-white border border-gray-200 rounded-lg p-6 shadow-sm;
  }
  
  /* Input styles */
  .input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent;
  }

  /* Ensure number input spinners are visible and functional */
  input[type="number"] {
    -moz-appearance: textfield;
  }
  
  /* Force webkit spinners to be visible and functional */
  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: auto !important;
    margin: 0;
    opacity: 1 !important;
    cursor: pointer;
    height: auto;
    width: auto;
  }
  
  /* Make spinners more visible on hover and focus */
  input[type="number"]:hover::-webkit-outer-spin-button,
  input[type="number"]:hover::-webkit-inner-spin-button,
  input[type="number"]:focus::-webkit-outer-spin-button,
  input[type="number"]:focus::-webkit-inner-spin-button {
    opacity: 1 !important;
  }
  
  /* Ensure spinners work in duration fields specifically */
  #duration-hours::-webkit-outer-spin-button,
  #duration-hours::-webkit-inner-spin-button,
  #duration-minutes::-webkit-outer-spin-button,
  #duration-minutes::-webkit-inner-spin-button,
  #duration-seconds::-webkit-outer-spin-button,
  #duration-seconds::-webkit-inner-spin-button {
    -webkit-appearance: auto !important;
    opacity: 1 !important;
    cursor: pointer;
  }
  
  /* Feed item styles */
  .feed-item {
    @apply bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow;
  }

  /* Tag styles */
  .tag {
    @apply px-2 py-1 bg-gray-100 text-gray-700 text-sm rounded-md hover:bg-gray-200 cursor-pointer transition-colors;
  }

  /* Action button styles */
  .action-btn {
    @apply flex items-center space-x-1 px-3 py-1.5 rounded-md text-sm font-medium transition-colors hover:bg-gray-100 text-gray-700;
  }

  .action-btn:disabled {
    @apply text-gray-400 cursor-not-allowed hover:bg-transparent;
  }

  /* Study Materials Table */
  .materials-table {
    @apply w-full border-collapse;
  }

  .materials-table td {
    @apply py-2 px-3 border-b border-gray-100 last:border-b-0;
  }

  .materials-table tr:hover {
    @apply bg-gray-50;
  }

  /* Section Headers */
  .section-header {
    @apply font-medium mb-2 pb-1 border-b-2 border-orange-400 inline-block text-gray-900;
  }

  /* Timeline Styles - Twitter-like design */
  .timeline-container {
    @apply max-w-2xl mx-auto min-h-screen bg-white;
  }

  /* Timeline Tabs - Connected to header */
  .timeline-tabs {
    @apply bg-white border-b border-gray-200;
  }

  /* Hide scrollbar for horizontal scroll */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }

  /* Header + tabs container - No sticky behavior */
  .header-tabs-container {
    /* Tabs scroll naturally with content */
  }

  /* Mobile-first responsive design */
  @media (min-width: 768px) {
    .timeline-container {
      @apply border-l border-r border-gray-200;
    }
  }

  .timeline-tab {
    @apply flex-1 min-w-max px-4 py-4 text-center font-medium transition-colors relative text-gray-600 hover:text-black hover:bg-gray-50;
  }

  .timeline-tab.active {
    @apply text-black font-semibold;
  }

  .timeline-tab.active::after {
    @apply absolute bottom-0 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-black rounded-full;
    content: '';
  }

  /* Timeline Items */
  .timeline-item {
    @apply flex space-x-3 p-4 border-b border-gray-100 hover:bg-gray-50/50 transition-colors;
  }

  .timeline-avatar {
    @apply w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center flex-shrink-0;
  }

  .timeline-content {
    @apply flex-1 min-w-0;
  }

  .timeline-header {
    @apply flex items-center space-x-2 mb-2 flex-wrap;
  }

  .timeline-username {
    @apply font-bold text-gray-900 text-sm;
  }

  .timeline-handle {
    @apply text-gray-500 text-sm;
  }

  .timeline-time {
    @apply text-gray-500 text-sm;
  }

  .timeline-main-content {
    @apply mb-3;
  }

  /* Timeline Actions */
  .timeline-actions {
    @apply flex flex-col space-y-4 mt-3 w-full;
  }

  .timeline-action {
    @apply flex items-center space-x-1 text-gray-500 hover:text-gray-700 transition-colors p-2 rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent disabled:hover:text-gray-500;
  }

  /* Mobile responsive adjustments */
  @media (max-width: 768px) {
    .timeline-container {
      @apply border-l-0 border-r-0;
    }
    
    .timeline-item {
      @apply px-3 py-3;
    }
    
    .timeline-avatar {
      @apply w-8 h-8;
    }
    
    .timeline-actions {
      @apply space-x-6;
    }
    
    .timeline-action {
      @apply p-1;
    }
  }

  @media (min-width: 769px) {
    .timeline-avatar {
      @apply w-12 h-12;
    }
  }
}
