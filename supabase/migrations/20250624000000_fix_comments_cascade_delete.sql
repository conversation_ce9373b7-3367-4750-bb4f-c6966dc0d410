-- Fix Comments Table Cascade Delete
-- This migration adds proper cascade delete constraints to the comments table

-- First, drop the existing foreign key constraint for parent_id
ALTER TABLE public.comments 
DROP CONSTRAINT IF EXISTS comments_parent_id_fkey;

-- Add the foreign key constraint with CASCADE DELETE
ALTER TABLE public.comments 
ADD CONSTRAINT comments_parent_id_fkey 
FOREIGN KEY (parent_id) 
REFERENCES public.comments(id) 
ON DELETE CASCADE;

-- Also ensure other foreign keys have proper cascade behavior
-- Drop and recreate recipe_id constraint with CASCADE
ALTER TABLE public.comments 
DROP CONSTRAINT IF EXISTS comments_recipe_id_fkey;

ALTER TABLE public.comments 
ADD CONSTRAINT comments_recipe_id_fkey 
FOREIGN KEY (recipe_id) 
REFERENCES public.study_recipes(id) 
ON DELETE CASCADE;

-- Drop and recreate log_id constraint with CASCADE
ALTER TABLE public.comments 
DROP CONSTRAINT IF EXISTS comments_log_id_fkey;

ALTER TABLE public.comments 
ADD CONSTRAINT comments_log_id_fkey 
FOREIGN KEY (log_id) 
REFERENCES public.study_logs(id) 
ON DELETE CASCADE;

-- Drop and recreate user_id constraint with CASCADE
ALTER TABLE public.comments 
DROP CONSTRAINT IF EXISTS comments_user_id_fkey;

ALTER TABLE public.comments 
ADD CONSTRAINT comments_user_id_fkey 
FOREIGN KEY (user_id) 
REFERENCES public.profiles(id) 
ON DELETE CASCADE;

-- Create an index on parent_id for better performance
CREATE INDEX IF NOT EXISTS idx_comments_parent_id ON public.comments(parent_id);

-- Create an index on recipe_id for better performance
CREATE INDEX IF NOT EXISTS idx_comments_recipe_id ON public.comments(recipe_id);

-- Create an index on log_id for better performance
CREATE INDEX IF NOT EXISTS idx_comments_log_id ON public.comments(log_id);

-- Create an index on user_id for better performance
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON public.comments(user_id);
