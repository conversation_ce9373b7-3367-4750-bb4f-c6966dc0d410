alter table "public"."study_logs" drop column "duration_seconds";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.refresh_leaderboard()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$ BEGIN REFRESH MATERIALIZED VIEW public.leaderboard; END; $function$
;

CREATE OR REPLACE FUNCTION public.update_user_stats_on_study_log()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- Update user stats when a study log is created
  INSERT INTO public.user_stats (user_id, total_study_sessions, last_study_date)
  VALUES (NEW.user_id, 1, NEW.completed_at::date)
  ON CONFLICT (user_id) DO UPDATE SET
    total_study_sessions = user_stats.total_study_sessions + 1,
    last_study_date = NEW.completed_at::date,
    updated_at = NOW();
  
  -- Update total study time in profiles
  UPDATE public.profiles 
  SET total_study_time = total_study_time + NEW.duration_minutes,
      updated_at = NOW()
  WHERE id = NEW.user_id;
  
  RETURN NEW;
END;
$function$
;


