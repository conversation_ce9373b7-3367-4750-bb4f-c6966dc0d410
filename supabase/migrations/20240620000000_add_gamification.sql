-- Gamification Database Schema Extensions for StudyShare

-- First, add missing columns to existing profiles table
ALTER TABLE public.profiles
ADD COLUMN IF NOT EXISTS rank_level INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS total_study_time INTEGER DEFAULT 0;

-- User Statistics Table (for detailed tracking)
CREATE TABLE public.user_stats (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) NOT NULL UNIQUE,
  
  -- Study Metrics
  total_study_sessions INTEGER DEFAULT 0,
  current_streak_days INTEGER DEFAULT 0,
  longest_streak_days INTEGER DEFAULT 0,
  last_study_date DATE,
  
  -- Community Metrics
  recipes_created INTEGER DEFAULT 0,
  total_upvotes_received INTEGER DEFAULT 0,
  total_comments_received INTEGER DEFAULT 0,
  total_bookmarks_received INTEGER DEFAULT 0,
  
  -- Engagement Metrics
  upvotes_given INTEGER DEFAULT 0,
  comments_made INTEGER DEFAULT 0,
  bookmarks_made INTEGER DEFAULT 0,
  
  -- Experience Points
  total_experience_points INTEGER DEFAULT 0,
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Achievements Table
CREATE TABLE public.achievements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT NOT NULL,
  icon TEXT NOT NULL, -- emoji or icon identifier
  category TEXT NOT NULL, -- 'study', 'community', 'consistency', 'milestone'
  requirement_type TEXT NOT NULL, -- 'study_time', 'streak', 'upvotes', 'recipes', etc.
  requirement_value INTEGER NOT NULL,
  points_reward INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User Achievements Table (many-to-many)
CREATE TABLE public.user_achievements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) NOT NULL,
  achievement_id UUID REFERENCES public.achievements(id) NOT NULL,
  earned_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, achievement_id)
);

-- Level System Table
CREATE TABLE public.levels (
  level INTEGER PRIMARY KEY,
  name TEXT NOT NULL,
  min_experience_points INTEGER NOT NULL,
  max_experience_points INTEGER,
  badge_color TEXT DEFAULT '#000000', -- hex color for badge
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Activity Log Table (for detailed tracking)
CREATE TABLE public.activity_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) NOT NULL,
  activity_type TEXT NOT NULL, -- 'study_session', 'recipe_created', 'upvote_received', etc.
  points_earned INTEGER DEFAULT 0,
  reference_id UUID, -- ID of related entity (recipe_id, log_id, etc.)
  metadata JSONB DEFAULT '{}', -- additional data
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Leaderboard View (materialized for performance)
CREATE MATERIALIZED VIEW public.leaderboard AS
SELECT
  p.id,
  p.username,
  p.display_name,
  p.avatar_url,
  p.rank_level,
  p.total_study_time,
  COALESCE(us.total_experience_points, 0) as total_experience_points,
  COALESCE(us.current_streak_days, 0) as current_streak_days,
  COALESCE(us.total_study_sessions, 0) as total_study_sessions,
  COALESCE(us.recipes_created, 0) as recipes_created,
  COALESCE(us.total_upvotes_received, 0) as total_upvotes_received,
  RANK() OVER (ORDER BY COALESCE(us.total_experience_points, 0) DESC) as global_rank
FROM public.profiles p
LEFT JOIN public.user_stats us ON p.id = us.user_id
ORDER BY COALESCE(us.total_experience_points, 0) DESC;

-- Function to refresh leaderboard
CREATE OR REPLACE FUNCTION refresh_leaderboard()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW public.leaderboard;
END;
$$ LANGUAGE plpgsql;

-- Indexes for performance
CREATE INDEX idx_user_stats_user_id ON public.user_stats(user_id);
CREATE INDEX idx_user_stats_experience ON public.user_stats(total_experience_points DESC);
CREATE INDEX idx_user_achievements_user_id ON public.user_achievements(user_id);
CREATE INDEX idx_activity_log_user_id ON public.activity_log(user_id);
CREATE INDEX idx_activity_log_created_at ON public.activity_log(created_at DESC);

-- RLS Policies
ALTER TABLE public.user_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.levels ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activity_log ENABLE ROW LEVEL SECURITY;

-- User Stats Policies
CREATE POLICY "User stats are viewable by everyone" ON public.user_stats
  FOR SELECT USING (true);

CREATE POLICY "Users can update their own stats" ON public.user_stats
  FOR UPDATE USING (auth.uid() = user_id);

-- Achievements Policies
CREATE POLICY "Achievements are viewable by everyone" ON public.achievements
  FOR SELECT USING (true);

-- User Achievements Policies
CREATE POLICY "User achievements are viewable by everyone" ON public.user_achievements
  FOR SELECT USING (true);

CREATE POLICY "Users can insert their own achievements" ON public.user_achievements
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Levels Policies
CREATE POLICY "Levels are viewable by everyone" ON public.levels
  FOR SELECT USING (true);

-- Activity Log Policies
CREATE POLICY "Users can view their own activity log" ON public.activity_log
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own activity" ON public.activity_log
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Functions for automatic updates
CREATE OR REPLACE FUNCTION update_user_stats_on_study_log()
RETURNS TRIGGER AS $$
BEGIN
  -- Update user stats when a study log is created
  INSERT INTO public.user_stats (user_id, total_study_sessions, last_study_date)
  VALUES (NEW.user_id, 1, NEW.completed_at::date)
  ON CONFLICT (user_id) DO UPDATE SET
    total_study_sessions = user_stats.total_study_sessions + 1,
    last_study_date = NEW.completed_at::date,
    updated_at = NOW();
  
  -- Update total study time in profiles
  UPDATE public.profiles 
  SET total_study_time = total_study_time + NEW.duration_minutes,
      updated_at = NOW()
  WHERE id = NEW.user_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_user_stats_on_study_log
  AFTER INSERT ON public.study_logs
  FOR EACH ROW EXECUTE FUNCTION update_user_stats_on_study_log();
