-- Add material_id column to study_logs table
-- This allows study logs to reference specific materials from study recipes

-- Add the material_id column (nullable for backward compatibility)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'study_logs' 
    AND column_name = 'material_id'
  ) THEN
    ALTER TABLE public.study_logs 
    ADD COLUMN material_id UUID REFERENCES public.study_materials(id) ON DELETE SET NULL;
  END IF;
END $$;

-- Add index for better query performance
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'public' 
    AND tablename = 'study_logs' 
    AND indexname = 'idx_study_logs_material_id'
  ) THEN
    CREATE INDEX idx_study_logs_material_id ON public.study_logs(material_id);
  END IF;
END $$;

-- Add comment for documentation
COMMENT ON COLUMN public.study_logs.material_id IS 'References the specific study material that was studied in this session';

-- Update RLS policy to include material_id in queries (no changes needed as existing policies cover this)
