-- Add duration_seconds column to study_logs table with default value
ALTER TABLE public.study_logs 
ADD COLUMN duration_seconds INTEGER DEFAULT 0;

-- Update existing records to convert duration_minutes to duration_seconds
UPDATE public.study_logs 
SET duration_seconds = duration_minutes * 60 
WHERE duration_seconds = 0 OR duration_seconds IS NULL;

-- Make duration_seconds NOT NULL after populating existing data
ALTER TABLE public.study_logs 
ALTER COLUMN duration_seconds SET NOT NULL;

-- Remove default value after migration
ALTER TABLE public.study_logs 
ALTER COLUMN duration_seconds DROP DEFAULT;

-- Add comment for documentation
COMMENT ON COLUMN public.study_logs.duration_seconds IS 'Study duration in seconds for precise time tracking';
