drop policy "Public logs are viewable by everyone" on "public"."study_logs";

drop policy "Public recipes are viewable by everyone" on "public"."study_recipes";

create table "public"."active_study_sessions" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid not null,
    "room_id" uuid,
    "started_at" timestamp with time zone default now(),
    "estimated_end_at" timestamp with time zone,
    "activity_type" text default 'studying'::text,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."active_study_sessions" enable row level security;

create table "public"."room_activity_stats" (
    "id" uuid not null default gen_random_uuid(),
    "room_id" uuid not null,
    "user_id" uuid not null,
    "date" date not null,
    "total_study_minutes" integer default 0,
    "session_count" integer default 0,
    "recipes_created" integer default 0,
    "logs_created" integer default 0,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."room_activity_stats" enable row level security;

create table "public"."room_creation_permissions" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid not null,
    "granted_by" uuid not null,
    "granted_at" timestamp with time zone default now()
);


create table "public"."room_members" (
    "id" uuid not null default gen_random_uuid(),
    "room_id" uuid,
    "user_id" uuid not null,
    "role" text default 'member'::text,
    "joined_at" timestamp with time zone default now()
);


alter table "public"."room_members" enable row level security;

create table "public"."rooms" (
    "id" uuid not null default gen_random_uuid(),
    "name" text not null,
    "description" text,
    "owner_id" uuid not null,
    "is_active" boolean default true,
    "member_count" integer default 0,
    "created_at" timestamp with time zone default now()
);


alter table "public"."rooms" enable row level security;

alter table "public"."study_logs" add column "room_id" uuid;

alter table "public"."study_recipes" add column "room_id" uuid;

CREATE UNIQUE INDEX active_study_sessions_pkey ON public.active_study_sessions USING btree (id);

CREATE INDEX idx_active_study_sessions_activity_type ON public.active_study_sessions USING btree (activity_type);

CREATE INDEX idx_active_study_sessions_room_id ON public.active_study_sessions USING btree (room_id);

CREATE INDEX idx_active_study_sessions_user_id ON public.active_study_sessions USING btree (user_id);

CREATE INDEX idx_room_activity_stats_date ON public.room_activity_stats USING btree (date);

CREATE INDEX idx_room_activity_stats_room_id ON public.room_activity_stats USING btree (room_id);

CREATE INDEX idx_room_activity_stats_room_user_date ON public.room_activity_stats USING btree (room_id, user_id, date);

CREATE INDEX idx_room_activity_stats_user_id ON public.room_activity_stats USING btree (user_id);

CREATE INDEX idx_room_members_room_id ON public.room_members USING btree (room_id);

CREATE INDEX idx_room_members_user_id ON public.room_members USING btree (user_id);

CREATE INDEX idx_rooms_is_active ON public.rooms USING btree (is_active);

CREATE INDEX idx_rooms_owner_id ON public.rooms USING btree (owner_id);

CREATE INDEX idx_study_logs_room_id ON public.study_logs USING btree (room_id);

CREATE INDEX idx_study_recipes_room_id ON public.study_recipes USING btree (room_id);

CREATE UNIQUE INDEX room_activity_stats_pkey ON public.room_activity_stats USING btree (id);

CREATE UNIQUE INDEX room_activity_stats_room_id_user_id_date_key ON public.room_activity_stats USING btree (room_id, user_id, date);

CREATE UNIQUE INDEX room_creation_permissions_pkey ON public.room_creation_permissions USING btree (id);

CREATE UNIQUE INDEX room_creation_permissions_user_id_key ON public.room_creation_permissions USING btree (user_id);

CREATE UNIQUE INDEX room_members_pkey ON public.room_members USING btree (id);

CREATE UNIQUE INDEX room_members_room_id_user_id_key ON public.room_members USING btree (room_id, user_id);

CREATE UNIQUE INDEX rooms_pkey ON public.rooms USING btree (id);

alter table "public"."active_study_sessions" add constraint "active_study_sessions_pkey" PRIMARY KEY using index "active_study_sessions_pkey";

alter table "public"."room_activity_stats" add constraint "room_activity_stats_pkey" PRIMARY KEY using index "room_activity_stats_pkey";

alter table "public"."room_creation_permissions" add constraint "room_creation_permissions_pkey" PRIMARY KEY using index "room_creation_permissions_pkey";

alter table "public"."room_members" add constraint "room_members_pkey" PRIMARY KEY using index "room_members_pkey";

alter table "public"."rooms" add constraint "rooms_pkey" PRIMARY KEY using index "rooms_pkey";

alter table "public"."active_study_sessions" add constraint "active_study_sessions_activity_type_check" CHECK ((activity_type = ANY (ARRAY['studying'::text, 'break'::text, 'completed'::text]))) not valid;

alter table "public"."active_study_sessions" validate constraint "active_study_sessions_activity_type_check";

alter table "public"."active_study_sessions" add constraint "active_study_sessions_room_id_fkey" FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE not valid;

alter table "public"."active_study_sessions" validate constraint "active_study_sessions_room_id_fkey";

alter table "public"."active_study_sessions" add constraint "active_study_sessions_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE not valid;

alter table "public"."active_study_sessions" validate constraint "active_study_sessions_user_id_fkey";

alter table "public"."room_activity_stats" add constraint "room_activity_stats_room_id_fkey" FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE not valid;

alter table "public"."room_activity_stats" validate constraint "room_activity_stats_room_id_fkey";

alter table "public"."room_activity_stats" add constraint "room_activity_stats_room_id_user_id_date_key" UNIQUE using index "room_activity_stats_room_id_user_id_date_key";

alter table "public"."room_activity_stats" add constraint "room_activity_stats_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE not valid;

alter table "public"."room_activity_stats" validate constraint "room_activity_stats_user_id_fkey";

alter table "public"."room_creation_permissions" add constraint "room_creation_permissions_granted_by_fkey" FOREIGN KEY (granted_by) REFERENCES profiles(id) not valid;

alter table "public"."room_creation_permissions" validate constraint "room_creation_permissions_granted_by_fkey";

alter table "public"."room_creation_permissions" add constraint "room_creation_permissions_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(id) not valid;

alter table "public"."room_creation_permissions" validate constraint "room_creation_permissions_user_id_fkey";

alter table "public"."room_creation_permissions" add constraint "room_creation_permissions_user_id_key" UNIQUE using index "room_creation_permissions_user_id_key";

alter table "public"."room_members" add constraint "room_members_role_check" CHECK ((role = ANY (ARRAY['owner'::text, 'member'::text]))) not valid;

alter table "public"."room_members" validate constraint "room_members_role_check";

alter table "public"."room_members" add constraint "room_members_room_id_fkey" FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE not valid;

alter table "public"."room_members" validate constraint "room_members_room_id_fkey";

alter table "public"."room_members" add constraint "room_members_room_id_user_id_key" UNIQUE using index "room_members_room_id_user_id_key";

alter table "public"."room_members" add constraint "room_members_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(id) not valid;

alter table "public"."room_members" validate constraint "room_members_user_id_fkey";

alter table "public"."rooms" add constraint "rooms_owner_id_fkey" FOREIGN KEY (owner_id) REFERENCES profiles(id) not valid;

alter table "public"."rooms" validate constraint "rooms_owner_id_fkey";

alter table "public"."study_logs" add constraint "study_logs_room_id_fkey" FOREIGN KEY (room_id) REFERENCES rooms(id) not valid;

alter table "public"."study_logs" validate constraint "study_logs_room_id_fkey";

alter table "public"."study_recipes" add constraint "study_recipes_room_id_fkey" FOREIGN KEY (room_id) REFERENCES rooms(id) not valid;

alter table "public"."study_recipes" validate constraint "study_recipes_room_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.add_room_owner_as_member()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  INSERT INTO public.room_members (room_id, user_id, role)
  VALUES (NEW.id, NEW.owner_id, 'owner');
  RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.cleanup_old_active_sessions()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  -- 24時間以上古いセッションを削除
  DELETE FROM public.active_study_sessions 
  WHERE created_at < NOW() - INTERVAL '24 hours';
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_room_activity_stats(p_room_id uuid, p_user_id uuid, p_study_minutes integer DEFAULT 0, p_session_increment integer DEFAULT 0, p_recipe_increment integer DEFAULT 0, p_log_increment integer DEFAULT 0)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  INSERT INTO public.room_activity_stats (
    room_id, user_id, date, total_study_minutes, session_count, recipes_created, logs_created
  ) VALUES (
    p_room_id, p_user_id, CURRENT_DATE, p_study_minutes, p_session_increment, p_recipe_increment, p_log_increment
  )
  ON CONFLICT (room_id, user_id, date)
  DO UPDATE SET
    total_study_minutes = room_activity_stats.total_study_minutes + p_study_minutes,
    session_count = room_activity_stats.session_count + p_session_increment,
    recipes_created = room_activity_stats.recipes_created + p_recipe_increment,
    logs_created = room_activity_stats.logs_created + p_log_increment,
    updated_at = NOW();
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_room_member_count()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE public.rooms 
    SET member_count = member_count + 1,
        updated_at = NOW()
    WHERE id = NEW.room_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE public.rooms 
    SET member_count = member_count - 1,
        updated_at = NOW()
    WHERE id = OLD.room_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_updated_at_column()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$function$
;

grant delete on table "public"."active_study_sessions" to "anon";

grant insert on table "public"."active_study_sessions" to "anon";

grant references on table "public"."active_study_sessions" to "anon";

grant select on table "public"."active_study_sessions" to "anon";

grant trigger on table "public"."active_study_sessions" to "anon";

grant truncate on table "public"."active_study_sessions" to "anon";

grant update on table "public"."active_study_sessions" to "anon";

grant delete on table "public"."active_study_sessions" to "authenticated";

grant insert on table "public"."active_study_sessions" to "authenticated";

grant references on table "public"."active_study_sessions" to "authenticated";

grant select on table "public"."active_study_sessions" to "authenticated";

grant trigger on table "public"."active_study_sessions" to "authenticated";

grant truncate on table "public"."active_study_sessions" to "authenticated";

grant update on table "public"."active_study_sessions" to "authenticated";

grant delete on table "public"."active_study_sessions" to "service_role";

grant insert on table "public"."active_study_sessions" to "service_role";

grant references on table "public"."active_study_sessions" to "service_role";

grant select on table "public"."active_study_sessions" to "service_role";

grant trigger on table "public"."active_study_sessions" to "service_role";

grant truncate on table "public"."active_study_sessions" to "service_role";

grant update on table "public"."active_study_sessions" to "service_role";

grant delete on table "public"."room_activity_stats" to "anon";

grant insert on table "public"."room_activity_stats" to "anon";

grant references on table "public"."room_activity_stats" to "anon";

grant select on table "public"."room_activity_stats" to "anon";

grant trigger on table "public"."room_activity_stats" to "anon";

grant truncate on table "public"."room_activity_stats" to "anon";

grant update on table "public"."room_activity_stats" to "anon";

grant delete on table "public"."room_activity_stats" to "authenticated";

grant insert on table "public"."room_activity_stats" to "authenticated";

grant references on table "public"."room_activity_stats" to "authenticated";

grant select on table "public"."room_activity_stats" to "authenticated";

grant trigger on table "public"."room_activity_stats" to "authenticated";

grant truncate on table "public"."room_activity_stats" to "authenticated";

grant update on table "public"."room_activity_stats" to "authenticated";

grant delete on table "public"."room_activity_stats" to "service_role";

grant insert on table "public"."room_activity_stats" to "service_role";

grant references on table "public"."room_activity_stats" to "service_role";

grant select on table "public"."room_activity_stats" to "service_role";

grant trigger on table "public"."room_activity_stats" to "service_role";

grant truncate on table "public"."room_activity_stats" to "service_role";

grant update on table "public"."room_activity_stats" to "service_role";

grant delete on table "public"."room_creation_permissions" to "anon";

grant insert on table "public"."room_creation_permissions" to "anon";

grant references on table "public"."room_creation_permissions" to "anon";

grant select on table "public"."room_creation_permissions" to "anon";

grant trigger on table "public"."room_creation_permissions" to "anon";

grant truncate on table "public"."room_creation_permissions" to "anon";

grant update on table "public"."room_creation_permissions" to "anon";

grant delete on table "public"."room_creation_permissions" to "authenticated";

grant insert on table "public"."room_creation_permissions" to "authenticated";

grant references on table "public"."room_creation_permissions" to "authenticated";

grant select on table "public"."room_creation_permissions" to "authenticated";

grant trigger on table "public"."room_creation_permissions" to "authenticated";

grant truncate on table "public"."room_creation_permissions" to "authenticated";

grant update on table "public"."room_creation_permissions" to "authenticated";

grant delete on table "public"."room_creation_permissions" to "service_role";

grant insert on table "public"."room_creation_permissions" to "service_role";

grant references on table "public"."room_creation_permissions" to "service_role";

grant select on table "public"."room_creation_permissions" to "service_role";

grant trigger on table "public"."room_creation_permissions" to "service_role";

grant truncate on table "public"."room_creation_permissions" to "service_role";

grant update on table "public"."room_creation_permissions" to "service_role";

grant delete on table "public"."room_members" to "anon";

grant insert on table "public"."room_members" to "anon";

grant references on table "public"."room_members" to "anon";

grant select on table "public"."room_members" to "anon";

grant trigger on table "public"."room_members" to "anon";

grant truncate on table "public"."room_members" to "anon";

grant update on table "public"."room_members" to "anon";

grant delete on table "public"."room_members" to "authenticated";

grant insert on table "public"."room_members" to "authenticated";

grant references on table "public"."room_members" to "authenticated";

grant select on table "public"."room_members" to "authenticated";

grant trigger on table "public"."room_members" to "authenticated";

grant truncate on table "public"."room_members" to "authenticated";

grant update on table "public"."room_members" to "authenticated";

grant delete on table "public"."room_members" to "service_role";

grant insert on table "public"."room_members" to "service_role";

grant references on table "public"."room_members" to "service_role";

grant select on table "public"."room_members" to "service_role";

grant trigger on table "public"."room_members" to "service_role";

grant truncate on table "public"."room_members" to "service_role";

grant update on table "public"."room_members" to "service_role";

grant delete on table "public"."rooms" to "anon";

grant insert on table "public"."rooms" to "anon";

grant references on table "public"."rooms" to "anon";

grant select on table "public"."rooms" to "anon";

grant trigger on table "public"."rooms" to "anon";

grant truncate on table "public"."rooms" to "anon";

grant update on table "public"."rooms" to "anon";

grant delete on table "public"."rooms" to "authenticated";

grant insert on table "public"."rooms" to "authenticated";

grant references on table "public"."rooms" to "authenticated";

grant select on table "public"."rooms" to "authenticated";

grant trigger on table "public"."rooms" to "authenticated";

grant truncate on table "public"."rooms" to "authenticated";

grant update on table "public"."rooms" to "authenticated";

grant delete on table "public"."rooms" to "service_role";

grant insert on table "public"."rooms" to "service_role";

grant references on table "public"."rooms" to "service_role";

grant select on table "public"."rooms" to "service_role";

grant trigger on table "public"."rooms" to "service_role";

grant truncate on table "public"."rooms" to "service_role";

grant update on table "public"."rooms" to "service_role";

create policy "Room members can view active sessions"
on "public"."active_study_sessions"
as permissive
for select
to public
using (((room_id IS NULL) OR (EXISTS ( SELECT 1
   FROM room_members
  WHERE ((room_members.room_id = active_study_sessions.room_id) AND (room_members.user_id = auth.uid()))))));


create policy "Users can manage their own sessions"
on "public"."active_study_sessions"
as permissive
for all
to public
using ((auth.uid() = user_id));


create policy "Room members can view activity stats"
on "public"."room_activity_stats"
as permissive
for select
to public
using ((EXISTS ( SELECT 1
   FROM room_members
  WHERE ((room_members.room_id = room_activity_stats.room_id) AND (room_members.user_id = auth.uid())))));


create policy "Users can insert their own activity stats"
on "public"."room_activity_stats"
as permissive
for insert
to public
with check ((auth.uid() = user_id));


create policy "Users can update their own activity stats"
on "public"."room_activity_stats"
as permissive
for update
to public
using ((auth.uid() = user_id));


create policy "Room creation permissions are viewable by everyone"
on "public"."room_creation_permissions"
as permissive
for select
to public
using (true);


create policy "Room members are viewable by everyone"
on "public"."room_members"
as permissive
for select
to public
using (true);


create policy "Room owners can manage members"
on "public"."room_members"
as permissive
for delete
to public
using ((EXISTS ( SELECT 1
   FROM rooms
  WHERE ((rooms.id = room_members.room_id) AND (rooms.owner_id = auth.uid())))));


create policy "Users can join rooms"
on "public"."room_members"
as permissive
for insert
to public
with check ((auth.uid() = user_id));


create policy "Users can leave rooms"
on "public"."room_members"
as permissive
for delete
to public
using ((auth.uid() = user_id));


create policy "Public rooms are viewable by everyone"
on "public"."rooms"
as permissive
for select
to public
using (true);


create policy "Room owners can delete their rooms"
on "public"."rooms"
as permissive
for delete
to public
using ((auth.uid() = owner_id));


create policy "Room owners can update their rooms"
on "public"."rooms"
as permissive
for update
to public
using ((auth.uid() = owner_id));


create policy "Users can create rooms if they have permission"
on "public"."rooms"
as permissive
for insert
to public
with check (((auth.uid() = owner_id) AND (EXISTS ( SELECT 1
   FROM room_creation_permissions
  WHERE (room_creation_permissions.user_id = auth.uid())))));


create policy "Public logs are viewable by everyone"
on "public"."study_logs"
as permissive
for select
to public
using (((room_id IS NULL) OR (EXISTS ( SELECT 1
   FROM room_members
  WHERE ((room_members.room_id = study_logs.room_id) AND (room_members.user_id = auth.uid()))))));


create policy "Public recipes are viewable by everyone"
on "public"."study_recipes"
as permissive
for select
to public
using (((room_id IS NULL) OR (EXISTS ( SELECT 1
   FROM room_members
  WHERE ((room_members.room_id = study_recipes.room_id) AND (room_members.user_id = auth.uid()))))));


CREATE TRIGGER update_active_study_sessions_updated_at BEFORE UPDATE ON public.active_study_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_room_activity_stats_updated_at BEFORE UPDATE ON public.room_activity_stats FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_room_member_count AFTER INSERT OR DELETE ON public.room_members FOR EACH ROW EXECUTE FUNCTION update_room_member_count();

CREATE TRIGGER trigger_add_room_owner_as_member AFTER INSERT ON public.rooms FOR EACH ROW EXECUTE FUNCTION add_room_owner_as_member();


