-- Fix RLS policies for user_stats table to allow safe INSERT operations

-- Add missing INSERT policy for user_stats table
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'user_stats' 
    AND policyname = 'Users can insert their own stats'
  ) THEN
    CREATE POLICY "Users can insert their own stats" ON public.user_stats
      FOR INSERT WITH CHECK (auth.uid() = user_id);
  END IF;
END $$;

-- Improve the trigger function to handle errors gracefully
CREATE OR REPLACE FUNCTION update_user_stats_on_study_log()
RETURNS TRIGGER AS $$
BEGIN
  -- Update user stats when a study log is created
  -- Use exception handling to prevent failures from affecting study log creation
  BEGIN
    INSERT INTO public.user_stats (user_id, total_study_sessions, last_study_date)
    VALUES (NEW.user_id, 1, NEW.completed_at::date)
    ON CONFLICT (user_id) DO UPDATE SET
      total_study_sessions = user_stats.total_study_sessions + 1,
      last_study_date = NEW.completed_at::date,
      updated_at = NOW();
  EXCEPTION
    WHEN OTHERS THEN
      -- Log the error but don't fail the study log creation
      RAISE WARNING 'Failed to update user_stats for user %: %', NEW.user_id, SQLERRM;
  END;
  
  -- Update total study time in profiles
  -- Also use exception handling for safety
  BEGIN
    UPDATE public.profiles 
    SET total_study_time = total_study_time + NEW.duration_minutes,
        updated_at = NOW()
    WHERE id = NEW.user_id;
  EXCEPTION
    WHEN OTHERS THEN
      -- Log the error but don't fail the study log creation
      RAISE WARNING 'Failed to update profile total_study_time for user %: %', NEW.user_id, SQLERRM;
  END;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
