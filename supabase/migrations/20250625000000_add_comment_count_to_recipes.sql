-- Add comment_count column to study_recipes table
-- Migration: 20250625000000_add_comment_count_to_recipes.sql

-- Step 1: Add comment_count column with default value 0
ALTER TABLE public.study_recipes 
ADD COLUMN comment_count INTEGER DEFAULT 0;

-- Step 2: Update existing data with actual comment counts
UPDATE public.study_recipes 
SET comment_count = (
  SELECT COUNT(*) 
  FROM public.comments 
  WHERE comments.recipe_id = study_recipes.id
);

-- Step 3: Create trigger function to automatically update comment_count
CREATE OR REPLACE FUNCTION update_recipe_comment_count()
RETURNS TRIGGER AS $$
BEGIN
  -- Handle INSERT (new comment added)
  IF TG_OP = 'INSERT' THEN
    -- Only update if the comment is for a recipe (not a study log)
    IF NEW.recipe_id IS NOT NULL THEN
      UPDATE public.study_recipes 
      SET comment_count = comment_count + 1 
      WHERE id = NEW.recipe_id;
    END IF;
    RETURN NEW;
  
  -- Handle DELETE (comment removed)
  ELSIF TG_OP = 'DELETE' THEN
    -- Only update if the comment was for a recipe (not a study log)
    IF OLD.recipe_id IS NOT NULL THEN
      UPDATE public.study_recipes 
      SET comment_count = GREATEST(comment_count - 1, 0)
      WHERE id = OLD.recipe_id;
    END IF;
    RETURN OLD;
  
  -- Handle UPDATE (comment moved between recipe/log)
  ELSIF TG_OP = 'UPDATE' THEN
    -- If recipe_id changed from one recipe to another
    IF OLD.recipe_id IS DISTINCT FROM NEW.recipe_id THEN
      -- Decrease count for old recipe
      IF OLD.recipe_id IS NOT NULL THEN
        UPDATE public.study_recipes 
        SET comment_count = GREATEST(comment_count - 1, 0)
        WHERE id = OLD.recipe_id;
      END IF;
      
      -- Increase count for new recipe
      IF NEW.recipe_id IS NOT NULL THEN
        UPDATE public.study_recipes 
        SET comment_count = comment_count + 1 
        WHERE id = NEW.recipe_id;
      END IF;
    END IF;
    RETURN NEW;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Create trigger on comments table
DROP TRIGGER IF EXISTS recipe_comment_count_trigger ON public.comments;
CREATE TRIGGER recipe_comment_count_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.comments
  FOR EACH ROW
  EXECUTE FUNCTION update_recipe_comment_count();

-- Step 5: Add comment to document the column
COMMENT ON COLUMN public.study_recipes.comment_count IS 'Cached count of comments for this recipe, automatically updated by trigger';
