-- StudyShare Sample Data
-- This file contains sample data for local development

-- Insert sample auth users first
INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at, raw_app_meta_data, raw_user_meta_data, is_super_admin, role) VALUES
  ('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', crypt('password123', gen_salt('bf')), NOW(), NOW(), NOW(), '{"provider":"email","providers":["email"]}', '{}', false, 'authenticated'),
  ('550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', crypt('password123', gen_salt('bf')), NOW(), NOW(), NOW(), '{"provider":"email","providers":["email"]}', '{}', false, 'authenticated'),
  ('550e8400-e29b-41d4-a716-446655440003', '<EMAIL>', crypt('password123', gen_salt('bf')), NOW(), NOW(), NOW(), '{"provider":"email","providers":["email"]}', '{}', false, 'authenticated');

-- Insert sample profiles (users)
INSERT INTO public.profiles (id, username, display_name, bio, rank_level, total_study_time) VALUES
  ('550e8400-e29b-41d4-a716-446655440001', 'study_master', '勉強マスター', '効率的な学習法を研究しています', 5, 1200),
  ('550e8400-e29b-41d4-a716-446655440002', 'code_learner', 'コード学習者', 'プログラミングを学習中です', 3, 800),
  ('550e8400-e29b-41d4-a716-446655440003', 'exam_fighter', '試験戦士', '資格試験に挑戦中', 4, 950);

-- Insert sample study recipes
INSERT INTO public.study_recipes (id, user_id, title, estimated_duration, tips, tags, upvote_count) VALUES
  ('660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 'プログラミング基礎学習法', '2時間', 'コードを書きながら学ぶのが効果的です。実際に手を動かすことで理解が深まります。', ARRAY['プログラミング', '基礎', 'コーディング'], 15),
  ('660e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440002', '英語リスニング強化メソッド', '1時間30分', 'シャドーイングとディクテーションを組み合わせることで効果的にリスニング力を向上させます。', ARRAY['英語', 'リスニング', '語学'], 12),
  ('660e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440003', '数学問題集攻略法', '3時間', '基礎問題から応用問題まで段階的に取り組むことで確実に実力をつけます。', ARRAY['数学', '問題集', '受験'], 8),
  ('660e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440001', 'ポモドーロテクニック活用法', '25分×8セット', '25分集中→5分休憩のサイクルで集中力を維持します。4セット後は長めの休憩を取ります。', ARRAY['集中力', 'ポモドーロ', '時間管理'], 20),
  ('660e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440002', 'アクティブリコール学習法', '1時間', '教科書を読むだけでなく、覚えた内容を思い出す練習を重視した学習法です。', ARRAY['記憶術', '復習', '効率化'], 18);

-- Insert sample study materials
INSERT INTO public.study_materials (recipe_id, material_name, frequency, time_per_session, time_unit, order_index) VALUES
  -- プログラミング基礎学習法の教材
  ('660e8400-e29b-41d4-a716-446655440001', 'JavaScript基礎教材', '毎日', 45, 'minutes', 1),
  ('660e8400-e29b-41d4-a716-446655440001', 'コーディング練習問題', '毎日', 30, 'minutes', 2),
  ('660e8400-e29b-41d4-a716-446655440001', 'プロジェクト作成', '週3回', 45, 'minutes', 3),
  
  -- 英語リスニング強化メソッドの教材
  ('660e8400-e29b-41d4-a716-446655440002', 'TED Talks シャドーイング', '毎日', 30, 'minutes', 1),
  ('660e8400-e29b-41d4-a716-446655440002', 'ディクテーション練習', '毎日', 20, 'minutes', 2),
  ('660e8400-e29b-41d4-a716-446655440002', '英語ニュース聞き取り', '週5回', 40, 'minutes', 3),
  
  -- 数学問題集攻略法の教材
  ('660e8400-e29b-41d4-a716-446655440003', '基礎問題演習', '毎日', 60, 'minutes', 1),
  ('660e8400-e29b-41d4-a716-446655440003', '応用問題チャレンジ', '週3回', 90, 'minutes', 2),
  ('660e8400-e29b-41d4-a716-446655440003', '過去問演習', '週2回', 120, 'minutes', 3);

-- Insert sample study logs
INSERT INTO public.study_logs (user_id, recipe_id, title, duration_minutes, duration_seconds, notes, timer_type) VALUES
  ('550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440001', 'JavaScript変数と関数の学習', 45, 2730, '変数の宣言方法と関数の基本的な書き方を学習しました。', 'countdown'),
  ('550e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440002', 'TED Talksシャドーイング練習', 30, 1830, '発音が難しい部分もありましたが、徐々に慣れてきました。', 'countdown'),
  ('550e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440003', '二次関数の問題演習', 90, 5430, '基礎問題は解けるようになりました。応用問題にも挑戦したいです。', 'stopwatch'),
  ('550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440004', 'ポモドーロで集中学習', 100, 6045, '4セット完了。集中力が持続しました。', 'countdown'),
  ('550e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440005', 'アクティブリコール実践', 60, 3675, '覚えた内容を思い出す練習をしました。記憶の定着が良くなった気がします。', 'countdown');

-- Insert sample votes
INSERT INTO public.votes (user_id, recipe_id) VALUES
  ('550e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440001'),
  ('550e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440001'),
  ('550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440002'),
  ('550e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440004'),
  ('550e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440005');

-- Insert sample bookmarks
INSERT INTO public.bookmarks (user_id, recipe_id) VALUES
  ('550e8400-e29b-41d4-a716-446655440001', '660e8400-e29b-41d4-a716-446655440002'),
  ('550e8400-e29b-41d4-a716-446655440002', '660e8400-e29b-41d4-a716-446655440003'),
  ('550e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440001');

-- Insert sample reactions
INSERT INTO public.reactions (user_id, log_id, emoji) VALUES
  ('550e8400-e29b-41d4-a716-446655440002', (SELECT id FROM public.study_logs WHERE title = 'JavaScript変数と関数の学習' LIMIT 1), '👍'),
  ('550e8400-e29b-41d4-a716-446655440003', (SELECT id FROM public.study_logs WHERE title = 'JavaScript変数と関数の学習' LIMIT 1), '🔥'),
  ('550e8400-e29b-41d4-a716-446655440001', (SELECT id FROM public.study_logs WHERE title = 'TED Talksシャドーイング練習' LIMIT 1), '💪'),
  ('550e8400-e29b-41d4-a716-446655440003', (SELECT id FROM public.study_logs WHERE title = '二次関数の問題演習' LIMIT 1), '📚');
