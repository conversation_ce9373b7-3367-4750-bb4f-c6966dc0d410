import { addBookmark, isRecipeBookmarked, removeBookmark } from '@/lib/api/bookmarks'
import { useEffect, useState } from 'react'
import { HiHeart, HiOutlineHeart } from 'react-icons/hi2'

interface BookmarkButtonProps {
  recipeId: string
  className?: string
  showText?: boolean
}

/**
 * ブックマークボタンコンポーネント
 * 楽観的更新により、ネットワークエラーがあってもユーザーには即座に反映される
 */
export const BookmarkButton: React.FC<BookmarkButtonProps> = ({
  recipeId,
  className = '',
  showText = false
}) => {
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 初期状態を取得
  useEffect(() => {
    const checkStatus = async () => {
      try {
        const status = await isRecipeBookmarked(recipeId)
        setIsBookmarked(status)
      } catch (err) {
        console.error('Failed to check bookmark status:', err)
        // Error is handled gracefully as cache provides fallback
      }
    }
    
    checkStatus()
  }, [recipeId])

  // ブックマーク切り替え
  const handleToggle = async () => {
    if (isLoading) return

    setIsLoading(true)
    setError(null)

    // Optimistic update: immediately update UI
    const newBookmarkState = !isBookmarked
    setIsBookmarked(newBookmarkState)

    try {
      if (newBookmarkState) {
        await addBookmark(recipeId)
        console.log('✅ Bookmark added successfully')
      } else {
        await removeBookmark(recipeId)
        console.log('✅ Bookmark removed successfully')
      }
    } catch (err) {
      // Rollback to original state on error
      setIsBookmarked(!newBookmarkState)
      const errorMessage = err instanceof Error ? err.message : 'Failed to update bookmark'
      setError(errorMessage)
      console.error('❌ Bookmark operation failed:', err)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="relative">
      <button
        onClick={handleToggle}
        disabled={isLoading}
        className={`
          flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200
          ${isBookmarked 
            ? 'bg-red-50 text-red-600 hover:bg-red-100' 
            : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
          }
          ${isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'}
          ${className}
        `}
        title={isBookmarked ? 'Remove bookmark' : 'Add bookmark'}
      >
        {isLoading ? (
          <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
        ) : isBookmarked ? (
          <HiHeart className="w-5 h-5" />
        ) : (
          <HiOutlineHeart className="w-5 h-5" />
        )}
        
        {showText && (
          <span className="text-sm font-medium">
            {isBookmarked ? 'Bookmarked' : 'Bookmark'}
          </span>
        )}
      </button>

      {/* エラー表示 */}
      {error && (
        <div className="absolute top-full left-0 mt-1 p-2 bg-red-100 text-red-700 text-xs rounded shadow-lg z-10 whitespace-nowrap">
          {error}
          <button
            onClick={() => setError(null)}
            className="ml-2 text-red-500 hover:text-red-700"
          >
            ×
          </button>
        </div>
      )}
    </div>
  )
}

export default BookmarkButton
