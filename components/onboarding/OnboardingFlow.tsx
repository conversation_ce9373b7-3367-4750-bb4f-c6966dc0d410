import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { useAuth } from '../../lib/auth'
import { OnboardingStep, OnboardingData } from '../../types'
import ProfileImageStep from './ProfileImageStep'
import UsernameNationalityStep from './UsernameNationalityStep'
import StudyCategoriesStep from './StudyCategoriesStep'
import { HiXMark } from 'react-icons/hi2'

interface OnboardingFlowProps {
  isOpen: boolean
  onClose: () => void
  onComplete: () => void
}

const STEPS: OnboardingStep[] = ['profile-image', 'username-nationality', 'study-categories']

export default function OnboardingFlow({ isOpen, onClose, onComplete }: OnboardingFlowProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({})
  const [isLoading, setIsLoading] = useState(false)
  const { profile, refreshProfile } = useAuth()
  const router = useRouter()

  const currentStep = STEPS[currentStepIndex]
  const isLastStep = currentStepIndex === STEPS.length - 1

  // Initialize username from current profile
  useEffect(() => {
    if (profile && !onboardingData.username) {
      setOnboardingData(prev => ({
        ...prev,
        username: profile.username
      }))
    }
  }, [profile, onboardingData.username])

  const handleNext = () => {
    if (isLastStep) {
      handleComplete()
    } else {
      setCurrentStepIndex(prev => prev + 1)
    }
  }

  const handleBack = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1)
    }
  }

  const handleSkip = () => {
    if (currentStep === 'study-categories') {
      // Cannot skip study categories
      return
    }
    handleNext()
  }

  const handleComplete = async () => {
    setIsLoading(true)
    try {
      // Update profile with collected data
      if (profile) {
        const updateData: any = {}
        
        if (onboardingData.username && onboardingData.username !== profile.username) {
          updateData.username = onboardingData.username
        }
        
        if (onboardingData.nationality) {
          updateData.nationality = onboardingData.nationality
        }

        // Update profile if there's data to update
        if (Object.keys(updateData).length > 0) {
          const { updateProfile } = await import('../../lib/api/profiles')
          await updateProfile(updateData)
        }

        // Handle profile image upload if provided
        if (onboardingData.profileImage) {
          const { uploadProfileImage } = await import('../../lib/api/profiles')
          await uploadProfileImage(onboardingData.profileImage)
        }

        // Handle study categories selection
        if (onboardingData.studyCategories && onboardingData.studyCategories.length > 0) {
          const { updateUserStudyCategories } = await import('../../lib/api/studyCategories')
          await updateUserStudyCategories(onboardingData.studyCategories)
        }

        // Refresh profile to get updated data
        await refreshProfile()
      }

      onComplete()
    } catch (error) {
      console.error('Error completing onboarding:', error)
      // Handle error - maybe show error message
    } finally {
      setIsLoading(false)
    }
  }

  const updateOnboardingData = (data: Partial<OnboardingData>) => {
    setOnboardingData(prev => ({ ...prev, ...data }))
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-semibold text-gray-900">
              Welcome to StudyShare
            </h2>
            <div className="text-sm text-gray-500">
              {currentStepIndex + 1} / {STEPS.length}
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <HiXMark className="w-6 h-6" />
          </button>
        </div>

        {/* Progress Bar */}
        <div className="px-6 py-2">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-black h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStepIndex + 1) / STEPS.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Step Content */}
        <div className="p-6">
          {currentStep === 'profile-image' && (
            <ProfileImageStep
              data={onboardingData}
              onUpdate={updateOnboardingData}
              onNext={handleNext}
              onSkip={handleSkip}
              isLoading={isLoading}
            />
          )}
          
          {currentStep === 'username-nationality' && (
            <UsernameNationalityStep
              data={onboardingData}
              onUpdate={updateOnboardingData}
              onNext={handleNext}
              onBack={handleBack}
              onSkip={handleSkip}
              isLoading={isLoading}
            />
          )}
          
          {currentStep === 'study-categories' && (
            <StudyCategoriesStep
              data={onboardingData}
              onUpdate={updateOnboardingData}
              onNext={handleNext}
              onBack={handleBack}
              isLoading={isLoading}
              isLastStep={true}
            />
          )}
        </div>
      </div>
    </div>
  )
}
