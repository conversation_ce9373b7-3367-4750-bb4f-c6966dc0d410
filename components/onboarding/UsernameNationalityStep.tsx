import { useState, useEffect } from 'react'
import { OnboardingData } from '../../types'
import { validateUsername, isUsernameAvailable } from '../../lib/utils/username'
import { HiArrowLeft } from 'react-icons/hi2'

interface UsernameNationalityStepProps {
  data: OnboardingData
  onUpdate: (data: Partial<OnboardingData>) => void
  onNext: () => void
  onBack: () => void
  onSkip: () => void
  isLoading: boolean
}

// Common countries/nationalities
const COUNTRIES = [
  'Japan', 'United States', 'United Kingdom', 'Canada', 'Australia',
  'Germany', 'France', 'Spain', 'Italy', 'Netherlands',
  'South Korea', 'China', 'Taiwan', 'Hong Kong', 'Singapore',
  'India', 'Thailand', 'Vietnam', 'Philippines', 'Indonesia',
  'Brazil', 'Mexico', 'Argentina', 'Chile', 'Colombia',
  'Russia', 'Ukraine', 'Poland', 'Czech Republic', 'Hungary',
  'Sweden', 'Norway', 'Denmark', 'Finland', 'Switzerland',
  'Austria', 'Belgium', 'Portugal', 'Greece', 'Turkey',
  'Israel', 'Saudi Arabia', 'UAE', 'Egypt', 'South Africa',
  'Nigeria', 'Kenya', 'Morocco', 'Ghana', 'Other'
]

export default function UsernameNationalityStep({
  data,
  onUpdate,
  onNext,
  onBack,
  onSkip,
  isLoading
}: UsernameNationalityStepProps) {
  const [username, setUsername] = useState(data.username || '')
  const [nationality, setNationality] = useState(data.nationality || '')
  const [usernameError, setUsernameError] = useState<string | null>(null)
  const [isCheckingUsername, setIsCheckingUsername] = useState(false)
  const [filteredCountries, setFilteredCountries] = useState(COUNTRIES)
  const [showCountryDropdown, setShowCountryDropdown] = useState(false)

  useEffect(() => {
    onUpdate({ username, nationality })
  }, [username, nationality, onUpdate])

  const handleUsernameChange = (value: string) => {
    setUsername(value)
    setUsernameError(null)
  }

  const handleUsernameBlur = async () => {
    if (!username.trim()) return

    const validation = validateUsername(username)
    if (!validation.isValid) {
      setUsernameError(validation.error || 'Invalid username')
      return
    }

    setIsCheckingUsername(true)
    try {
      const isAvailable = await isUsernameAvailable(username)
      if (!isAvailable) {
        setUsernameError('Username is already taken')
      }
    } catch (error) {
      setUsernameError('Error checking username availability')
    } finally {
      setIsCheckingUsername(false)
    }
  }

  const handleNationalityChange = (value: string) => {
    setNationality(value)
    setShowCountryDropdown(false)
  }

  const handleNationalityInputChange = (value: string) => {
    setNationality(value)
    const filtered = COUNTRIES.filter(country =>
      country.toLowerCase().includes(value.toLowerCase())
    )
    setFilteredCountries(filtered)
    setShowCountryDropdown(true)
  }

  const canProceed = username.trim() && !usernameError && !isCheckingUsername

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Customize your profile
        </h3>
        <p className="text-gray-600 text-sm">
          Choose your username and let others know where you're from
        </p>
      </div>

      <div className="space-y-4">
        {/* Username Field */}
        <div>
          <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
            Username *
          </label>
          <input
            id="username"
            type="text"
            value={username}
            onChange={(e) => handleUsernameChange(e.target.value)}
            onBlur={handleUsernameBlur}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent ${
              usernameError ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="your_username"
            disabled={isLoading}
          />
          {isCheckingUsername && (
            <p className="text-sm text-gray-500 mt-1">Checking availability...</p>
          )}
          {usernameError && (
            <p className="text-sm text-red-500 mt-1">{usernameError}</p>
          )}
          <p className="text-xs text-gray-500 mt-1">
            This is how others will find and mention you
          </p>
        </div>

        {/* Nationality Field */}
        <div className="relative">
          <label htmlFor="nationality" className="block text-sm font-medium text-gray-700 mb-1">
            Country/Region
          </label>
          <input
            id="nationality"
            type="text"
            value={nationality}
            onChange={(e) => handleNationalityInputChange(e.target.value)}
            onFocus={() => setShowCountryDropdown(true)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
            placeholder="Select your country"
            disabled={isLoading}
            autoComplete="country"
          />
          
          {showCountryDropdown && filteredCountries.length > 0 && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-48 overflow-y-auto">
              {filteredCountries.map((country) => (
                <button
                  key={country}
                  type="button"
                  onClick={() => handleNationalityChange(country)}
                  className="w-full text-left px-3 py-2 hover:bg-gray-50 focus:bg-gray-50 focus:outline-none"
                >
                  {country}
                </button>
              ))}
            </div>
          )}
          
          <p className="text-xs text-gray-500 mt-1">
            Optional - helps connect you with learners from your region
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col space-y-3">
        <button
          type="button"
          onClick={onNext}
          disabled={!canProceed || isLoading}
          className="w-full bg-black text-white py-3 px-4 rounded-lg font-medium hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Processing...' : 'Continue'}
        </button>
        
        <button
          type="button"
          onClick={onSkip}
          disabled={isLoading}
          className="w-full text-gray-600 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Skip for now
        </button>

        <button
          type="button"
          onClick={onBack}
          disabled={isLoading}
          className="flex items-center justify-center space-x-2 w-full text-gray-600 py-2 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <HiArrowLeft className="w-4 h-4" />
          <span>Back</span>
        </button>
      </div>
    </div>
  )
}
