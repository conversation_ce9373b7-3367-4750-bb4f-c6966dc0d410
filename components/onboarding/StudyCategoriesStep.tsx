import { useState, useEffect } from 'react'
import { OnboardingData, StudyCategory } from '../../types'
import { HiArrowLeft, HiCheck } from 'react-icons/hi2'

interface StudyCategoriesStepProps {
  data: OnboardingData
  onUpdate: (data: Partial<OnboardingData>) => void
  onNext: () => void
  onBack: () => void
  isLoading: boolean
  isLastStep: boolean
}

export default function StudyCategoriesStep({
  data,
  onUpdate,
  onNext,
  onBack,
  isLoading,
  isLastStep
}: StudyCategoriesStepProps) {
  const [categories, setCategories] = useState<StudyCategory[]>([])
  const [selectedCategories, setSelectedCategories] = useState<string[]>(data.studyCategories || [])
  const [loadingCategories, setLoadingCategories] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadCategories()
  }, [])

  useEffect(() => {
    onUpdate({ studyCategories: selectedCategories })
  }, [selectedCategories, onUpdate])

  const loadCategories = async () => {
    try {
      setLoadingCategories(true)
      const { fetchStudyCategories } = await import('../../lib/api/studyCategories')
      const categoriesData = await fetchStudyCategories()
      setCategories(categoriesData)
    } catch (error) {
      console.error('Error loading categories:', error)
      setError('Failed to load study categories')
    } finally {
      setLoadingCategories(false)
    }
  }

  const handleCategoryToggle = (categoryId: string) => {
    setSelectedCategories(prev => {
      if (prev.includes(categoryId)) {
        return prev.filter(id => id !== categoryId)
      } else {
        return [...prev, categoryId]
      }
    })
  }

  const canProceed = selectedCategories.length > 0

  if (loadingCategories) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            What would you like to study?
          </h3>
          <p className="text-gray-600 text-sm">Loading categories...</p>
        </div>
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            What would you like to study?
          </h3>
          <p className="text-red-600 text-sm">{error}</p>
        </div>
        <button
          onClick={loadCategories}
          className="w-full bg-black text-white py-3 px-4 rounded-lg font-medium hover:bg-gray-800 transition-colors"
        >
          Try Again
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          What would you like to study?
        </h3>
        <p className="text-gray-600 text-sm">
          Select the languages or subjects you're interested in learning
        </p>
      </div>

      {/* Categories Grid */}
      <div className="space-y-3">
        {categories.map((category) => {
          const isSelected = selectedCategories.includes(category.id)
          return (
            <button
              key={category.id}
              type="button"
              onClick={() => handleCategoryToggle(category.id)}
              className={`w-full flex items-center justify-between p-4 rounded-lg border-2 transition-all ${
                isSelected
                  ? 'border-black bg-black text-white'
                  : 'border-gray-200 bg-white text-gray-900 hover:border-gray-300'
              }`}
              disabled={isLoading}
            >
              <span className="font-medium">{category.display_name}</span>
              {isSelected && (
                <HiCheck className="w-5 h-5" />
              )}
            </button>
          )
        })}
      </div>

      {selectedCategories.length > 0 && (
        <div className="text-center">
          <p className="text-sm text-gray-600">
            {selectedCategories.length} categor{selectedCategories.length === 1 ? 'y' : 'ies'} selected
          </p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col space-y-3">
        <button
          type="button"
          onClick={onNext}
          disabled={!canProceed || isLoading}
          className="w-full bg-black text-white py-3 px-4 rounded-lg font-medium hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Setting up your profile...' : isLastStep ? 'Complete Setup' : 'Continue'}
        </button>

        <button
          type="button"
          onClick={onBack}
          disabled={isLoading}
          className="flex items-center justify-center space-x-2 w-full text-gray-600 py-2 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <HiArrowLeft className="w-4 h-4" />
          <span>Back</span>
        </button>
      </div>

      <div className="text-center">
        <p className="text-xs text-gray-500">
          You can always change your study categories later in your profile settings
        </p>
      </div>
    </div>
  )
}
