import { useEffect, useState } from 'react'
import { TimerState } from '../../hooks/useTimer'

interface QuickTimeSettingsProps {
  timerState: TimerState
  onAddTime: (seconds: number) => void
  onSetTime: (minutes: number) => void
  onSetTimeSeconds: (totalSeconds: number) => void
  className?: string
}

export default function QuickTimeSettings({
  timerState,
  onAddTime,
  onSetTime,
  onSetTimeSeconds,
  className = ''
}: QuickTimeSettingsProps) {
  // Only show for timer mode
  if (timerState.mode !== 'timer') return null

  // State for custom time input (hh:mm:ss)
  const [customHours, setCustomHours] = useState(0)
  const [customMinutes, setCustomMinutes] = useState(25)
  const [customSeconds, setCustomSeconds] = useState(0)
  const [isInitializing, setIsInitializing] = useState(true)

  // Initialize custom time from timer state (only when initializing)
  useEffect(() => {
    const hours = Math.floor(timerState.initialTime / 3600)
    const minutes = Math.floor((timerState.initialTime % 3600) / 60)
    const seconds = timerState.initialTime % 60
    
    setCustomHours(hours)
    setCustomMinutes(minutes)
    setCustomSeconds(seconds)
    setIsInitializing(false)
  }, [timerState.initialTime])

  // Update timer when custom time changes (only after initialization)
  useEffect(() => {
    if (!isInitializing && !timerState.isRunning) {
      const totalSeconds = (customHours * 3600) + (customMinutes * 60) + customSeconds
      if (totalSeconds > 0) {
        onSetTimeSeconds(totalSeconds)
      }
    }
  }, [customHours, customMinutes, customSeconds, timerState.isRunning, isInitializing])

  const handleHoursChange = (value: number) => {
    setCustomHours(Math.max(0, Math.min(23, value)))
  }

  const handleMinutesChange = (value: number) => {
    setCustomMinutes(Math.max(0, Math.min(59, value)))
  }

  const handleSecondsChange = (value: number) => {
    setCustomSeconds(Math.max(0, Math.min(59, value)))
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Custom Time Input */}
      <div>
        <h4 className="text-sm font-medium text-gray-700 mb-2">Custom Time</h4>
        <div className="flex items-center space-x-2">
          <input
            type="number"
            min="0"
            max="23"
            value={customHours || 0}
            onChange={(e) => handleHoursChange(parseInt(e.target.value) || 0)}
            className="input w-16 text-center"
            disabled={timerState.isRunning}
            placeholder="0"
          />
          <span className="text-sm text-gray-600">h</span>
          <input
            type="number"
            min="0"
            max="59"
            value={customMinutes || 0}
            onChange={(e) => handleMinutesChange(parseInt(e.target.value) || 0)}
            className="input w-16 text-center"
            disabled={timerState.isRunning}
            placeholder="0"
          />
          <span className="text-sm text-gray-600">m</span>
          <input
            type="number"
            min="0"
            max="59"
            value={customSeconds || 0}
            onChange={(e) => handleSecondsChange(parseInt(e.target.value) || 0)}
            className="input w-16 text-center"
            disabled={timerState.isRunning}
            placeholder="0"
          />
          <span className="text-sm text-gray-600">s</span>
        </div>
      </div>
    </div>
  )
}

// Mobile-optimized quick settings
export function MobileQuickTimeSettings({
  timerState,
  onAddTime,
  onSetTime,
  onSetTimeSeconds,
  className = ''
}: QuickTimeSettingsProps) {
  if (timerState.mode !== 'timer') return null

  // State for custom time input (hh:mm:ss)
  const [customHours, setCustomHours] = useState(0)
  const [customMinutes, setCustomMinutes] = useState(25)
  const [customSeconds, setCustomSeconds] = useState(0)
  const [isInitializing, setIsInitializing] = useState(true)

  // Initialize custom time from timer state (only when initializing)
  useEffect(() => {
    const hours = Math.floor(timerState.initialTime / 3600)
    const minutes = Math.floor((timerState.initialTime % 3600) / 60)
    const seconds = timerState.initialTime % 60
    
    setCustomHours(hours)
    setCustomMinutes(minutes)
    setCustomSeconds(seconds)
    setIsInitializing(false)
  }, [timerState.initialTime])

  // Update timer when custom time changes (only after initialization)
  useEffect(() => {
    if (!isInitializing && !timerState.isRunning && onSetTimeSeconds) {
      const totalSeconds = (customHours * 3600) + (customMinutes * 60) + customSeconds
      if (totalSeconds > 0) {
        onSetTimeSeconds(totalSeconds)
      }
    }
  }, [customHours, customMinutes, customSeconds, timerState.isRunning, isInitializing])

  const handleHoursChange = (value: number) => {
    setCustomHours(Math.max(0, Math.min(23, value)))
  }

  const handleMinutesChange = (value: number) => {
    setCustomMinutes(Math.max(0, Math.min(59, value)))
  }

  const handleSecondsChange = (value: number) => {
    setCustomSeconds(Math.max(0, Math.min(59, value)))
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Custom Time Input */}
      <div>
        <h4 className="text-sm font-medium text-gray-700 mb-2">Custom Time</h4>
        <div className="flex items-center space-x-2">
          <input
            type="number"
            min="0"
            max="23"
            value={customHours || 0}
            onChange={(e) => handleHoursChange(parseInt(e.target.value) || 0)}
            className="input w-12 text-center text-sm"
            disabled={timerState.isRunning}
            placeholder="0"
          />
          <span className="text-xs text-gray-600">h</span>
          <input
            type="number"
            min="0"
            max="59"
            value={customMinutes || 0}
            onChange={(e) => handleMinutesChange(parseInt(e.target.value) || 0)}
            className="input w-12 text-center text-sm"
            disabled={timerState.isRunning}
            placeholder="0"
          />
          <span className="text-xs text-gray-600">m</span>
          <input
            type="number"
            min="0"
            max="59"
            value={customSeconds || 0}
            onChange={(e) => handleSecondsChange(parseInt(e.target.value) || 0)}
            className="input w-12 text-center text-sm"
            disabled={timerState.isRunning}
            placeholder="0"
          />
          <span className="text-xs text-gray-600">s</span>
        </div>
      </div>
    </div>
  )
}

// Compact quick settings
export function CompactQuickTimeSettings({
  timerState,
  onAddTime,
  onSetTime,
  className = ''
}: QuickTimeSettingsProps) {
  if (timerState.mode !== 'timer') return null

  const quickButtons = [
    { label: '+30s', seconds: 30 },
    { label: '+1m', seconds: 60 },
    { label: '+5m', seconds: 300 },
  ]

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {quickButtons.map((button) => (
        <button
          key={button.seconds}
          type="button"
          onClick={() => onAddTime(button.seconds)}
          className="px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50 transition-colors"
          disabled={timerState.isRunning}
        >
          {button.label}
        </button>
      ))}
    </div>
  )
}

// Fullscreen quick settings
export function FullscreenQuickTimeSettings({
  timerState,
  onAddTime,
  onSetTime,
  onSetTimeSeconds,
  className = ''
}: QuickTimeSettingsProps) {
  if (timerState.mode !== 'timer') return null

  // State for custom time input (hh:mm:ss)
  const [customHours, setCustomHours] = useState(0)
  const [customMinutes, setCustomMinutes] = useState(25)
  const [customSeconds, setCustomSeconds] = useState(0)
  const [isInitializing, setIsInitializing] = useState(true)

  // Initialize custom time from timer state (only when initializing)
  useEffect(() => {
    const hours = Math.floor(timerState.initialTime / 3600)
    const minutes = Math.floor((timerState.initialTime % 3600) / 60)
    const seconds = timerState.initialTime % 60
    
    setCustomHours(hours)
    setCustomMinutes(minutes)
    setCustomSeconds(seconds)
    setIsInitializing(false)
  }, [timerState.initialTime])

  // Update timer when custom time changes (only after initialization)
  useEffect(() => {
    if (!isInitializing && !timerState.isRunning && onSetTimeSeconds) {
      const totalSeconds = (customHours * 3600) + (customMinutes * 60) + customSeconds
      if (totalSeconds > 0) {
        onSetTimeSeconds(totalSeconds)
      }
    }
  }, [customHours, customMinutes, customSeconds, timerState.isRunning, isInitializing])

  const handleHoursChange = (value: number) => {
    setCustomHours(Math.max(0, Math.min(23, value)))
  }

  const handleMinutesChange = (value: number) => {
    setCustomMinutes(Math.max(0, Math.min(59, value)))
  }

  const handleSecondsChange = (value: number) => {
    setCustomSeconds(Math.max(0, Math.min(59, value)))
  }

  return (
    <div className={`space-y-4 sm:space-y-6 ${className}`}>
      {/* Custom Time Input */}
      <div>
        <h4 className="text-base sm:text-lg font-medium text-white mb-3 sm:mb-4 text-center">Custom Time</h4>
        <div className="flex items-center justify-center space-x-2 sm:space-x-3">
          <input
            type="number"
            min="0"
            max="23"
            value={customHours || 0}
            onChange={(e) => handleHoursChange(parseInt(e.target.value) || 0)}
            className="w-12 h-10 sm:w-16 sm:h-12 text-center text-sm sm:text-lg font-mono bg-gray-700 text-white border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-white"
            disabled={timerState.isRunning}
            placeholder="0"
          />
          <span className="text-sm sm:text-lg text-gray-300">h</span>
          <input
            type="number"
            min="0"
            max="59"
            value={customMinutes || 0}
            onChange={(e) => handleMinutesChange(parseInt(e.target.value) || 0)}
            className="w-12 h-10 sm:w-16 sm:h-12 text-center text-sm sm:text-lg font-mono bg-gray-700 text-white border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-white"
            disabled={timerState.isRunning}
            placeholder="0"
          />
          <span className="text-sm sm:text-lg text-gray-300">m</span>
          <input
            type="number"
            min="0"
            max="59"
            value={customSeconds || 0}
            onChange={(e) => handleSecondsChange(parseInt(e.target.value) || 0)}
            className="w-12 h-10 sm:w-16 sm:h-12 text-center text-sm sm:text-lg font-mono bg-gray-700 text-white border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-white"
            disabled={timerState.isRunning}
            placeholder="0"
          />
          <span className="text-sm sm:text-lg text-gray-300">s</span>
        </div>
      </div>
    </div>
  )
}
