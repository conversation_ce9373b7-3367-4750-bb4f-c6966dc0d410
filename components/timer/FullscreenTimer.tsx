import { HiXMark } from 'react-icons/hi2'
import { formatTime, getProgress, TimerState } from '../../hooks/useTimer'
import { FullscreenCircularProgress } from './CircularProgress'
import { FullscreenQuickTimeSettings } from './QuickTimeSettings'
import { FullscreenTimerControls } from './TimerControls'
import { FullscreenTimerDisplay, FullscreenTimerStatus } from './TimerDisplay'
import { FullscreenTimerTabs } from './TimerTabs'

interface FullscreenTimerProps {
  timerState: TimerState
  onModeChange: (mode: 'timer' | 'stopwatch') => void
  onStart: () => void
  onPause: () => void
  onStop: () => void
  onReset: () => void
  onAddTime: (seconds: number) => void
  onSetTime: (minutes: number) => void
  onSetTimeSeconds: (totalSeconds: number) => void
  onExitFullscreen: () => void
}

export default function FullscreenTimer({
  timerState,
  onModeChange,
  onStart,
  onPause,
  onStop,
  onReset,
  onAddTime,
  onSetTime,
  onSetTimeSeconds,
  onExitFullscreen
}: FullscreenTimerProps) {
  const progress = getProgress(timerState)
  const displayTime = timerState.mode === 'timer' 
    ? timerState.timeRemaining 
    : timerState.timeElapsed

  return (
    <div className="fixed inset-0 bg-black text-white z-50 flex flex-col">
      {/* Header with exit button */}
      <div className="flex justify-between items-center p-4 sm:p-6">
        <div className="flex-1" />
        <h1 className="text-xl sm:text-2xl font-bold text-center">
          Study Timer
        </h1>
        <div className="flex-1 flex justify-end">
          <button
            onClick={onExitFullscreen}
            className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gray-800 hover:bg-gray-700 transition-colors flex items-center justify-center"
            title="全画面を終了 (ESC)"
          >
            <HiXMark className="w-6 h-6 sm:w-7 sm:h-7" />
          </button>
        </div>
      </div>

      {/* Mode tabs */}
      <div className="px-4 sm:px-6 mb-6">
        <div className="max-w-md mx-auto">
          <FullscreenTimerTabs
            activeMode={timerState.mode}
            onModeChange={onModeChange}
          />
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col items-center justify-center px-4 sm:px-6 space-y-4 sm:space-y-6 lg:space-y-8 min-h-0">
        {/* Circular progress with time display */}
        <div className="relative flex-shrink-0">
          <FullscreenCircularProgress progress={progress}>
            <div className="text-center">
              <FullscreenTimerDisplay timerState={timerState} />
              <FullscreenTimerStatus timerState={timerState} className="mt-2" />
            </div>
          </FullscreenCircularProgress>
        </div>

        {/* Quick time settings (only for timer mode) */}
        {timerState.mode === 'timer' && !timerState.isRunning && (
          <div className="flex-shrink-0 w-full max-w-2xl">
            <FullscreenQuickTimeSettings
              timerState={timerState}
              onAddTime={onAddTime}
              onSetTime={onSetTime}
              onSetTimeSeconds={onSetTimeSeconds}
            />
          </div>
        )}

        {/* Controls */}
        <div className="flex-shrink-0">
          <FullscreenTimerControls
            timerState={timerState}
            onStart={onStart}
            onPause={onPause}
            onStop={onStop}
            onReset={onReset}
          />
        </div>
      </div>

      {/* Footer with instructions */}
      <div className="p-4 sm:p-6 text-center">
        <p className="text-sm text-gray-400">
          ESCキーまたは右上のボタンで全画面を終了
        </p>
      </div>
    </div>
  )
}

// Mobile-specific fullscreen timer
export function MobileFullscreenTimer({
  timerState,
  onModeChange,
  onStart,
  onPause,
  onStop,
  onReset,
  onAddTime,
  onSetTime,
  onExitFullscreen
}: FullscreenTimerProps) {
  const progress = getProgress(timerState)

  return (
    <div className="fixed inset-0 bg-black text-white z-50 flex flex-col">
      {/* Header */}
      <div className="flex justify-between items-center p-4">
        <h1 className="text-lg font-bold">Study Timer</h1>
        <button
          onClick={onExitFullscreen}
          className="w-10 h-10 rounded-full bg-gray-800 hover:bg-gray-700 transition-colors flex items-center justify-center"
        >
          <HiXMark className="w-5 h-5" />
        </button>
      </div>

      {/* Mode tabs */}
      <div className="px-4 mb-4">
        <FullscreenTimerTabs
          activeMode={timerState.mode}
          onModeChange={onModeChange}
        />
      </div>

      {/* Main content - optimized for mobile */}
      <div className="flex-1 flex flex-col items-center justify-center px-4 space-y-6">
        {/* Circular progress */}
        <div className="relative">
          <div className="w-64 h-64">
            <FullscreenCircularProgress progress={progress}>
              <div className="text-center">
                <div className="text-4xl font-mono font-bold text-white mb-2">
                  {formatTime(timerState.mode === 'timer' ? timerState.timeRemaining : timerState.timeElapsed)}
                </div>
                <div className="text-sm text-gray-300">
                  {timerState.mode === 'timer' ? '残り時間' : '経過時間'}
                </div>
              </div>
            </FullscreenCircularProgress>
          </div>
        </div>

        {/* Quick settings for timer */}
        {timerState.mode === 'timer' && !timerState.isRunning && (
          <div className="w-full max-w-sm">
            <div className="grid grid-cols-3 gap-2 mb-4">
              {[
                { label: '+30秒', seconds: 30 },
                { label: '+1分', seconds: 60 },
                { label: '+5分', seconds: 300 },
              ].map((button) => (
                <button
                  key={button.seconds}
                  onClick={() => onAddTime(button.seconds)}
                  className="bg-gray-700 text-white px-3 py-2 text-sm rounded-lg hover:bg-gray-600 transition-colors"
                >
                  {button.label}
                </button>
              ))}
            </div>
            <div className="grid grid-cols-3 gap-2">
              {[5, 10, 15, 25, 30, 45].map((minutes) => (
                <button
                  key={minutes}
                  onClick={() => onSetTime(minutes)}
                  className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                    Math.floor(timerState.initialTime / 60) === minutes
                      ? 'bg-white text-black'
                      : 'bg-gray-700 text-white hover:bg-gray-600'
                  }`}
                >
                  {minutes}分
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Controls */}
        <div className="flex items-center space-x-4">
          {timerState.isRunning && !timerState.isPaused ? (
            <button
              onClick={onPause}
              className="bg-gray-700 text-white hover:bg-gray-600 px-6 py-3 rounded-xl text-base font-medium transition-colors"
            >
              一時停止
            </button>
          ) : (
            <button
              onClick={onStart}
              className="bg-white text-black hover:bg-gray-100 px-6 py-3 rounded-xl text-base font-medium transition-colors"
            >
              開始
            </button>
          )}
          
          <button
            onClick={onStop}
            className="bg-gray-700 text-white hover:bg-gray-600 px-6 py-3 rounded-xl text-base font-medium transition-colors"
          >
            停止
          </button>
          
          <button
            onClick={onReset}
            className="w-12 h-12 rounded-full border-2 border-white bg-transparent hover:bg-white hover:bg-opacity-20 transition-colors flex items-center justify-center"
          >
            <HiXMark className="w-5 h-5 text-white transform rotate-45" />
          </button>
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 text-center">
        <p className="text-xs text-gray-400">
          タップして全画面を終了
        </p>
      </div>
    </div>
  )
}
