import { useEffect, useRef } from 'react'
import { HiArrowsPointingOut } from 'react-icons/hi2'
import { useFullscreen } from '../../hooks/useFullscreen'
import { formatTime, getProgress, useTimer } from '../../hooks/useTimer'
import { useTimerEndNotification } from '../../hooks/useTimerNotification'
import { ResponsiveCircularProgress } from './CircularProgress'
import FullscreenTimer from './FullscreenTimer'
import QuickTimeSettings, { MobileQuickTimeSettings } from './QuickTimeSettings'
import TimerControls, { MobileTimerControls } from './TimerControls'
import TimerDisplay, { MobileTimerDisplay, TimerStatus } from './TimerDisplay'
import TimerTabs, { MobileTimerTabs } from './TimerTabs'

interface TimerStopwatchProps {
  onDurationChange?: (totalSeconds: number) => void
  className?: string
}

export default function TimerStopwatch({ onDurationChange, className = '' }: TimerStopwatchProps) {
  const [timerState, timerActions] = useTimer()
  const [fullscreenState, fullscreenActions] = useFullscreen()
  const { notifyTimerEnd } = useTimerEndNotification()
  const wasRunning = useRef(false)

  const progress = getProgress(timerState)

  // Handle timer end notification
  useEffect(() => {
    if (timerState.mode === 'timer' && timerState.timeRemaining === 0 && !timerState.isRunning) {
      const duration = formatTime(timerState.initialTime)
      notifyTimerEnd('timer', duration)
    }
  }, [timerState.mode, timerState.timeRemaining, timerState.isRunning, timerState.initialTime, notifyTimerEnd])

  // Update duration when timer stops (only once when transitioning from running to stopped)
  useEffect(() => {
    if (timerState.isRunning) {
      wasRunning.current = true
    } else if (wasRunning.current && !timerState.isRunning && onDurationChange && !fullscreenState.isFullscreen) {
      // Timer just stopped (but not due to fullscreen change)
      if (timerState.mode === 'stopwatch' && timerState.timeElapsed > 0) {
        onDurationChange(timerState.timeElapsed)
      } else if (timerState.mode === 'timer' && timerState.timeRemaining < timerState.initialTime) {
        const elapsed = timerState.initialTime - timerState.timeRemaining
        onDurationChange(elapsed)
      }
      wasRunning.current = false
    }
  }, [timerState.isRunning, timerState.mode, timerState.timeElapsed, timerState.timeRemaining, timerState.initialTime, onDurationChange, fullscreenState.isFullscreen])

  // Show fullscreen timer if in fullscreen mode
  if (fullscreenState.isFullscreen) {
    return (
      <FullscreenTimer
        timerState={timerState}
        onModeChange={timerActions.setMode}
        onStart={timerActions.start}
        onPause={timerActions.pause}
        onStop={timerActions.stop}
        onReset={timerActions.reset}
        onAddTime={timerActions.addTime}
        onSetTime={timerActions.setInitialTime}
        onSetTimeSeconds={timerActions.setInitialTimeSeconds}
        onExitFullscreen={fullscreenActions.exitFullscreen}
      />
    )
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header with fullscreen button */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-black">Timer & Stopwatch</h3>
        {fullscreenState.isSupported && (
          <button
            type="button"
            onClick={async (e) => {
              e.preventDefault()
              e.stopPropagation()
              await fullscreenActions.enterFullscreen()
            }}
            className="w-8 h-8 rounded-md border border-gray-300 bg-white hover:bg-gray-50 transition-colors flex items-center justify-center"
            title="Fullscreen"
          >
            <HiArrowsPointingOut className="w-4 h-4 text-gray-600" />
          </button>
        )}
      </div>

      {/* Desktop Layout */}
      <div className="hidden sm:block space-y-6">
        {/* Mode Tabs */}
        <TimerTabs
          activeMode={timerState.mode}
          onModeChange={timerActions.setMode}
        />

        {/* Timer Display with Circular Progress */}
        <div className="flex flex-col items-center space-y-4">
          <ResponsiveCircularProgress progress={progress}>
            <TimerDisplay timerState={timerState} />
          </ResponsiveCircularProgress>
          <TimerStatus timerState={timerState} />
        </div>

        {/* Quick Time Settings (Timer mode only) */}
        <QuickTimeSettings
          timerState={timerState}
          onAddTime={timerActions.addTime}
          onSetTime={timerActions.setInitialTime}
          onSetTimeSeconds={timerActions.setInitialTimeSeconds}
        />

        {/* Controls */}
        <TimerControls
          timerState={timerState}
          onStart={timerActions.start}
          onPause={timerActions.pause}
          onStop={timerActions.stop}
          onReset={timerActions.reset}
        />
      </div>

      {/* Mobile Layout */}
      <div className="block sm:hidden space-y-4">
        {/* Mode Tabs */}
        <MobileTimerTabs
          activeMode={timerState.mode}
          onModeChange={timerActions.setMode}
        />

        {/* Timer Display with Circular Progress */}
        <div className="flex flex-col items-center space-y-3">
          <ResponsiveCircularProgress progress={progress}>
            <MobileTimerDisplay timerState={timerState} />
          </ResponsiveCircularProgress>
          <TimerStatus timerState={timerState} />
        </div>

        {/* Quick Time Settings (Timer mode only) */}
        <MobileQuickTimeSettings
          timerState={timerState}
          onAddTime={timerActions.addTime}
          onSetTime={timerActions.setInitialTime}
          onSetTimeSeconds={timerActions.setInitialTimeSeconds}
        />

        {/* Controls */}
        <MobileTimerControls
          timerState={timerState}
          onStart={timerActions.start}
          onPause={timerActions.pause}
          onStop={timerActions.stop}
          onReset={timerActions.reset}
        />
      </div>
    </div>
  )
}

// Compact version for smaller spaces
export function CompactTimerStopwatch({ onDurationChange, className = '' }: TimerStopwatchProps) {
  const [timerState, timerActions] = useTimer()
  const [fullscreenState, fullscreenActions] = useFullscreen()
  const { notifyTimerEnd } = useTimerEndNotification()

  const progress = getProgress(timerState)

  // Handle timer end notification
  useEffect(() => {
    if (timerState.mode === 'timer' && timerState.timeRemaining === 0 && !timerState.isRunning) {
      const duration = formatTime(timerState.initialTime)
      notifyTimerEnd('timer', duration)
    }
  }, [timerState.mode, timerState.timeRemaining, timerState.isRunning, timerState.initialTime, notifyTimerEnd])

  // Update duration when timer stops
  useEffect(() => {
    if (!timerState.isRunning && onDurationChange && !fullscreenState.isFullscreen) {
      if (timerState.mode === 'stopwatch' && timerState.timeElapsed > 0) {
        onDurationChange(timerState.timeElapsed)
      } else if (timerState.mode === 'timer' && timerState.timeRemaining < timerState.initialTime) {
        const elapsed = timerState.initialTime - timerState.timeRemaining
        onDurationChange(elapsed)
      }
    }
  }, [timerState.isRunning, timerState.mode, timerState.timeElapsed, timerState.timeRemaining, timerState.initialTime, onDurationChange, fullscreenState.isFullscreen])

  // Show fullscreen timer if in fullscreen mode
  if (fullscreenState.isFullscreen) {
    return (
      <FullscreenTimer
        timerState={timerState}
        onModeChange={timerActions.setMode}
        onStart={timerActions.start}
        onPause={timerActions.pause}
        onStop={timerActions.stop}
        onReset={timerActions.reset}
        onAddTime={timerActions.addTime}
        onSetTime={timerActions.setInitialTime}
        onSetTimeSeconds={timerActions.setInitialTimeSeconds}
        onExitFullscreen={fullscreenActions.exitFullscreen}
      />
    )
  }

  return (
    <div className={`bg-gray-50 rounded-lg p-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <TimerTabs
            activeMode={timerState.mode}
            onModeChange={timerActions.setMode}
            className="text-xs"
          />
        </div>
        {fullscreenState.isSupported && (
          <button
            type="button"
            onClick={async (e) => {
              e.preventDefault()
              e.stopPropagation()
              await fullscreenActions.enterFullscreen()
            }}
            className="w-6 h-6 rounded border border-gray-300 bg-white hover:bg-gray-50 transition-colors flex items-center justify-center"
            title="Fullscreen"
          >
            <HiArrowsPointingOut className="w-3 h-3 text-gray-600" />
          </button>
        )}
      </div>

      {/* Compact Display */}
      <div className="flex items-center space-x-4">
        {/* Mini circular progress */}
        <div className="flex-shrink-0">
          <ResponsiveCircularProgress progress={progress}>
            <div className="text-center">
              <div className="text-lg font-mono font-bold text-black">
                {formatTime(timerState.mode === 'timer' ? timerState.timeRemaining : timerState.timeElapsed)}
              </div>
            </div>
          </ResponsiveCircularProgress>
        </div>

        {/* Controls */}
        <div className="flex-1">
          <MobileTimerControls
            timerState={timerState}
            onStart={timerActions.start}
            onPause={timerActions.pause}
            onStop={timerActions.stop}
            onReset={timerActions.reset}
            className="justify-start"
          />
        </div>
      </div>
    </div>
  )
}
