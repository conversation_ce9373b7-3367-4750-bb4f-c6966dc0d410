import React from 'react'

interface CircularProgressProps {
  progress: number // 0 to 1
  size?: number // diameter in pixels
  strokeWidth?: number
  className?: string
  children?: React.ReactNode
}

export default function CircularProgress({
  progress,
  size = 200,
  strokeWidth = 8,
  className = '',
  children
}: CircularProgressProps) {
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (progress * circumference)

  return (
    <div className={`relative inline-flex items-center justify-center ${className}`}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
        viewBox={`0 0 ${size} ${size}`}
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          className="text-gray-200"
        />
        
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className="text-black transition-all duration-300 ease-in-out"
        />
      </svg>
      
      {/* Content in the center */}
      {children && (
        <div className="absolute inset-0 flex items-center justify-center">
          {children}
        </div>
      )}
    </div>
  )
}

// Responsive circular progress for different screen sizes
export function ResponsiveCircularProgress({
  progress,
  className = '',
  children
}: Omit<CircularProgressProps, 'size' | 'strokeWidth'>) {
  return (
    <div className={className}>
      {/* Mobile */}
      <div className="block sm:hidden">
        <CircularProgress
          progress={progress}
          size={200}
          strokeWidth={6}
        >
          {children}
        </CircularProgress>
      </div>
      
      {/* Tablet */}
      <div className="hidden sm:block lg:hidden">
        <CircularProgress
          progress={progress}
          size={280}
          strokeWidth={8}
        >
          {children}
        </CircularProgress>
      </div>
      
      {/* Desktop */}
      <div className="hidden lg:block">
        <CircularProgress
          progress={progress}
          size={320}
          strokeWidth={10}
        >
          {children}
        </CircularProgress>
      </div>
    </div>
  )
}

// Fullscreen circular progress
export function FullscreenCircularProgress({
  progress,
  children
}: Omit<CircularProgressProps, 'size' | 'strokeWidth' | 'className'>) {
  return (
    <div className="flex items-center justify-center">
      {/* Mobile portrait fullscreen */}
      <div className="block sm:hidden">
        <CircularProgress
          progress={progress}
          size={240}
          strokeWidth={6}
          className="text-white"
        >
          {children}
        </CircularProgress>
      </div>
      
      {/* Tablet and mobile landscape fullscreen */}
      <div className="hidden sm:block xl:hidden">
        <CircularProgress
          progress={progress}
          size={320}
          strokeWidth={8}
          className="text-white"
        >
          {children}
        </CircularProgress>
      </div>
      
      {/* Desktop fullscreen */}
      <div className="hidden xl:block">
        <CircularProgress
          progress={progress}
          size={400}
          strokeWidth={10}
          className="text-white"
        >
          {children}
        </CircularProgress>
      </div>
    </div>
  )
}
