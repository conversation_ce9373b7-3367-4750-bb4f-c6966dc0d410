import { TimerMode } from '../../hooks/useTimer'

interface TimerTabsProps {
  activeMode: TimerMode
  onModeChange: (mode: TimerMode) => void
  className?: string
}

export default function TimerTabs({ activeMode, onModeChange, className = '' }: TimerTabsProps) {
  return (
    <div className={`flex bg-gray-50 rounded-lg p-1 ${className}`}>
      <button
        type="button"
        onClick={() => onModeChange('timer')}
        className={`flex-1 px-4 py-3 text-sm font-medium rounded-md transition-all duration-200 relative ${
          activeMode === 'timer'
            ? 'bg-black text-white shadow-sm'
            : 'text-gray-600 hover:text-black hover:bg-white'
        }`}
      >
        <div className="flex items-center justify-center space-x-2">
          <span>⏰</span>
          <span>Timer</span>
        </div>
        {activeMode === 'timer' && (
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full" />
        )}
      </button>
      
      <button
        type="button"
        onClick={() => onModeChange('stopwatch')}
        className={`flex-1 px-4 py-3 text-sm font-medium rounded-md transition-all duration-200 relative ${
          activeMode === 'stopwatch'
            ? 'bg-black text-white shadow-sm'
            : 'text-gray-600 hover:text-black hover:bg-white'
        }`}
      >
        <div className="flex items-center justify-center space-x-2">
          <span>⏱️</span>
          <span>Stopwatch</span>
        </div>
        {activeMode === 'stopwatch' && (
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-white rounded-full" />
        )}
      </button>
    </div>
  )
}

// Mobile-optimized tabs with swipe support
export function MobileTimerTabs({ activeMode, onModeChange, className = '' }: TimerTabsProps) {
  return (
    <div className={`flex bg-gray-50 rounded-lg p-1 ${className}`}>
      <button
        type="button"
        onClick={() => onModeChange('timer')}
        className={`flex-1 px-3 py-2.5 text-sm font-medium rounded-md transition-all duration-200 relative ${
          activeMode === 'timer'
            ? 'bg-black text-white shadow-sm'
            : 'text-gray-600 hover:text-black hover:bg-white'
        }`}
      >
        <div className="flex flex-col items-center space-y-1">
          <span className="text-lg">⏰</span>
          <span className="text-xs">Timer</span>
        </div>
        {activeMode === 'timer' && (
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-4 h-0.5 bg-white rounded-full" />
        )}
      </button>
      
      <button
        type="button"
        onClick={() => onModeChange('stopwatch')}
        className={`flex-1 px-3 py-2.5 text-sm font-medium rounded-md transition-all duration-200 relative ${
          activeMode === 'stopwatch'
            ? 'bg-black text-white shadow-sm'
            : 'text-gray-600 hover:text-black hover:bg-white'
        }`}
      >
        <div className="flex flex-col items-center space-y-1">
          <span className="text-lg">⏱️</span>
          <span className="text-xs">Stopwatch</span>
        </div>
        {activeMode === 'stopwatch' && (
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-4 h-0.5 bg-white rounded-full" />
        )}
      </button>
    </div>
  )
}

// Fullscreen tabs
export function FullscreenTimerTabs({ activeMode, onModeChange, className = '' }: TimerTabsProps) {
  return (
    <div className={`flex bg-gray-800 rounded-lg p-1 ${className}`}>
      <button
        type="button"
        onClick={() => onModeChange('timer')}
        className={`flex-1 px-6 py-4 text-base font-medium rounded-md transition-all duration-200 relative ${
          activeMode === 'timer'
            ? 'bg-white text-black shadow-sm'
            : 'text-gray-300 hover:text-white hover:bg-gray-700'
        }`}
      >
        <div className="flex items-center justify-center space-x-3">
          <span className="text-xl">⏰</span>
          <span>Timer</span>
        </div>
        {activeMode === 'timer' && (
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-black rounded-full" />
        )}
      </button>
      
      <button
        type="button"
        onClick={() => onModeChange('stopwatch')}
        className={`flex-1 px-6 py-4 text-base font-medium rounded-md transition-all duration-200 relative ${
          activeMode === 'stopwatch'
            ? 'bg-white text-black shadow-sm'
            : 'text-gray-300 hover:text-white hover:bg-gray-700'
        }`}
      >
        <div className="flex items-center justify-center space-x-3">
          <span className="text-xl">⏱️</span>
          <span>Stopwatch</span>
        </div>
        {activeMode === 'stopwatch' && (
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-black rounded-full" />
        )}
      </button>
    </div>
  )
}
