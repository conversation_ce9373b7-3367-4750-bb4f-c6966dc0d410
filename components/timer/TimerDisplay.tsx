import { formatTime, TimerState } from '../../hooks/useTimer'

interface TimerDisplayProps {
  timerState: TimerState
  className?: string
}

export default function TimerDisplay({ timerState, className = '' }: TimerDisplayProps) {
  const displayTime = timerState.mode === 'timer' 
    ? timerState.timeRemaining 
    : timerState.timeElapsed

  return (
    <div className={`text-center ${className}`}>
      <div className="text-4xl sm:text-5xl lg:text-6xl font-mono font-bold text-black mb-2">
        {formatTime(displayTime)}
      </div>
      <div className="text-sm text-gray-600">
        {timerState.mode === 'timer' ? 'Time Remaining' : 'Elapsed Time'}
      </div>
    </div>
  )
}

// Mobile-optimized display
export function MobileTimerDisplay({ timerState, className = '' }: TimerDisplayProps) {
  const displayTime = timerState.mode === 'timer' 
    ? timerState.timeRemaining 
    : timerState.timeElapsed

  return (
    <div className={`text-center ${className}`}>
      <div className="text-3xl font-mono font-bold text-black mb-1">
        {formatTime(displayTime)}
      </div>
      <div className="text-xs text-gray-600">
        {timerState.mode === 'timer' ? 'Time Remaining' : 'Elapsed Time'}
      </div>
    </div>
  )
}

// Fullscreen display
export function FullscreenTimerDisplay({ timerState, className = '' }: TimerDisplayProps) {
  const displayTime = timerState.mode === 'timer' 
    ? timerState.timeRemaining 
    : timerState.timeElapsed

  const timeString = formatTime(displayTime)
  
  // Dynamic font size based on time string length and content
  const getFontSizeClass = () => {
    const length = timeString.length
    if (length <= 4) { // M:SS
      return 'text-6xl sm:text-8xl lg:text-9xl'
    } else if (length === 5) { // MM:SS
      return 'text-5xl sm:text-7xl lg:text-8xl'
    } else if (length <= 8) { // H:MM:SS or HH:MM:SS
      return 'text-4xl sm:text-6xl lg:text-7xl'
    } else { // HH:MM:SS or longer
      return 'text-3xl sm:text-4xl lg:text-5xl'
    }
  }

  return (
    <div className={`text-center ${className}`}>
      <div className={`${getFontSizeClass()} font-mono font-bold text-white mb-4 leading-tight`}>
        {timeString}
      </div>
      <div className="text-lg sm:text-xl text-gray-300">
        {timerState.mode === 'timer' ? 'Time Remaining' : 'Elapsed Time'}
      </div>
    </div>
  )
}

// Status indicator
export function TimerStatus({ timerState, className = '' }: TimerDisplayProps) {
  const getStatusText = () => {
    if (timerState.isRunning && !timerState.isPaused) {
      return timerState.mode === 'timer' ? 'Counting Down' : 'Running'
    }
    if (timerState.isPaused) {
      return 'Paused'
    }
    return 'Stopped'
  }

  const getStatusColor = () => {
    if (timerState.isRunning && !timerState.isPaused) {
      return 'text-green-600'
    }
    if (timerState.isPaused) {
      return 'text-yellow-600'
    }
    return 'text-gray-600'
  }

  return (
    <div className={`text-center ${className}`}>
      <div className={`text-sm font-medium ${getStatusColor()}`}>
        {getStatusText()}
      </div>
    </div>
  )
}

// Fullscreen status
export function FullscreenTimerStatus({ timerState, className = '' }: TimerDisplayProps) {
  const getStatusText = () => {
    if (timerState.isRunning && !timerState.isPaused) {
      return timerState.mode === 'timer' ? 'Counting Down' : 'Running'
    }
    if (timerState.isPaused) {
      return 'Paused'
    }
    return 'Stopped'
  }

  const getStatusColor = () => {
    if (timerState.isRunning && !timerState.isPaused) {
      return 'text-green-400'
    }
    if (timerState.isPaused) {
      return 'text-yellow-400'
    }
    return 'text-gray-400'
  }

  return (
    <div className={`text-center ${className}`}>
      <div className={`text-base sm:text-lg font-medium ${getStatusColor()}`}>
        {getStatusText()}
      </div>
    </div>
  )
}
