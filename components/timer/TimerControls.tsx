import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Hi<PERSON><PERSON>e, HiP<PERSON>, HiStop } from 'react-icons/hi2'
import { TimerState } from '../../hooks/useTimer'

interface TimerControlsProps {
  timerState: TimerState
  onStart: () => void
  onPause: () => void
  onStop: () => void
  onReset: () => void
  className?: string
}

export default function TimerControls({
  timerState,
  onStart,
  onPause,
  onStop,
  onReset,
  className = ''
}: TimerControlsProps) {
  const isRunning = timerState.isRunning && !timerState.isPaused
  const canStart = !timerState.isRunning || timerState.isPaused
  const canStop = timerState.isRunning || timerState.timeElapsed > 0 || timerState.timeRemaining < timerState.initialTime

  return (
    <div className={`flex items-center justify-center space-x-4 ${className}`}>
      {/* Play/Pause Button - Rectangle */}
      {canStart ? (
        <button
          type="button"
          onClick={onStart}
          className="btn-primary flex items-center space-x-2 px-6 py-3 rounded-lg"
        >
          <HiPlay className="w-5 h-5" />
          <span>Start</span>
        </button>
      ) : (
        <button
          type="button"
          onClick={onPause}
          className="btn-secondary flex items-center space-x-2 px-6 py-3 rounded-lg"
        >
          <HiPause className="w-5 h-5" />
          <span>Pause</span>
        </button>
      )}

      {/* Stop Button - Rectangle */}
      <button
        type="button"
        onClick={onStop}
        className="btn-secondary flex items-center space-x-2 px-6 py-3 rounded-lg"
        disabled={!canStop}
      >
        <HiStop className="w-5 h-5" />
        <span>Stop</span>
      </button>

      {/* Reset Button - Circle */}
      <button
        type="button"
        onClick={onReset}
        className="w-12 h-12 rounded-full border border-black bg-white hover:bg-gray-50 transition-colors flex items-center justify-center"
        title="Reset"
      >
        <HiArrowPath className="w-5 h-5 text-black" />
      </button>
    </div>
  )
}

// Mobile-optimized controls
export function MobileTimerControls({
  timerState,
  onStart,
  onPause,
  onStop,
  onReset,
  className = ''
}: TimerControlsProps) {
  const isRunning = timerState.isRunning && !timerState.isPaused
  const canStart = !timerState.isRunning || timerState.isPaused
  const canStop = timerState.isRunning || timerState.timeElapsed > 0 || timerState.timeRemaining < timerState.initialTime

  return (
    <div className={`flex items-center justify-center space-x-3 ${className}`}>
      {/* Play/Pause Button - Larger for touch */}
      {canStart ? (
        <button
          type="button"
          onClick={onStart}
          className="btn-primary flex items-center space-x-2 px-5 py-3 rounded-lg min-h-[44px]"
        >
          <HiPlay className="w-5 h-5" />
          <span className="text-sm">Start</span>
        </button>
      ) : (
        <button
          type="button"
          onClick={onPause}
          className="btn-secondary flex items-center space-x-2 px-5 py-3 rounded-lg min-h-[44px]"
        >
          <HiPause className="w-5 h-5" />
          <span className="text-sm">Pause</span>
        </button>
      )}

      {/* Stop Button */}
      <button
        type="button"
        onClick={onStop}
        className="btn-secondary flex items-center space-x-2 px-5 py-3 rounded-lg min-h-[44px]"
        disabled={!canStop}
      >
        <HiStop className="w-5 h-5" />
        <span className="text-sm">Stop</span>
      </button>

      {/* Reset Button - Larger circle for touch */}
      <button
        type="button"
        onClick={onReset}
        className="w-11 h-11 rounded-full border border-black bg-white hover:bg-gray-50 transition-colors flex items-center justify-center min-h-[44px]"
        title="Reset"
      >
        <HiArrowPath className="w-5 h-5 text-black" />
      </button>
    </div>
  )
}

// Fullscreen controls
export function FullscreenTimerControls({
  timerState,
  onStart,
  onPause,
  onStop,
  onReset,
  className = ''
}: TimerControlsProps) {
  const isRunning = timerState.isRunning && !timerState.isPaused
  const canStart = !timerState.isRunning || timerState.isPaused
  const canStop = timerState.isRunning || timerState.timeElapsed > 0 || timerState.timeRemaining < timerState.initialTime

  return (
    <div className={`flex items-center justify-center space-x-3 sm:space-x-4 lg:space-x-6 ${className}`}>
      {/* Play/Pause Button - Responsive sizing */}
      {canStart ? (
        <button
          type="button"
          onClick={onStart}
          className="bg-white text-black hover:bg-gray-100 flex items-center space-x-2 sm:space-x-3 px-4 py-2 sm:px-6 sm:py-3 lg:px-8 lg:py-4 rounded-lg sm:rounded-xl text-sm sm:text-base lg:text-lg font-medium transition-colors"
        >
          <HiPlay className="w-5 h-5 sm:w-6 sm:h-6" />
          <span>Start</span>
        </button>
      ) : (
        <button
          type="button"
          onClick={onPause}
          className="bg-gray-700 text-white hover:bg-gray-600 flex items-center space-x-2 sm:space-x-3 px-4 py-2 sm:px-6 sm:py-3 lg:px-8 lg:py-4 rounded-lg sm:rounded-xl text-sm sm:text-base lg:text-lg font-medium transition-colors"
        >
          <HiPause className="w-5 h-5 sm:w-6 sm:h-6" />
          <span>Pause</span>
        </button>
      )}

      {/* Stop Button */}
      <button
        type="button"
        onClick={onStop}
        className="bg-gray-700 text-white hover:bg-gray-600 flex items-center space-x-2 sm:space-x-3 px-4 py-2 sm:px-6 sm:py-3 lg:px-8 lg:py-4 rounded-lg sm:rounded-xl text-sm sm:text-base lg:text-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        disabled={!canStop}
      >
        <HiStop className="w-5 h-5 sm:w-6 sm:h-6" />
        <span>Stop</span>
      </button>

      {/* Reset Button - Responsive circle */}
      <button
        type="button"
        onClick={onReset}
        className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-full border-2 border-white bg-transparent hover:bg-white hover:bg-opacity-20 transition-colors flex items-center justify-center"
        title="Reset"
      >
        <HiArrowPath className="w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 text-white" />
      </button>
    </div>
  )
}

// Compact controls for small spaces
export function CompactTimerControls({
  timerState,
  onStart,
  onPause,
  onStop,
  onReset,
  className = ''
}: TimerControlsProps) {
  const isRunning = timerState.isRunning && !timerState.isPaused
  const canStart = !timerState.isRunning || timerState.isPaused
  const canStop = timerState.isRunning || timerState.timeElapsed > 0 || timerState.timeRemaining < timerState.initialTime

  return (
    <div className={`flex items-center justify-center space-x-2 ${className}`}>
      {/* Play/Pause Button */}
      {canStart ? (
        <button
          type="button"
          onClick={onStart}
          className="btn-primary p-2 rounded-md"
          title="Start"
        >
          <HiPlay className="w-4 h-4" />
        </button>
      ) : (
        <button
          type="button"
          onClick={onPause}
          className="btn-secondary p-2 rounded-md"
          title="Pause"
        >
          <HiPause className="w-4 h-4" />
        </button>
      )}

      {/* Stop Button */}
      <button
        type="button"
        onClick={onStop}
        className="btn-secondary p-2 rounded-md"
        disabled={!canStop}
        title="Stop"
      >
        <HiStop className="w-4 h-4" />
      </button>

      {/* Reset Button */}
      <button
        type="button"
        onClick={onReset}
        className="w-8 h-8 rounded-full border border-black bg-white hover:bg-gray-50 transition-colors flex items-center justify-center"
        title="Reset"
      >
        <HiArrowPath className="w-3 h-3 text-black" />
      </button>
    </div>
  )
}
