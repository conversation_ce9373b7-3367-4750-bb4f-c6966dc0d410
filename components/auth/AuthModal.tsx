import { useCallback, useEffect, useState } from 'react'
import { HiXMark } from 'react-icons/hi2'
import { getAuthRedirectUrl } from '../../lib/config/environment'
import { supabase } from '../../lib/supabase'

interface AuthModalProps {
  isOpen: boolean
  onClose: () => void
  initialMode?: 'signin' | 'signup'
  onSuccess?: () => void
}

export default function AuthModal({ isOpen, onClose, initialMode = 'signin', onSuccess }: AuthModalProps) {
  const [mode, setMode] = useState<'signin' | 'signup'>(initialMode)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [username, setUsername] = useState('')
  const [displayName, setDisplayName] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [message, setMessage] = useState<string | null>(null)
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})

  // Reset form when modal opens/closes or mode changes
  useEffect(() => {
    if (!isOpen) {
      // Reset all form state when modal closes
      setEmail('')
      setPassword('')
      setUsername('')
      setDisplayName('')
      setError(null)
      setMessage(null)
      setValidationErrors({})
      setLoading(false)
    }
  }, [isOpen])

  // Update mode when initialMode prop changes
  useEffect(() => {
    setMode(initialMode)
  }, [initialMode])

  // Memoized validation function to prevent unnecessary re-renders
  const validateForm = useCallback(() => {
    const errors: {[key: string]: string} = {}

    if (!email.trim()) {
      errors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = 'Please enter a valid email address'
    }

    if (!password.trim()) {
      errors.password = 'Password is required'
    } else if (password.length < 6) {
      errors.password = 'Password must be at least 6 characters'
    }

    if (mode === 'signup') {
      if (!username.trim()) {
        errors.username = 'Username is required'
      } else if (username.length < 3) {
        errors.username = 'Username must be at least 3 characters'
      }
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }, [email, password, username, mode])

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form before submission
    if (!validateForm()) {
      return
    }

    setLoading(true)
    setError(null)
    setMessage(null)

    try {
      // Check if username is already taken
      const { data: existingUser } = await supabase
        .from('profiles')
        .select('username')
        .eq('username', username)
        .single()

      if (existingUser) {
        setError('Username is already taken')
        setLoading(false)
        return
      }

      // Sign up user
      const { data, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username,
            display_name: displayName || username,
          }
        }
      })

      if (signUpError) throw signUpError

      if (data.user) {
        setMessage('Check your email for the confirmation link!')
        // Reset form
        setEmail('')
        setPassword('')
        setUsername('')
        setDisplayName('')
      }
    } catch (error: any) {
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form before submission
    if (!validateForm()) {
      return
    }

    setLoading(true)
    setError(null)
    setMessage(null)

    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) throw error

      // Call success callback and close modal on successful sign in
      onSuccess?.()
      onClose()
    } catch (error: any) {
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  const handleGoogleSignIn = async () => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: getAuthRedirectUrl()
        }
      })
      if (error) throw error
    } catch (error: any) {
      setError(error.message)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full p-6 relative">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
          data-testid="auth-modal-close"
        >
          <HiXMark className="w-6 h-6" />
        </button>

        {/* Header */}
        <div className="mb-6">
          <h2 className="text-2xl font-bold mb-2">
            {mode === 'signin' ? 'Welcome back' : 'Join StudyShare'}
          </h2>
          <p className="text-gray-600">
            {mode === 'signin' 
              ? 'Sign in to your account to continue' 
              : 'Create an account to start sharing your learning journey'
            }
          </p>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md" data-testid="error-message">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}
        {message && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md" data-testid="success-message">
            <p className="text-green-800 text-sm">{message}</p>
          </div>
        )}

        {/* Form */}
        <form onSubmit={mode === 'signin' ? handleSignIn : handleSignUp} className="space-y-4">
          {mode === 'signup' && (
            <>
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
                  Username *
                </label>
                <input
                  id="username"
                  name="username"
                  type="text"
                  value={username}
                  onChange={(e) => {
                    setUsername(e.target.value)
                    if (validationErrors.username) {
                      setValidationErrors(prev => ({ ...prev, username: '' }))
                    }
                  }}
                  className={`input ${validationErrors.username ? 'border-red-500' : ''}`}
                  placeholder="your_username"
                  required
                  data-testid="username-input"
                />
                {validationErrors.username && (
                  <p className="text-red-500 text-sm mt-1" data-testid="username-error">
                    {validationErrors.username}
                  </p>
                )}
              </div>
              <div>
                <label htmlFor="displayName" className="block text-sm font-medium text-gray-700 mb-1">
                  Display Name
                </label>
                <input
                  id="displayName"
                  name="displayName"
                  type="text"
                  value={displayName}
                  onChange={(e) => setDisplayName(e.target.value)}
                  className="input"
                  placeholder="Your Display Name"
                  data-testid="display-name-input"
                />
              </div>
            </>
          )}
          
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email *
            </label>
            <input
              id="email"
              name="email"
              type="email"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value)
                if (validationErrors.email) {
                  setValidationErrors(prev => ({ ...prev, email: '' }))
                }
              }}
              className={`input ${validationErrors.email ? 'border-red-500' : ''}`}
              placeholder="<EMAIL>"
              required
              data-testid="email-input"
            />
            {validationErrors.email && (
              <p className="text-red-500 text-sm mt-1" data-testid="email-error">
                {validationErrors.email}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Password *
            </label>
            <input
              id="password"
              name="password"
              type="password"
              value={password}
              onChange={(e) => {
                setPassword(e.target.value)
                if (validationErrors.password) {
                  setValidationErrors(prev => ({ ...prev, password: '' }))
                }
              }}
              className={`input ${validationErrors.password ? 'border-red-500' : ''}`}
              placeholder="••••••••"
              required
              minLength={6}
              data-testid="password-input"
            />
            {validationErrors.password && (
              <p className="text-red-500 text-sm mt-1" data-testid="password-error">
                {validationErrors.password}
              </p>
            )}
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            data-testid="auth-submit"
          >
            {loading ? 'Loading...' : (mode === 'signin' ? 'Sign In' : 'Sign Up')}
          </button>
        </form>

        {/* Divider */}
        <div className="my-6 flex items-center">
          <div className="flex-1 border-t border-gray-300"></div>
          <span className="px-4 text-sm text-gray-500">or</span>
          <div className="flex-1 border-t border-gray-300"></div>
        </div>

        {/* Google Sign In */}
        <button
          onClick={handleGoogleSignIn}
          className="w-full btn-secondary mb-4"
          data-testid="google-signin"
        >
          Continue with Google
        </button>

        {/* Mode Switch */}
        <div className="text-center">
          <p className="text-sm text-gray-600">
            {mode === 'signin' ? "Don't have an account? " : "Already have an account? "}
            <button
              onClick={() => {
                setMode(mode === 'signin' ? 'signup' : 'signin')
                setError(null)
                setMessage(null)
                setValidationErrors({})
              }}
              className="text-black font-medium hover:underline"
              data-testid="auth-mode-toggle"
            >
              {mode === 'signin' ? 'Sign up' : 'Sign in'}
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}
