import { ReactNode } from 'react'
import { CompactRankBadge } from '../gamification/RankBadge'

interface TimelineItemProps {
  author: {
    display_name?: string | null
    username: string
    rank_level?: number
  }
  timeAgo: string
  children: ReactNode
  actions: ReactNode
}

export default function TimelineItem({ author, timeAgo, children, actions }: TimelineItemProps) {
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'now'
    if (diffInHours < 24) return `${diffInHours}h`
    return `${Math.floor(diffInHours / 24)}d`
  }

  return (
    <div className="timeline-item">
      {/* Avatar */}
      <div className="timeline-avatar">
        <span className="text-sm font-medium text-gray-700">
          {author.display_name?.charAt(0) || author.username.charAt(0)}
        </span>
      </div>

      {/* Content */}
      <div className="timeline-content">
        {/* Header */}
        <div className="timeline-header">
          <span className="timeline-username">
            {author.display_name || author.username}
          </span>
          <span className="timeline-handle">@{author.username}</span>
          <span className="timeline-time">·</span>
          <span className="timeline-time">{formatTimeAgo(timeAgo)}</span>
          {author.rank_level && (
            <CompactRankBadge level={author.rank_level} />
          )}
        </div>

        {/* Main Content */}
        <div className="timeline-main-content">
          {children}
        </div>

        {/* Actions */}
        <div className="timeline-actions">
          {actions}
        </div>
      </div>
    </div>
  )
}
