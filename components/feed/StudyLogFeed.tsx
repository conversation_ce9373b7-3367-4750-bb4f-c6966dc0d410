import { Hi<PERSON>rrowPath, HiOutlineBookOpen } from 'react-icons/hi2'
import { StudyLogWithRecipe } from '../../lib/api/studyLogs'
import { useAuth } from '../../lib/auth'
import StudyLogCardSkeleton from './StudyLogCardSkeleton'
import StudyLogTimeline from './StudyLogTimeline'

interface StudyLogFeedProps {
  logs: StudyLogWithRecipe[]
  loading: boolean
  error: string | null
  onRefresh: () => void
  onAuthRequired?: (action: () => void) => void
}

const StudyLogFeed = ({ logs, loading, error, onRefresh, onAuthRequired }: StudyLogFeedProps) => {
  const { user } = useAuth()

  if (loading) {
    return (
      <div className="space-y-6">
        {[...Array(3)].map((_, index) => (
          <StudyLogCardSkeleton key={index} />
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <p className="text-lg font-medium">Error loading study logs</p>
          <p className="text-sm">{error}</p>
        </div>
        <button
          onClick={onRefresh}
          className="btn-secondary flex items-center mx-auto"
        >
          <HiArrowPath className="w-4 h-4 mr-2" />
          Try Again
        </button>
      </div>
    )
  }

  if (!logs || logs.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <HiOutlineBookOpen className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No study logs yet</h3>
        <p className="text-gray-600 mb-6">
          {user 
            ? "Start your first study session to see logs here!" 
            : "Sign in to see study logs from the community"
          }
        </p>
        {user && (
          <button
            onClick={onRefresh}
            className="btn-secondary flex items-center mx-auto"
          >
            <HiArrowPath className="w-4 h-4 mr-2" />
            Refresh
          </button>
        )}
      </div>
    )
  }

  return (
    <div>
      {/* Study Log Timeline Items */}
      {logs.map((log) => (
        <StudyLogTimeline
          key={log.id}
          log={log}
          currentUser={user}
          onAuthRequired={onAuthRequired}
        />
      ))}
      
      {/* Load More Button (for future pagination) */}
      {logs.length >= 20 && (
        <div className="text-center py-6">
          <button
            onClick={onRefresh}
            className="btn-secondary"
          >
            Load More
          </button>
        </div>
      )}
    </div>
  )
}

export default StudyLogFeed
