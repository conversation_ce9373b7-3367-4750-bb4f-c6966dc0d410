import { useRouter } from 'next/router'
import { useState } from 'react'
import { HiOutlineFire } from 'react-icons/hi2'
import { RecipeWithMaterials } from '../../lib/api/recipes'
import TimelineActions from './TimelineActions'
import TimelineItem from './TimelineItem'

interface RecipeTimelineProps {
  recipe: RecipeWithMaterials
  onUpvote: (recipeId: string) => void
  onBookmark: (recipeId: string) => void
  isUpvoting: boolean
  isBookmarking: boolean
  isBookmarked: boolean
  isUpvoted: boolean
  currentUser: any
  onAuthRequired?: (action: () => void) => void
}

export default function RecipeTimeline({
  recipe,
  onUpvote,
  onBookmark,
  isUpvoting,
  isBookmarking,
  isBookmarked,
  isUpvoted,
  currentUser,
  onAuthRequired
}: RecipeTimelineProps) {
  const router = useRouter()
  const [showComments, setShowComments] = useState(false)
  const [commentCount, setCommentCount] = useState(recipe.comment_count || 0)

  const handleUpvoteClick = () => {
    console.log('🖱️ Timeline: Upvote button clicked for recipe:', recipe.id)
    if (!currentUser) {
      console.log('❌ Timeline: No current user')
      return
    }
    console.log('🚀 Timeline: Calling onUpvote')
    onUpvote(recipe.id)
  }

  const handleBookmarkClick = () => {
    if (!currentUser) return
    onBookmark(recipe.id)
  }

  const handleCommentClick = () => {
    router.push(`/recipes/${recipe.id}#comments`)
  }

  // Convert frequency to English
  const convertFrequencyToEnglish = (frequency: string) => {
    const frequencyMap: { [key: string]: string } = {
      'daily': 'daily',
      '毎日': 'daily',
      'weekly': 'weekly',
      '6_times_week': '6x/week',
      '6x/week': '6x/week',
      '週6回': '6x/week',
      '5_times_week': '5x/week',
      '5x/week': '5x/week',
      '週5回': '5x/week',
      '4_times_week': '4x/week',
      '4x/week': '4x/week',
      '週4回': '4x/week',
      '3_times_week': '3x/week',
      '3x/week': '3x/week',
      '週3回': '3x/week',
      '2_times_week': '2x/week',
      '2x/week': '2x/week',
      '週2回': '2x/week',
      '1_time_week': 'weekly',
      '1x/week': 'weekly',
      '週1回': 'weekly',
      '4_times_month': '4x/month',
      '月4回': '4x/month',
      '3_times_month': '3x/month',
      '月3回': '3x/month',
      '2_times_month': '2x/month',
      '月2回': '2x/month'
    }
    return frequencyMap[frequency] || frequency
  }

  // Format estimated duration to short form (e.g., "1時間30分" -> "1h 30m")
  const formatEstimatedDuration = (duration: string | undefined) => {
    if (!duration) return 'Flexible'
    
    let result = duration
    
    // First, handle Japanese units and convert to English
    result = result.replace(/(\d+)時間(\d+)分/g, '$1 hours $2 minutes')
    result = result.replace(/(\d+)時間/g, '$1 hours')
    result = result.replace(/(\d+)分/g, '$1 minutes')
    result = result.replace(/ヶ月/g, ' months')
    result = result.replace(/か月/g, ' months')
    result = result.replace(/月/g, ' months')
    result = result.replace(/週間/g, ' weeks')
    result = result.replace(/週/g, ' weeks')
    result = result.replace(/日/g, ' days')
    result = result.replace(/年/g, ' years')
    
    // Then, convert English to short form
    result = result.replace(/(\d+)\s*hours\s*(\d+)\s*minutes/gi, '$1h $2m')
    result = result.replace(/(\d+)\s*hours/gi, '$1h')
    result = result.replace(/(\d+)\s*minutes/gi, '$1m')
    result = result.replace(/(\d+)\s*mins/gi, '$1m')
    
    // Handle special formats like "25分×8セット"
    result = result.replace(/(\d+)\s*m×(\d+)セット/gi, '$1m×$2 sets')
    result = result.replace(/×(\d+)セット/gi, '×$1 sets')
    
    return result.trim()
  }

  // Calculate total study time per week
  const calculateWeeklyTime = () => {
    if (!recipe.materials || recipe.materials.length === 0) return null

    let totalMinutesPerWeek = 0
    recipe.materials.forEach(material => {
      const timePerSession = material.time_per_session
      let sessionsPerWeek = 0

      switch (material.frequency.toLowerCase()) {
        case 'daily':
          sessionsPerWeek = 7
          break
        case '6_times_week':
        case '6x/week':
          sessionsPerWeek = 6
          break
        case '5_times_week':
        case '5x/week':
          sessionsPerWeek = 5
          break
        case '4_times_week':
        case '4x/week':
          sessionsPerWeek = 4
          break
        case '3_times_week':
        case '3x/week':
          sessionsPerWeek = 3
          break
        case '2_times_week':
        case '2x/week':
          sessionsPerWeek = 2
          break
        case '1_time_week':
        case 'weekly':
        case '1x/week':
          sessionsPerWeek = 1
          break
        case '4_times_month':
          sessionsPerWeek = 1 // 月4回 ≈ 週1回
          break
        case '3_times_month':
          sessionsPerWeek = 0.75 // 月3回 ≈ 週0.75回
          break
        case '2_times_month':
          sessionsPerWeek = 0.5 // 月2回 ≈ 週0.5回
          break
        default:
          sessionsPerWeek = 1
      }

      totalMinutesPerWeek += timePerSession * sessionsPerWeek
    })

    const hours = Math.floor(totalMinutesPerWeek / 60)
    const minutes = totalMinutesPerWeek % 60

    if (hours > 0 && minutes > 0) {
      return `${hours}h ${minutes}m`
    } else if (hours > 0) {
      return `${hours}h`
    } else {
      return `${minutes}m`
    }
  }

  const recipeContent = (
    <div className="space-y-3">
      {/* Recipe Title */}
      <h2
        className="text-xl font-bold text-gray-900 leading-tight cursor-pointer hover:text-gray-700 transition-colors"
        onClick={() => router.push(`/recipes/${recipe.id}`)}
        data-testid="recipe-title"
      >
        {recipe.title}
      </h2>

      {/* Popular Recipe Indicator */}
      {recipe.upvote_count >= 10 && (
        <div className="flex items-center space-x-1 mb-2">
          <HiOutlineFire className="w-4 h-4 text-orange-500" />
          <span className="text-sm text-orange-600 font-medium">Popular Recipe</span>
        </div>
      )}

      {/* Meta Information */}
      <div className="grid grid-cols-2 gap-4">
        {/* Estimated Duration */}
        <div className="flex items-center space-x-2">
          <span className="text-lg">🎯</span>
          <div>
            <div className="text-sm text-gray-600">To Goal</div>
            <div className="font-medium text-black">{formatEstimatedDuration(recipe.estimated_duration)}</div>
          </div>
        </div>

        {/* Time per week */}
        <div className="flex items-center space-x-2">
          <span className="text-lg">⏱️</span>
          <div>
            <div className="text-sm text-gray-600">Per Week</div>
            <div className="font-medium text-black">{calculateWeeklyTime() || '2h'}</div>
          </div>
        </div>
      </div>

      {/* Materials Section */}
      {recipe.materials && recipe.materials.length > 0 && (
        <div className="space-y-3">
          {recipe.materials.map((material) => (
            <div key={material.id} className="flex justify-between items-center py-2 border-b border-gray-100">
              <span className="text-gray-900 font-medium">{material.material_name}</span>
              <span className="text-gray-600">
                {material.time_per_session} {material.time_unit === 'minutes' ? 'min' : material.time_unit === 'hours' ? 'h' : material.time_unit}/{convertFrequencyToEnglish(material.frequency)}
              </span>
            </div>
          ))}
        </div>
      )}

      {/* Tips Section */}
      {recipe.tips && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-sm font-medium text-gray-800">Tips & Notes:</span>
          </div>
          <div className="text-gray-700 leading-relaxed whitespace-pre-wrap">
            {recipe.tips}
          </div>
        </div>
      )}

      {/* Tags - Moved after Tips & Notes */}
      {recipe.tags && recipe.tags.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {recipe.tags.map((tag) => (
            <span key={tag} className="px-2 py-1 bg-black text-white text-xs rounded-full">
              #{tag}
            </span>
          ))}
        </div>
      )}
    </div>
  )

  const actions = (
    <TimelineActions
      type="recipe"
      commentCount={commentCount}
      onCommentClick={handleCommentClick}
      upvoteCount={recipe.upvote_count || 0}
      isUpvoted={isUpvoted}
      isBookmarked={isBookmarked}
      onUpvoteClick={handleUpvoteClick}
      onBookmarkClick={handleBookmarkClick}
      isUpvoting={isUpvoting}
      isBookmarking={isBookmarking}
      disabled={false}
      currentUser={currentUser}
      onAuthRequired={onAuthRequired}
    />
  )

  return (
    <TimelineItem
      author={recipe.author}
      timeAgo={recipe.created_at}
      actions={actions}
    >
      {recipeContent}
    </TimelineItem>
  )
}
