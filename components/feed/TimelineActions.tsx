import React from 'react'
import { HiArrowUp, <PERSON>Bookmark, HiOutlineArrowUp, HiOutlineBookmark, HiOutlineChatBubbleLeft } from 'react-icons/hi2'

interface TimelineActionsProps {
  type: 'studylog' | 'recipe'
  
  // Common actions
  commentCount: number
  onCommentClick: () => void
  
  // Study log specific (reactions)
  reactions?: {
    reactions: Array<{ emoji: string; count: number }>
    userReactions: string[]
  }
  onEmojiSelect?: (emoji: string) => void
  
  // Recipe specific (upvote, bookmark)
  upvoteCount?: number
  isUpvoted?: boolean
  isBookmarked?: boolean
  onUpvoteClick?: () => void
  onBookmarkClick?: () => void
  
  // Loading states
  isUpvoting?: boolean
  isBookmarking?: boolean
  disabled?: boolean
  
  // Auth handling
  onAuthRequired?: (action: () => void) => void
  currentUser?: any
}

export default function TimelineActions({
  type,
  commentCount,
  onCommentClick,
  reactions,
  onEmojiSelect,
  upvoteCount = 0,
  isUpvoted = false,
  isBookmarked = false,
  onUpvoteClick,
  onBookmarkClick,
  isUpvoting = false,
  isBookmarking = false,
  disabled = false,
  onAuthRequired,
  currentUser
}: TimelineActionsProps) {
  // Calculate total reactions for study logs
  const totalReactions = reactions?.reactions.reduce((sum, r) => sum + r.count, 0) || 0
  
  // State for reaction picker
  const [showReactionPicker, setShowReactionPicker] = React.useState(false)

  // Handle actions with auth check
  const handleActionWithAuth = (action: () => void) => {
    if (!currentUser) {
      onAuthRequired?.(action)
      return
    }
    action()
  }

  const handleUpvoteClick = () => {
    handleActionWithAuth(() => onUpvoteClick?.())
  }

  const handleBookmarkClick = () => {
    handleActionWithAuth(() => onBookmarkClick?.())
  }

  const handleEmojiClick = (emoji: string) => {
    handleActionWithAuth(() => {
      onEmojiSelect?.(emoji)
      setShowReactionPicker(false)
    })
  }

  return (
    <div className="flex items-center space-x-2">
      {type === 'recipe' && (
        // Upvote Action - Far left with clear labeling
        <button
          onClick={handleUpvoteClick}
          disabled={disabled || isUpvoting}
          className={`flex items-center space-x-2 px-3 py-1.5 rounded-full transition-all duration-200 ${
            isUpvoted 
              ? 'bg-orange-50 text-orange-600 hover:bg-orange-100' 
              : !currentUser
              ? 'text-gray-400 hover:bg-gray-50 hover:text-orange-400'
              : 'text-gray-600 hover:bg-gray-50 hover:text-orange-600'
          }`}
          data-testid="upvote-button"
        >
          {isUpvoting ? (
            <div className="w-4 h-4 border-2 border-gray-300 border-t-orange-600 rounded-full animate-spin" />
          ) : isUpvoted ? (
            <HiArrowUp className="w-4 h-4" />
          ) : (
            <HiOutlineArrowUp className="w-4 h-4" />
          )}
          <span className="text-sm font-medium">
            {isUpvoted ? 'Upvoted' : 'Upvote'}
          </span>
          {upvoteCount > 0 && (
            <span className="text-sm font-medium">{upvoteCount}</span>
          )}
        </button>
      )}

      {/* Comment Action */}
      <button
        onClick={onCommentClick}
        disabled={disabled}
        className="flex items-center space-x-2 text-gray-600 hover:bg-gray-50 hover:text-blue-600 px-3 py-1.5 rounded-full transition-all duration-200"
        data-testid="comment-button"
      >
        <HiOutlineChatBubbleLeft className="w-4 h-4" />
        <span className="text-sm font-medium hidden md:inline">Comment</span>
        {commentCount > 0 && <span className="text-sm font-medium">{commentCount}</span>}
      </button>

      {/* Study Log Reaction Action */}
      {type === 'studylog' && onEmojiSelect && reactions && (
        <div className="relative">
          <button
            onClick={() => setShowReactionPicker(!showReactionPicker)}
            disabled={disabled}
            className="flex items-center space-x-2 px-3 py-1.5 rounded-full text-gray-600 hover:bg-gray-50 hover:text-yellow-600 transition-all duration-200"
            data-testid="reaction-button"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm font-medium hidden md:inline">React</span>
            {totalReactions > 0 && <span className="text-sm font-medium">{totalReactions}</span>}
          </button>

          {/* Reaction Picker Dropdown */}
          {showReactionPicker && (
            <div className="absolute bottom-full left-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg p-2 z-50 min-w-max">
              <div className="flex gap-1">
                {['👍', '🔥', '🎉', '💪', '💯', '❤️'].map((emoji) => (
                  <button
                    key={emoji}
                    onClick={() => handleEmojiClick(emoji)}
                    disabled={disabled}
                    className="text-xl p-2 rounded-lg transition-all hover:scale-110 hover:bg-gray-100"
                  >
                    {emoji}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {type === 'recipe' && (
        // Recipe Actions: Bookmark (after comment)
        <button
          onClick={handleBookmarkClick}
          disabled={disabled || isBookmarking}
          className={`flex items-center space-x-2 px-3 py-1.5 rounded-full transition-all duration-200 ${
            isBookmarked 
              ? 'text-green-600 hover:bg-green-50' 
              : !currentUser
              ? 'text-gray-400 hover:bg-gray-50 hover:text-green-400'
              : 'text-gray-600 hover:bg-gray-50 hover:text-green-600'
          }`}
          data-testid="bookmark-button"
        >
          {isBookmarking ? (
            <div className="w-4 h-4 border-2 border-gray-300 border-t-green-600 rounded-full animate-spin" />
          ) : isBookmarked ? (
            <HiBookmark className="w-4 h-4" />
          ) : (
            <HiOutlineBookmark className="w-4 h-4" />
          )}
          <span className="text-sm font-medium hidden md:inline">Bookmark</span>
        </button>
      )}
    </div>
  )
}
