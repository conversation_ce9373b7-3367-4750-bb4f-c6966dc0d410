import { useEffect, useRef, useState } from 'react'
import { HiOutlineBookOpen, HiOutlineFire } from 'react-icons/hi2'
import { addReaction, getLogReactions, removeReaction } from '../../lib/api/reactions'
import { StudyLogWithRecipe } from '../../lib/api/studyLogs'
import { calculateStudyExperience } from '../../lib/gamification/rankingSystem'
import { LogReactions } from '../../types'
import CommentList from '../comments/CommentList'
import ReactionDisplay from '../reactions/ReactionDisplay'
import TimelineActions from './TimelineActions'
import TimelineItem from './TimelineItem'

interface StudyLogTimelineProps {
  log: StudyLogWithRecipe
  currentUser: any
  showExperienceGained?: boolean
  onAuthRequired?: (action: () => void) => void
  isDetailPage?: boolean
}

export default function StudyLogTimeline({ log, currentUser, showExperienceGained = true, onAuthRequired, isDetailPage = false }: StudyLogTimelineProps) {
  const router = typeof window !== 'undefined' ? require('next/router').useRouter() : null
  const [reactions, setReactions] = useState<LogReactions>({ reactions: [], userReactions: [] })
  const [loadingEmoji, setLoadingEmoji] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [showComments, setShowComments] = useState(false)
  const [commentCount, setCommentCount] = useState(log.comment_count || 0)
  const [showReactionPicker, setShowReactionPicker] = useState(false)
  const pendingOperationsRef = useRef<Set<string>>(new Set())

  const handleCommentClick = () => {
    if (isDetailPage) {
      // In detail page, just scroll to comments section
      const commentsElement = document.getElementById('comments')
      if (commentsElement) {
        commentsElement.scrollIntoView({ behavior: 'smooth' })
      }
    } else if (router) {
      router.push(`/study-logs/${log.id}#comments`)
    }
  }

  // Load initial reactions
  useEffect(() => {
    const loadInitialReactions = async () => {
      try {
        const logReactions = await getLogReactions(log.id)
        setReactions(logReactions)
      } catch (error) {
        console.error('Error loading initial reactions:', error)
      }
    }

    loadInitialReactions()
  }, [log.id])

  const handleEmojiToggle = async (emoji: string) => {
    if (!currentUser || isLoading || pendingOperationsRef.current.has(emoji)) {
      console.log('🚫 Reaction blocked:', {
        hasUser: !!currentUser,
        isLoading,
        isPending: pendingOperationsRef.current.has(emoji)
      })
      return
    }

    // Add to pending operations to prevent race conditions
    pendingOperationsRef.current.add(emoji)
    setLoadingEmoji(emoji)
    setIsLoading(true)

    try {
      const isUserReaction = reactions.userReactions.includes(emoji)
      console.log(`🔄 ${isUserReaction ? 'Removing' : 'Adding'} reaction:`, emoji)

      // Optimistic update
      const newUserReactions = isUserReaction
        ? reactions.userReactions.filter(e => e !== emoji)
        : [...reactions.userReactions, emoji]

      const newReactions = reactions.reactions.map(r => {
        if (r.emoji === emoji) {
          return {
            ...r,
            count: isUserReaction ? Math.max(0, r.count - 1) : r.count + 1,
            users: isUserReaction
              ? r.users.filter(id => id !== currentUser.id)
              : [...r.users, currentUser.id]
          }
        }
        return r
      })

      // If emoji doesn't exist and we're adding it
      if (!isUserReaction && !reactions.reactions.find(r => r.emoji === emoji)) {
        newReactions.push({
          emoji,
          count: 1,
          users: [currentUser.id]
        })
      }

      // Filter out reactions with 0 count
      const filteredReactions = newReactions.filter(r => r.count > 0)

      const optimisticReactions = {
        reactions: filteredReactions,
        userReactions: newUserReactions
      }

      setReactions(optimisticReactions)

      // Make API call
      if (isUserReaction) {
        await removeReaction(log.id, emoji)
      } else {
        await addReaction(log.id, emoji)
      }

      // Refresh data from server to ensure consistency
      const freshReactions = await getLogReactions(log.id)
      setReactions(freshReactions)
      console.log('✅ Reaction updated successfully:', emoji)

    } catch (error) {
      console.error('❌ Error toggling reaction:', error)

      // Revert optimistic update on error
      try {
        const revertedReactions = await getLogReactions(log.id)
        setReactions(revertedReactions)
      } catch (revertError) {
        console.error('❌ Error reverting reactions:', revertError)
      }
    } finally {
      // Clean up loading states
      pendingOperationsRef.current.delete(emoji)
      setLoadingEmoji(null)
      setIsLoading(false)
    }
  }

  const formatDuration = (totalSeconds: number) => {
    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = totalSeconds % 60

    if (hours > 0) {
      if (minutes > 0 && seconds > 0) {
        return `${hours}h ${minutes}m ${seconds}s`
      } else if (minutes > 0) {
        return `${hours}h ${minutes}m`
      } else if (seconds > 0) {
        return `${hours}h ${seconds}s`
      } else {
        return `${hours}h`
      }
    } else if (minutes > 0) {
      if (seconds > 0) {
        return `${minutes}m ${seconds}s`
      } else {
        return `${minutes}m`
      }
    } else {
      return `${seconds}s`
    }
  }


  const studyContent = (
    <div className="space-y-3">
      {/* Study Session Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <HiOutlineBookOpen className="w-5 h-5 text-gray-600" />
          <span className="font-medium text-gray-900">Completed study session</span>
        </div>
        {showExperienceGained && (
          <div className="flex items-center space-x-2">
            <span className="text-sm text-green-600 font-medium">
              +{calculateStudyExperience(log.duration_minutes)} XP
            </span>
            {log.duration_minutes >= 60 && (
              <div className="flex items-center space-x-1">
                <HiOutlineFire className="w-4 h-4 text-orange-500" />
                <span className="text-sm font-medium text-orange-600">Long session!</span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Linked Recipe */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-2">
          <span className="text-sm font-medium text-gray-600">Study Content:</span>
        </div>
        <h3 
          className={`font-semibold text-gray-900 ${!isDetailPage ? 'cursor-pointer hover:text-gray-700 transition-colors' : ''}`}
          data-testid="study-log-title"
          onClick={() => !isDetailPage && router && router.push(`/study-logs/${log.id}`)}
        >
          {log.title}
        </h3>
      </div>

      {/* Study Session Details */}
      <div className="flex flex-col space-y-3 sm:grid sm:grid-cols-2 sm:gap-6 sm:space-y-0">
        <div className="flex items-center space-x-2">
          <span className="text-lg">⏱️</span>
          <div>
            <div className="text-sm text-gray-600">Duration</div>
            <div className="font-medium text-black">{formatDuration(log.duration_seconds || log.duration_minutes * 60)}</div>
          </div>
        </div>
        
        {/* Material Information */}
        {log.material && (
          <div className="flex items-center space-x-2">
            <span className="text-lg">📚</span>
            <div>
              <div className="text-sm text-gray-600">Material</div>
              <div className="font-medium text-black">{log.material.material_name}</div>
            </div>
          </div>
        )}
      </div>

      {/* Notes */}
      {log.notes && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-sm font-medium text-blue-800">Session Notes:</span>
          </div>
          <div className="text-gray-700 leading-relaxed whitespace-pre-wrap">
            {log.notes}
          </div>
        </div>
      )}

      {/* Reactions Display */}
      <ReactionDisplay
        reactions={reactions}
        onEmojiToggle={currentUser ? handleEmojiToggle : (emoji: string) => onAuthRequired?.(() => handleEmojiToggle(emoji))}
        loadingEmoji={loadingEmoji}
        disabled={isLoading}
        onAuthRequired={onAuthRequired}
      />
    </div>
  )

  const actions = (
    <div className="space-y-4">
      {/* Action buttons */}
      <TimelineActions
        type="studylog"
        commentCount={commentCount}
        onCommentClick={handleCommentClick}
        reactions={reactions}
        onEmojiSelect={handleEmojiToggle}
        disabled={false}
        currentUser={currentUser}
        onAuthRequired={onAuthRequired}
      />

      {/* Comments Section */}
      <CommentList
        logId={log.id}
        currentUser={currentUser}
        initialCommentCount={log.comment_count || 0}
        isOpen={isDetailPage || showComments}
        onCommentCountChange={setCommentCount}
        onAuthRequired={onAuthRequired}
      />
    </div>
  )

  return (
    <TimelineItem
      author={log.author}
      timeAgo={log.completed_at}
      actions={actions}
    >
      {studyContent}
    </TimelineItem>
  )
}
