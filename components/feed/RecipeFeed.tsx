import { useEffect, useState } from 'react'
import { HiArrowPath } from 'react-icons/hi2'
import { getUserBookmarks, toggleBookmark } from '../../lib/api/bookmarks'
import { getUserUpvotes, RecipeWithMaterials, upvoteRecipe } from '../../lib/api/recipes'
import { useAuth } from '../../lib/auth'
import RecipeCardSkeleton from './RecipeCardSkeleton'
import RecipeTimeline from './RecipeTimeline'

interface RecipeFeedProps {
  recipes: RecipeWithMaterials[]
  loading: boolean
  error: string | null
  onRefresh: () => void
  sortBy?: 'latest' | 'trending'
  onSortChange?: (sortBy: 'latest' | 'trending') => void
  onAuthRequired?: (action: () => void) => void
}

const RecipeFeed = ({ recipes, loading, error, onRefresh, sortBy = 'latest', onSortChange, onAuthRequired }: RecipeFeedProps) => {
  const { user } = useAuth()
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const [bookmarkedRecipes, setBookmarkedRecipes] = useState<string[]>([])
  const [upvotedRecipes, setUpvotedRecipes] = useState<string[]>([])
  // Local state for optimistic updates
  const [localRecipes, setLocalRecipes] = useState<RecipeWithMaterials[]>(recipes)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)

  // Update local recipes when props change
  useEffect(() => {
    console.log('🔄 RecipeFeed: Updating local recipes, count:', recipes.length)
    setLocalRecipes(recipes)
  }, [recipes])

  // Load user's bookmarks and upvotes when component mounts or user changes
  useEffect(() => {
    if (user) {
      loadUserBookmarks()
      loadUserUpvotes()
    } else {
      setBookmarkedRecipes([])
      setUpvotedRecipes([])
    }
  }, [user])

  const loadUserBookmarks = async () => {
    try {
      const bookmarks = await getUserBookmarks()
      setBookmarkedRecipes(bookmarks)
    } catch (error) {
      console.error('Error loading bookmarks:', error)
    }
  }

  const loadUserUpvotes = async () => {
    try {
      const upvotes = await getUserUpvotes()
      setUpvotedRecipes(upvotes)
    } catch (error) {
      console.error('Error loading upvotes:', error)
    }
  }

  const handleUpvote = async (recipeId: string) => {
    if (!user) return

    console.log('🔄 Starting optimistic upvote for recipe:', recipeId)
    setActionLoading(`upvote-${recipeId}`)

    // Get current state
    const isCurrentlyUpvoted = upvotedRecipes.includes(recipeId)
    const currentRecipe = localRecipes.find(r => r.id === recipeId)

    if (!currentRecipe) {
      console.error('❌ Recipe not found in local state')
      setActionLoading(null)
      return
    }

    // Optimistic updates - update UI immediately
    const newUpvotedState = isCurrentlyUpvoted
      ? upvotedRecipes.filter(id => id !== recipeId)
      : [...upvotedRecipes, recipeId]

    const newUpvoteCount = isCurrentlyUpvoted
      ? Math.max(0, (currentRecipe.upvote_count || 0) - 1)
      : (currentRecipe.upvote_count || 0) + 1

    console.log('🚀 Optimistic update:', {
      recipeId,
      wasUpvoted: isCurrentlyUpvoted,
      newCount: newUpvoteCount,
      newUpvotedState
    })

    // Apply optimistic updates
    setUpvotedRecipes(newUpvotedState)
    setLocalRecipes(prev => prev.map(recipe =>
      recipe.id === recipeId
        ? { ...recipe, upvote_count: newUpvoteCount }
        : recipe
    ))

    try {
      const result = await upvoteRecipe(recipeId)
      console.log('✅ Server upvote result:', result)

      // Verify server state matches our optimistic update
      if (result.voted !== !isCurrentlyUpvoted) {
        console.warn('⚠️ Server state mismatch, correcting...')
        setUpvotedRecipes(prev => result.voted
          ? [...prev.filter(id => id !== recipeId), recipeId]
          : prev.filter(id => id !== recipeId)
        )
      }

      // Update with actual server count if provided
      if (result.newCount !== undefined) {
        setLocalRecipes(prev => prev.map(recipe =>
          recipe.id === recipeId
            ? { ...recipe, upvote_count: result.newCount }
            : recipe
        ))
      }

    } catch (error) {
      console.error('❌ Error upvoting recipe, reverting optimistic update:', error)

      // Revert optimistic updates on error
      setUpvotedRecipes(upvotedRecipes) // Revert to original state
      setLocalRecipes(prev => prev.map(recipe =>
        recipe.id === recipeId
          ? { ...recipe, upvote_count: currentRecipe.upvote_count }
          : recipe
      ))

      // Show error message
      setErrorMessage('Failed to upvote. Please try again.')
      setTimeout(() => setErrorMessage(null), 3000) // Clear error after 3 seconds
    } finally {
      console.log('🏁 Upvote action completed, clearing loading state')
      setActionLoading(null)
    }
  }

  const handleBookmark = async (recipeId: string) => {
    if (!user) return

    setActionLoading(`bookmark-${recipeId}`)
    try {
      const isCurrentlyBookmarked = bookmarkedRecipes.includes(recipeId)
      const newBookmarkStatus = await toggleBookmark(recipeId, isCurrentlyBookmarked)

      // Update local state immediately for better UX
      if (newBookmarkStatus) {
        setBookmarkedRecipes(prev => [...prev, recipeId])
      } else {
        setBookmarkedRecipes(prev => prev.filter(id => id !== recipeId))
      }
    } catch (error) {
      console.error('Error toggling bookmark:', error)
      // Reload bookmarks on error to ensure consistency
      loadUserBookmarks()
    } finally {
      setActionLoading(null)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        {[...Array(3)].map((_, index) => (
          <RecipeCardSkeleton key={index} />
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={onRefresh}
            className="btn-secondary flex items-center mx-auto"
          >
            <HiArrowPath className="w-4 h-4 mr-2" />
            Try Again
          </button>
        </div>
      </div>
    )
  }

  if (recipes.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <h3 className="text-lg font-medium text-gray-900 mb-2">No recipes yet</h3>
          <p className="text-gray-500 mb-6">
            Be the first to share a study recipe with the community!
          </p>
          {user && (
            <p className="text-sm text-gray-400">
              Click the "Create" button in the header to get started.
            </p>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Error Message */}
      {errorMessage && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600 text-sm">{errorMessage}</p>
        </div>
      )}


      {/* Recipe Timeline Items */}
      {localRecipes.map((recipe) => (
        <RecipeTimeline
          key={recipe.id}
          recipe={recipe}
          onUpvote={handleUpvote}
          onBookmark={handleBookmark}
          isUpvoting={actionLoading === `upvote-${recipe.id}`}
          isBookmarking={actionLoading === `bookmark-${recipe.id}`}
          isBookmarked={bookmarkedRecipes.includes(recipe.id)}
          isUpvoted={upvotedRecipes.includes(recipe.id)}
          currentUser={user}
          onAuthRequired={onAuthRequired}
        />
      ))}
    </div>
  )
}

export default RecipeFeed
