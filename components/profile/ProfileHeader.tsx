import Image from 'next/image'
import { HiOutlineCog6Tooth } from 'react-icons/hi2'
import { Profile } from '../../types'
import { RankBadge } from '../gamification/RankBadge'

interface ProfileHeaderProps {
  profile: Profile
  isOwnProfile: boolean
  onEditClick?: () => void
}

export default function ProfileHeader({ profile, isOwnProfile, onEditClick }: ProfileHeaderProps) {
  const formatStudyTime = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} min`
    }
    const hours = Math.floor(minutes / 60)
    if (hours < 24) {
      return `${hours} hr`
    }
    const days = Math.floor(hours / 24)
    return `${days} day${days > 1 ? 's' : ''}`
  }

  return (
    <div className="border-b border-gray-100 px-4 py-6">
      {/* Profile Info Section */}
      <div className="flex items-start justify-between mb-6">
        {/* Avatar and Basic Info */}
        <div className="flex items-center space-x-4">
          <div className="w-20 h-20 sm:w-24 sm:h-24 rounded-full bg-white border-4 border-white shadow-lg flex items-center justify-center text-xl sm:text-2xl font-bold text-gray-600 relative overflow-hidden">
            {profile.avatar_url ? (
              <Image
                src={profile.avatar_url}
                alt={profile.username}
                fill
                className="object-cover"
                sizes="(max-width: 640px) 80px, 96px"
                priority
              />
            ) : (
              (profile.display_name?.charAt(0) || profile.username.charAt(0) || 'U').toUpperCase()
            )}
          </div>
          
          {/* Name and Username */}
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-black">
              {profile.display_name ?? profile.username}
            </h1>
            <p className="text-gray-500 text-sm sm:text-base">
              @{profile.username}
            </p>
          </div>
        </div>

        {/* Edit Button (only for own profile) */}
        {isOwnProfile && (
          <button
            onClick={onEditClick}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-full hover:bg-gray-50 transition-colors text-sm font-medium"
          >
            <HiOutlineCog6Tooth className="w-4 h-4" />
            <span className="hidden sm:inline">Edit Profile</span>
            <span className="sm:hidden">Edit</span>
          </button>
        )}
      </div>

      {/* Profile Details */}
      <div>
        {/* Bio */}
        {profile.bio && (
          <p className="text-gray-700 mb-4 text-sm sm:text-base leading-relaxed">
            {profile.bio}
          </p>
        )}

        {/* Rank Badge */}
        <div className="mb-4">
          <RankBadge level={profile.rank_level} size="lg" />
        </div>

        {/* Stats - Mobile: Stacked, Desktop: Horizontal */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-2 sm:space-y-0 text-sm">
          <div className="flex items-center space-x-1">
            <span className="text-gray-500">Total Study Time:</span>
            <span className="font-medium text-black">
              {formatStudyTime(profile.total_study_time)}
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <span className="text-gray-500">Joined:</span>
            <span className="font-medium text-black">
              {new Date(profile.created_at).toLocaleDateString('en-US', {
                month: 'short',
                year: 'numeric'
              })}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
