import { useState, useEffect } from 'react'
import { getUserWithStats } from '../../lib/api/gamification'
import { UserWithStats } from '../../types'
import { ACHIEVEMENTS, getAchievementsByCategory } from '../../lib/gamification/achievements'
import { RankBadge, LevelProgressBar } from '../gamification/RankBadge'
import { AchievementGrid, CompactAchievement } from '../gamification/AchievementBadge'
import { HiOutlineTrophy, HiOutlineFire, HiOutlineBookOpen, HiOutlineUsers, HiOutlineChartBar } from 'react-icons/hi2'

interface GamificationSectionProps {
  userId: string
  isOwnProfile: boolean
  className?: string
}

export function GamificationSection({
  userId, 
  isOwnProfile, 
  className = '' 
}: GamificationSectionProps) {
  const [userStats, setUserStats] = useState<UserWithStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'achievements' | 'progress'>('overview')

  useEffect(() => {
    loadUserStats()
  }, [userId])

  const loadUserStats = async () => {
    try {
      setLoading(true)
      const data = await getUserWithStats(userId)
      setUserStats(data)
    } catch (error) {
      console.error('Error loading user stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatStudyTime = (minutes: number) => {
    if (minutes < 60) return `${minutes} min`
    const hours = Math.floor(minutes / 60)
    if (hours < 24) return `${hours} hr`
    const days = Math.floor(hours / 24)
    return `${days} day${days > 1 ? 's' : ''}`
  }

  const earnedAchievementIds = userStats?.achievements.map(a => a.id) || []
  const totalAchievements = ACHIEVEMENTS.length
  const earnedCount = earnedAchievementIds.length
  const completionRate = Math.round((earnedCount / totalAchievements) * 100)

  if (loading) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-8 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!userStats) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
        <p className="text-gray-600 text-center">Unable to load gamification data</p>
      </div>
    )
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-black">Progress & Achievements</h2>
        <RankBadge 
          level={userStats.rank_level} 
          experiencePoints={userStats.stats.total_experience_points}
          size="lg"
        />
      </div>

      {/* Tabs */}
      <div className="flex space-x-4 mb-6 border-b border-gray-200">
        {[
          { id: 'overview', label: 'Overview', icon: HiOutlineChartBar },
          { id: 'achievements', label: 'Achievements', icon: HiOutlineTrophy },
          { id: 'progress', label: 'Progress', icon: HiOutlineFire }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center space-x-2 px-4 py-2 border-b-2 transition-colors ${
              activeTab === tab.id
                ? 'border-black text-black'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-black">
                {userStats.stats.total_experience_points.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Experience Points</div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-black">
                {userStats.stats.current_streak_days}
              </div>
              <div className="text-sm text-gray-600">Current Streak</div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-black">
                {userStats.stats.total_study_sessions}
              </div>
              <div className="text-sm text-gray-600">Study Sessions</div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-black">
                {formatStudyTime(userStats.total_study_time)}
              </div>
              <div className="text-sm text-gray-600">Total Study Time</div>
            </div>
          </div>

          {/* Level Progress */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium text-black mb-3">Level Progress</h3>
            <LevelProgressBar 
              experiencePoints={userStats.stats.total_experience_points}
              size="md"
            />
          </div>

          {/* Recent Achievements */}
          <div>
            <h3 className="font-medium text-black mb-3">Recent Achievements</h3>
            {earnedAchievementIds.length > 0 ? (
              <div className="text-sm text-gray-600">
                {userStats.achievements.length} achievements earned
              </div>
            ) : (
              <p className="text-gray-600 text-sm">
                {isOwnProfile ? 'Start studying to earn your first achievement!' : 'No achievements earned yet.'}
              </p>
            )}
          </div>
        </div>
      )}

      {activeTab === 'achievements' && (
        <div className="space-y-6">
          {/* Achievement Summary */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h3 className="font-medium text-black">Achievement Progress</h3>
              <p className="text-sm text-gray-600">
                {earnedCount} of {totalAchievements} achievements earned
              </p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-black">{completionRate}%</div>
              <div className="text-sm text-gray-600">Complete</div>
            </div>
          </div>

          {/* Achievement Categories */}
          <div className="space-y-4">
            {['study', 'community', 'consistency', 'milestone'].map((category) => {
              const categoryAchievements = getAchievementsByCategory(category as any)
              const earnedInCategory = categoryAchievements.filter(a => earnedAchievementIds.includes(a.id)).length
              
              return (
                <div key={category} className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-black capitalize">{category} Achievements</h4>
                    <span className="text-sm text-gray-600">
                      {earnedInCategory}/{categoryAchievements.length}
                    </span>
                  </div>
                  {/* <AchievementGrid
                    achievements={categoryAchievements}
                    earnedAchievements={earnedAchievementIds}
                    category={category as any}
                  /> */}
                  <div className="text-sm text-gray-600">
                    Achievement grid temporarily disabled
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      )}

      {activeTab === 'progress' && (
        <div className="space-y-6">
          {/* Detailed Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Study Progress */}
            <div className="space-y-4">
              <h3 className="font-medium text-black flex items-center space-x-2">
                <HiOutlineBookOpen className="w-5 h-5" />
                <span>Study Progress</span>
              </h3>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Sessions:</span>
                  <span className="font-medium text-black">{userStats.stats.total_study_sessions}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Time:</span>
                  <span className="font-medium text-black">{formatStudyTime(userStats.total_study_time)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Current Streak:</span>
                  <span className="font-medium text-black">{userStats.stats.current_streak_days} days</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Longest Streak:</span>
                  <span className="font-medium text-black">{userStats.stats.longest_streak_days} days</span>
                </div>
              </div>
            </div>

            {/* Community Progress */}
            <div className="space-y-4">
              <h3 className="font-medium text-black flex items-center space-x-2">
                <HiOutlineUsers className="w-5 h-5" />
                <span>Community Progress</span>
              </h3>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Recipes Created:</span>
                  <span className="font-medium text-black">{userStats.stats.recipes_created}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Upvotes Received:</span>
                  <span className="font-medium text-black">{userStats.stats.total_upvotes_received}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Comments Received:</span>
                  <span className="font-medium text-black">{userStats.stats.total_comments_received}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Comments Made:</span>
                  <span className="font-medium text-black">{userStats.stats.comments_made}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Level Progress Detail */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium text-black mb-4">Level Progress Detail</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Current Level:</span>
                <span className="font-medium text-black">
                  {userStats.level_info.currentLevel.level} - {userStats.level_info.currentLevel.name}
                </span>
              </div>
              {userStats.level_info.nextLevel && (
                <>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Next Level:</span>
                    <span className="font-medium text-black">
                      {userStats.level_info.nextLevel.level} - {userStats.level_info.nextLevel.name}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Points to Next Level:</span>
                    <span className="font-medium text-black">
                      {userStats.level_info.pointsToNext.toLocaleString()} XP
                    </span>
                  </div>
                </>
              )}
              <LevelProgressBar 
                experiencePoints={userStats.stats.total_experience_points}
                size="md"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
