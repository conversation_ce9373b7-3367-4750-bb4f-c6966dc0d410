interface ProfileTabsProps {
  activeTab: 'recipes' | 'logs' | 'progress' | 'upvoted' | 'bookmarks'
  onTabChange: (tab: 'recipes' | 'logs' | 'progress' | 'upvoted' | 'bookmarks') => void
  isOwnProfile: boolean
}

export default function ProfileTabs({ activeTab, onTabChange, isOwnProfile }: ProfileTabsProps) {
  const tabs = [
    { id: 'recipes', label: 'Recipes', shortLabel: 'Recipes', public: true },
    { id: 'logs', label: 'Study Logs', shortLabel: 'Logs', public: true },
    { id: 'progress', label: 'Progress', shortLabel: 'Progress', public: true },
    { id: 'upvoted', label: 'Upvoted', shortLabel: 'Upvoted', public: false },
    { id: 'bookmarks', label: 'Bookmarks', shortLabel: 'Bookmark', public: false },
  ] as const

  // Filter tabs based on whether it's the user's own profile
  const visibleTabs = tabs.filter(tab => tab.public || isOwnProfile)

  return (
    <div className="timeline-tabs">
      <div className="flex overflow-x-auto scrollbar-hide">
        {visibleTabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`timeline-tab flex-shrink-0 whitespace-nowrap ${activeTab === tab.id ? 'active' : ''}`}
          >
            {/* Show short label on mobile, full label on desktop */}
            <span className="sm:hidden">{tab.shortLabel}</span>
            <span className="hidden sm:inline">{tab.label}</span>
          </button>
        ))}
      </div>
    </div>
  )
}
