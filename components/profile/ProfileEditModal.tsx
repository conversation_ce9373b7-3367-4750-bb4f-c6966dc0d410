import { useEffect, useState } from 'react'
import { HiExclamationTriangle, HiXMark } from 'react-icons/hi2'
import { checkUsernameAvailability, updateProfile } from '../../lib/api/profiles'
import { Profile } from '../../types'

interface ProfileEditModalProps {
  isOpen: boolean
  onClose: () => void
  profile: Profile
  onProfileUpdate: (updatedProfile: Profile) => void
}

interface FormData {
  display_name: string
  bio: string
  username: string
  nationality: string
}

interface FormErrors {
  display_name?: string
  bio?: string
  username?: string
  nationality?: string
  general?: string
}

export default function ProfileEditModal({ 
  isOpen, 
  onClose, 
  profile, 
  onProfileUpdate 
}: ProfileEditModalProps) {
  const [formData, setFormData] = useState<FormData>({
    display_name: profile.display_name ?? '',
    bio: profile.bio ?? '',
    username: profile.username,
    nationality: profile.nationality ?? ''
  })
  
  const [errors, setErrors] = useState<FormErrors>({})
  const [isLoading, setIsLoading] = useState(false)
  const [isCheckingUsername, setIsCheckingUsername] = useState(false)

  // Reset form when modal opens/closes or profile changes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        display_name: profile.display_name ?? '',
        bio: profile.bio ?? '',
        username: profile.username,
        nationality: profile.nationality ?? ''
      })
      setErrors({})
    }
  }, [isOpen, profile])

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // Display name validation
    if (formData.display_name.trim().length === 0) {
      newErrors.display_name = 'Display name is required'
    } else if (formData.display_name.trim().length > 50) {
      newErrors.display_name = 'Display name must be 50 characters or less'
    }

    // Bio validation
    if (formData.bio.length > 500) {
      newErrors.bio = 'Bio must be 500 characters or less'
    }

    // Username validation
    if (formData.username.trim().length === 0) {
      newErrors.username = 'Username is required'
    } else if (formData.username.trim().length < 3) {
      newErrors.username = 'Username must be at least 3 characters'
    } else if (formData.username.trim().length > 30) {
      newErrors.username = 'Username must be 30 characters or less'
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username.trim())) {
      newErrors.username = 'Username can only contain letters, numbers, and underscores'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const checkUsername = async (username: string) => {
    if (username === profile.username) return true

    setIsCheckingUsername(true)
    try {
      const isAvailable = await checkUsernameAvailability(username, profile.id)
      if (!isAvailable) {
        setErrors(prev => ({ ...prev, username: 'Username is already taken' }))
        return false
      }
      setErrors(prev => ({ ...prev, username: undefined }))
      return true
    } catch (error) {
      setErrors(prev => ({ ...prev, username: 'Error checking username availability' }))
      return false
    } finally {
      setIsCheckingUsername(false)
    }
  }

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear field-specific error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const handleUsernameBlur = async () => {
    if (formData.username.trim() && formData.username !== profile.username) {
      await checkUsername(formData.username.trim())
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    // Check username availability if changed
    if (formData.username !== profile.username) {
      const isUsernameValid = await checkUsername(formData.username.trim())
      if (!isUsernameValid) return
    }

    setIsLoading(true)
    setErrors({})

    try {
      const updatedProfile = await updateProfile({
        display_name: formData.display_name.trim(),
        bio: formData.bio.trim() || undefined,
        username: formData.username.trim(),
        nationality: formData.nationality.trim() || undefined
      })

      onProfileUpdate(updatedProfile)
      onClose()
    } catch (error) {
      console.error('Error updating profile:', error)
      setErrors({ general: 'Failed to update profile. Please try again.' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    setFormData({
      display_name: profile.display_name ?? '',
      bio: profile.bio ?? '',
      username: profile.username,
      nationality: profile.nationality ?? ''
    })
    setErrors({})
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Edit Profile</h2>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <HiXMark className="w-6 h-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* General Error */}
          {errors.general && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3">
              <HiExclamationTriangle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
              <p className="text-red-700 text-sm">{errors.general}</p>
            </div>
          )}

          {/* Display Name */}
          <div>
            <label htmlFor="display_name" className="block text-sm font-medium text-gray-700 mb-2">
              Display Name *
            </label>
            <input
              type="text"
              id="display_name"
              value={formData.display_name}
              onChange={(e) => handleInputChange('display_name', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent ${
                errors.display_name ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Your display name"
              maxLength={50}
            />
            {errors.display_name && (
              <p className="text-red-600 text-sm mt-1">{errors.display_name}</p>
            )}
          </div>

          {/* Username */}
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
              Username *
            </label>
            <input
              type="text"
              id="username"
              value={formData.username}
              onChange={(e) => handleInputChange('username', e.target.value.toLowerCase())}
              onBlur={handleUsernameBlur}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent ${
                errors.username ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="your_username"
              maxLength={30}
            />
            {isCheckingUsername && (
              <p className="text-gray-500 text-sm mt-1">Checking availability...</p>
            )}
            {errors.username && (
              <p className="text-red-600 text-sm mt-1">{errors.username}</p>
            )}
            <p className="text-gray-500 text-xs mt-1">
              Letters, numbers, and underscores only. 3-30 characters.
            </p>
          </div>

          {/* Bio */}
          <div>
            <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-2">
              Bio
            </label>
            <textarea
              id="bio"
              value={formData.bio}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              rows={4}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none ${
                errors.bio ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Tell us about yourself..."
              maxLength={500}
            />
            {errors.bio && (
              <p className="text-red-600 text-sm mt-1">{errors.bio}</p>
            )}
            <p className="text-gray-500 text-xs mt-1">
              {formData.bio.length}/500 characters
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || isCheckingUsername}
              className="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
