import { useEffect, useState } from 'react'
import { Hi<PERSON>rrowPath, HiOutlineBookmark } from 'react-icons/hi2'
import { getUserBookmarks, toggleBookmark } from '../../lib/api/bookmarks'
import { getUserUpvotes, RecipeWithMaterials, upvoteRecipe } from '../../lib/api/recipes'
import { useAuth } from '../../lib/auth'
import RecipeCardSkeleton from '../feed/RecipeCardSkeleton'
import RecipeTimeline from '../feed/RecipeTimeline'

interface UserBookmarksProps {
  recipes: RecipeWithMaterials[]
  loading: boolean
  error: string | null
  onRefresh: () => void
}

export default function UserBookmarks({ 
  recipes, 
  loading, 
  error, 
  onRefresh 
}: UserBookmarksProps) {
  const { user } = useAuth()
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const [bookmarkedRecipes, setBookmarkedRecipes] = useState<string[]>([])
  const [upvotedRecipes, setUpvotedRecipes] = useState<string[]>([])
  const [localRecipes, setLocalRecipes] = useState<RecipeWithMaterials[]>(recipes)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)

  // Update local recipes when props change
  useEffect(() => {
    setLocalRecipes(recipes)
  }, [recipes])

  // Load user's bookmarks and upvotes when component mounts or user changes
  useEffect(() => {
    if (user) {
      loadUserBookmarks()
      loadUserUpvotes()
    } else {
      setBookmarkedRecipes([])
      setUpvotedRecipes([])
    }
  }, [user])

  const loadUserBookmarks = async () => {
    try {
      const bookmarks = await getUserBookmarks()
      setBookmarkedRecipes(bookmarks)
    } catch (error) {
      console.error('Error loading bookmarks:', error)
    }
  }

  const loadUserUpvotes = async () => {
    try {
      const upvotes = await getUserUpvotes()
      setUpvotedRecipes(upvotes)
    } catch (error) {
      console.error('Error loading upvotes:', error)
    }
  }

  const handleUpvote = async (recipeId: string) => {
    if (!user) return

    setActionLoading(`upvote-${recipeId}`)
    const isCurrentlyUpvoted = upvotedRecipes.includes(recipeId)
    const currentRecipe = localRecipes.find(r => r.id === recipeId)

    if (!currentRecipe) {
      setActionLoading(null)
      return
    }

    // Optimistic updates
    const newUpvotedState = isCurrentlyUpvoted
      ? upvotedRecipes.filter(id => id !== recipeId)
      : [...upvotedRecipes, recipeId]

    const newUpvoteCount = isCurrentlyUpvoted
      ? Math.max(0, (currentRecipe.upvote_count || 0) - 1)
      : (currentRecipe.upvote_count || 0) + 1

    setUpvotedRecipes(newUpvotedState)
    setLocalRecipes(prev => prev.map(recipe =>
      recipe.id === recipeId
        ? { ...recipe, upvote_count: newUpvoteCount }
        : recipe
    ))

    try {
      const result = await upvoteRecipe(recipeId)
      
      if (result.voted !== !isCurrentlyUpvoted) {
        setUpvotedRecipes(prev => result.voted
          ? [...prev.filter(id => id !== recipeId), recipeId]
          : prev.filter(id => id !== recipeId)
        )
      }

      if (result.newCount !== undefined) {
        setLocalRecipes(prev => prev.map(recipe =>
          recipe.id === recipeId
            ? { ...recipe, upvote_count: result.newCount }
            : recipe
        ))
      }
    } catch (error) {
      console.error('Error upvoting recipe:', error)
      // Revert optimistic updates
      setUpvotedRecipes(upvotedRecipes)
      setLocalRecipes(prev => prev.map(recipe =>
        recipe.id === recipeId
          ? { ...recipe, upvote_count: currentRecipe.upvote_count }
          : recipe
      ))
      setErrorMessage('Failed to upvote. Please try again.')
      setTimeout(() => setErrorMessage(null), 3000)
    } finally {
      setActionLoading(null)
    }
  }

  const handleBookmark = async (recipeId: string) => {
    if (!user) return

    setActionLoading(`bookmark-${recipeId}`)
    try {
      const isCurrentlyBookmarked = bookmarkedRecipes.includes(recipeId)
      const newBookmarkStatus = await toggleBookmark(recipeId, isCurrentlyBookmarked)

      if (newBookmarkStatus) {
        setBookmarkedRecipes(prev => [...prev, recipeId])
      } else {
        setBookmarkedRecipes(prev => prev.filter(id => id !== recipeId))
        // Remove from local list if unbookmarked
        setLocalRecipes(prev => prev.filter(recipe => recipe.id !== recipeId))
      }
    } catch (error) {
      console.error('Error toggling bookmark:', error)
      loadUserBookmarks()
    } finally {
      setActionLoading(null)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        {[...Array(3)].map((_, index) => (
          <RecipeCardSkeleton key={index} />
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={onRefresh}
            className="btn-secondary flex items-center mx-auto"
          >
            <HiArrowPath className="w-4 h-4 mr-2" />
            Try Again
          </button>
        </div>
      </div>
    )
  }

  if (recipes.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <HiOutlineBookmark className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No bookmarked recipes yet</h3>
        <p className="text-gray-600 mb-6">
          Save recipes you want to reference later by bookmarking them.
        </p>
        <p className="text-sm text-gray-400">
          Browse the community feed and click the bookmark button on recipes you like!
        </p>
      </div>
    )
  }

  return (
    <div>
      {/* Error Message */}
      {errorMessage && (
        <div className="border-b border-gray-100 px-4 py-3">
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-red-600 text-sm">{errorMessage}</p>
          </div>
        </div>
      )}

      {/* Recipe Count Header */}
      <div className="border-b border-gray-100 px-4 py-3">
        <p className="text-sm text-gray-600">
          {recipes.length} bookmarked recipe{recipes.length !== 1 ? 's' : ''}
        </p>
      </div>

      {/* Timeline Items */}
      <div>
        {localRecipes.map((recipe) => (
          <RecipeTimeline
            key={recipe.id}
            recipe={recipe}
            onUpvote={handleUpvote}
            onBookmark={handleBookmark}
            isUpvoting={actionLoading === `upvote-${recipe.id}`}
            isBookmarking={actionLoading === `bookmark-${recipe.id}`}
            isBookmarked={bookmarkedRecipes.includes(recipe.id)}
            isUpvoted={upvotedRecipes.includes(recipe.id)}
            currentUser={user}
          />
        ))}
      </div>

      {/* Load More Button (for future pagination) */}
      {recipes.length >= 20 && (
        <div className="border-t border-gray-100 px-4 py-6 text-center">
          <button
            onClick={onRefresh}
            className="btn-secondary"
          >
            Load More
          </button>
        </div>
      )}
    </div>
  )
}
