import { useEffect, useState } from 'react'
import { getUserWithStats } from '../../lib/api/gamification'
import { fetchUserStudyLogs, StudyLogWithRecipe } from '../../lib/api/studyLogs'
import { UserWithStats } from '../../types'

interface SimpleProgressSectionProps {
  userId: string
  isOwnProfile: boolean
  className?: string
}

interface DayStudyData {
  date: string
  dateString: string
  isInRange: boolean
  hasStudied: boolean
  sessions: StudyLogWithRecipe[]
  totalMinutes: number
}

interface StudyDayDetail {
  date: string
  sessions: StudyLogWithRecipe[]
  totalMinutes: number
  sessionCount: number
}

export function SimpleProgressSection({
  userId, 
  isOwnProfile, 
  className = '' 
}: SimpleProgressSectionProps) {
  const [userStats, setUserStats] = useState<UserWithStats | null>(null)
  const [studyLogs, setStudyLogs] = useState<StudyLogWithRecipe[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedDay, setSelectedDay] = useState<StudyDayDetail | null>(null)
  const [showModal, setShowModal] = useState(false)

  useEffect(() => {
    loadData()
  }, [userId])

  const loadData = async () => {
    try {
      setLoading(true)
      const [statsData, logsData] = await Promise.all([
        getUserWithStats(userId),
        fetchUserStudyLogs(userId, 100) // Get more logs to cover 30 days
      ])
      setUserStats(statsData)
      setStudyLogs(logsData)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatStudyTime = (minutes: number) => {
    if (minutes < 60) return `${minutes} min`
    const hours = Math.floor(minutes / 60)
    if (hours < 24) return `${hours} hr`
    const days = Math.floor(hours / 24)
    return `${days} day${days > 1 ? 's' : ''}`
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    })
  }

  const getProgressPercentage = (experiencePoints: number) => {
    const currentLevel = userStats?.level_info?.currentLevel?.level || 1
    const nextLevelXP = currentLevel * 100 // Simple calculation
    const currentLevelXP = (currentLevel - 1) * 100
    const progressInLevel = experiencePoints - currentLevelXP
    const levelRange = nextLevelXP - currentLevelXP
    return Math.min(Math.max((progressInLevel / levelRange) * 100, 0), 100)
  }

  const getXPToNextLevel = (experiencePoints: number) => {
    const currentLevel = userStats?.level_info?.currentLevel?.level || 1
    const nextLevelXP = currentLevel * 100
    return Math.max(nextLevelXP - experiencePoints, 0)
  }

  // Generate calendar grid with actual study data
  const generateCalendarGrid = (): DayStudyData[][] => {
    const today = new Date()
    const startDate = new Date(today)
    startDate.setDate(today.getDate() - 29) // 30 days total
    
    // Find the start of the week (Sunday)
    const startOfWeek = new Date(startDate)
    const dayOfWeek = startDate.getDay()
    startOfWeek.setDate(startDate.getDate() - dayOfWeek)
    
    // Group study logs by date
    const logsByDate: { [key: string]: StudyLogWithRecipe[] } = {}
    studyLogs.forEach(log => {
      const logDate = new Date(log.completed_at).toISOString().split('T')[0]
      if (!logsByDate[logDate]) {
        logsByDate[logDate] = []
      }
      logsByDate[logDate].push(log)
    })
    
    const grid: DayStudyData[][] = []
    const currentDate = new Date(startOfWeek)
    
    // Generate 6 weeks (42 days) to ensure proper grid
    for (let week = 0; week < 6; week++) {
      const weekDays: DayStudyData[] = []
      for (let day = 0; day < 7; day++) {
        const dateString = currentDate.toISOString().split('T')[0]
        const isInRange = currentDate >= startDate && currentDate <= today
        const sessions = logsByDate[dateString] || []
        const hasStudied = sessions.length > 0
        const totalMinutes = sessions.reduce((sum, session) => sum + session.duration_minutes, 0)
        
        weekDays.push({
          date: currentDate.toISOString(),
          dateString,
          isInRange,
          hasStudied,
          sessions,
          totalMinutes
        })
        
        currentDate.setDate(currentDate.getDate() + 1)
      }
      grid.push(weekDays)
    }
    
    return grid
  }

  const handleDayClick = (dayData: DayStudyData) => {
    if (!dayData.isInRange || !dayData.hasStudied) return
    
    setSelectedDay({
      date: dayData.dateString,
      sessions: dayData.sessions,
      totalMinutes: dayData.totalMinutes,
      sessionCount: dayData.sessions.length
    })
    setShowModal(true)
  }

  const closeModal = () => {
    setShowModal(false)
    setSelectedDay(null)
  }

  if (loading) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="h-20 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (!userStats) {
    return (
      <div className={`p-6 ${className}`}>
        <p className="text-gray-600 text-center">Unable to load study data</p>
      </div>
    )
  }

  const progressPercentage = getProgressPercentage(userStats.stats.total_experience_points)
  const xpToNext = getXPToNextLevel(userStats.stats.total_experience_points)
  const currentLevel = userStats.level_info?.currentLevel?.level || 1
  const levelName = userStats.level_info?.currentLevel?.name || 'Beginner'
  const calendarGrid = generateCalendarGrid()

  return (
    <>
      <div className={`p-6 space-y-6 ${className}`}>
        {/* Header */}
        <div className="flex items-center space-x-2">
          <span className="text-xl">📊</span>
          <h2 className="text-lg font-bold text-black">Study Progress</h2>
        </div>

        {/* Level Progress */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <span className="text-lg">🎯</span>
            <span className="font-medium text-black">{levelName} Level {currentLevel}</span>
          </div>
          
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className="bg-blue-500 h-3 rounded-full transition-all duration-300"
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
            <div className="flex justify-between text-sm text-gray-600">
              <span>{Math.round(progressPercentage)}% → Level {currentLevel + 1}</span>
            </div>
          </div>
          
          {xpToNext > 0 && (
            <p className="text-sm text-blue-600 font-medium">
              {xpToNext} XP to level up!
            </p>
          )}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center space-x-2">
            <span className="text-lg">⏱️</span>
            <div>
              <div className="text-sm text-gray-600">Total Study Time</div>
              <div className="font-medium text-black">{formatStudyTime(userStats.total_study_time)}</div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-lg">🔥</span>
            <div>
              <div className="text-sm text-gray-600">Current Streak</div>
              <div className="font-medium text-black">{userStats.stats.current_streak_days} days</div>
            </div>
          </div>
        </div>

        {/* Study History Calendar */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <span className="text-lg">📅</span>
            <span className="font-medium text-black">Study History (Past 30 Days)</span>
          </div>
          
          <div className="space-y-2">
            {/* Week labels - aligned with calendar grid */}
            <div className="grid grid-cols-7 gap-1 text-xs text-gray-500 text-center">
              <div className="w-6">Sun</div>
              <div className="w-6">Mon</div>
              <div className="w-6">Tue</div>
              <div className="w-6">Wed</div>
              <div className="w-6">Thu</div>
              <div className="w-6">Fri</div>
              <div className="w-6">Sat</div>
            </div>
            
            {/* Calendar grid */}
            <div className="space-y-1">
              {calendarGrid.map((week, weekIndex) => (
                <div key={weekIndex} className="grid grid-cols-7 gap-1">
                  {week.map((day, dayIndex) => (
                    <div
                      key={dayIndex}
                      className={`w-6 h-6 rounded-sm border cursor-pointer transition-all duration-200 ${
                        !day.isInRange 
                          ? 'bg-transparent border-transparent'
                          : day.hasStudied 
                            ? 'bg-green-500 border-green-600 hover:bg-green-600' 
                            : 'bg-gray-100 border-gray-200 hover:bg-gray-200'
                      }`}
                      title={day.isInRange ? `${day.dateString}: ${day.hasStudied ? `${day.sessions.length} session(s), ${day.totalMinutes} min` : 'No study'}` : ''}
                      onClick={() => handleDayClick(day)}
                    ></div>
                  ))}
                </div>
              ))}
            </div>
            
            <div className="flex items-center space-x-4 text-xs text-gray-600">
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-gray-100 border border-gray-200 rounded-sm"></div>
                <span>No study</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-green-500 border border-green-600 rounded-sm"></div>
                <span>Studied</span>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Achievements */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <span className="text-lg">🏆</span>
            <span className="font-medium text-black">Recent Achievements</span>
          </div>
          
          <div className="space-y-2">
            {userStats.stats.current_streak_days >= 3 && (
              <div className="flex items-center space-x-2 text-sm">
                <span className="text-green-600">•</span>
                <span className="text-gray-700">{userStats.stats.current_streak_days} day study streak achieved!</span>
              </div>
            )}
            
            {userStats.stats.recipes_created > 0 && (
              <div className="flex items-center space-x-2 text-sm">
                <span className="text-blue-600">•</span>
                <span className="text-gray-700">First post completed</span>
              </div>
            )}
            
            {userStats.stats.current_streak_days === 0 && userStats.stats.recipes_created === 0 && (
              <p className="text-sm text-gray-500">
                {isOwnProfile ? 'Start studying to earn your first achievement!' : 'No achievements yet.'}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Study Day Detail Modal */}
      {showModal && selectedDay && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full max-h-96 overflow-y-auto">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-bold text-black">
                  Study Details - {formatDate(selectedDay.date)}
                </h3>
                <button
                  onClick={closeModal}
                  className="text-gray-500 hover:text-gray-700 text-xl"
                >
                  ×
                </button>
              </div>
            </div>
            
            <div className="p-4 space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Sessions:</span>
                  <div className="font-medium">{selectedDay.sessionCount}</div>
                </div>
                <div>
                  <span className="text-gray-600">Total Time:</span>
                  <div className="font-medium">{formatStudyTime(selectedDay.totalMinutes)}</div>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-black mb-2">Study Sessions:</h4>
                <div className="space-y-2">
                  {selectedDay.sessions.map((session, index) => (
                    <div key={session.id} className="p-3 bg-gray-50 rounded-lg">
                      <div className="font-medium text-sm text-black">{session.title}</div>
                      <div className="text-xs text-gray-600 mt-1">
                        {session.recipe?.title} • {session.duration_minutes} min
                      </div>
                      {session.notes && (
                        <div className="text-xs text-gray-500 mt-1">{session.notes}</div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
