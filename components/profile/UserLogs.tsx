import { Hi<PERSON><PERSON><PERSON><PERSON>, HiOutlineBookOpen } from 'react-icons/hi2'
import { StudyLogWithRecipe } from '../../lib/api/studyLogs'
import { useAuth } from '../../lib/auth'
import StudyLogCardSkeleton from '../feed/StudyLogCardSkeleton'
import StudyLogTimeline from '../feed/StudyLogTimeline'

interface UserLogsProps {
  logs: StudyLogWithRecipe[]
  loading: boolean
  error: string | null
  onRefresh: () => void
  isOwnProfile: boolean
  username: string
}

export default function UserLogs({ 
  logs, 
  loading, 
  error, 
  onRefresh, 
  isOwnProfile, 
  username 
}: UserLogsProps) {
  const { user } = useAuth()

  // Calculate study statistics
  const totalMinutes = logs.reduce((sum, log) => sum + log.duration_minutes, 0)
  const totalSessions = logs.length
  const averageSession = totalSessions > 0 ? Math.round(totalMinutes / totalSessions) : 0

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} min`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    if (remainingMinutes === 0) {
      return `${hours} hr`
    }
    return `${hours}h ${remainingMinutes}m`
  }

  if (loading) {
    return (
      <div className="space-y-6">
        {[...Array(3)].map((_, index) => (
          <StudyLogCardSkeleton key={index} />
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <p className="text-lg font-medium">Error loading study logs</p>
          <p className="text-sm">{error}</p>
        </div>
        <button
          onClick={onRefresh}
          className="btn-secondary flex items-center mx-auto"
        >
          <HiArrowPath className="w-4 h-4 mr-2" />
          Try Again
        </button>
      </div>
    )
  }

  if (!logs || logs.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <HiOutlineBookOpen className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {isOwnProfile ? 'No study logs yet' : `${username} hasn't logged any study sessions yet`}
        </h3>
        <p className="text-gray-600 mb-6">
          {isOwnProfile 
            ? "Start your first study session to see logs here!" 
            : "Check back later for study activity."
          }
        </p>
        {isOwnProfile && (
          <p className="text-sm text-gray-400">
            Click the "Create" button in the header to log a study session.
          </p>
        )}
      </div>
    )
  }

  return (
    <div>
      {/* Study Statistics */}
      <div className="border-b border-gray-100 px-4 py-4">
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-3">
            {isOwnProfile ? 'Your Study Stats' : `${username}'s Study Stats`}
          </h4>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-xl sm:text-2xl font-bold text-black">{totalSessions}</div>
              <div className="text-xs sm:text-sm text-gray-600">Sessions</div>
            </div>
            <div>
              <div className="text-xl sm:text-2xl font-bold text-black">{formatDuration(totalMinutes)}</div>
              <div className="text-xs sm:text-sm text-gray-600">Total Time</div>
            </div>
            <div>
              <div className="text-xl sm:text-2xl font-bold text-black">{formatDuration(averageSession)}</div>
              <div className="text-xs sm:text-sm text-gray-600">Avg Session</div>
            </div>
          </div>
        </div>
      </div>

      {/* Session Count Header */}
      <div className="border-b border-gray-100 px-4 py-3">
        <p className="text-sm text-gray-600">
          {logs.length} study session{logs.length !== 1 ? 's' : ''}
        </p>
      </div>

      {/* Timeline Items */}
      <div>
        {logs.map((log) => (
          <StudyLogTimeline
            key={log.id}
            log={log}
            currentUser={user}
          />
        ))}
      </div>
      
      {/* Load More Button (for future pagination) */}
      {logs.length >= 20 && (
        <div className="border-t border-gray-100 px-4 py-6 text-center">
          <button
            onClick={onRefresh}
            className="btn-secondary"
          >
            Load More
          </button>
        </div>
      )}
    </div>
  )
}
