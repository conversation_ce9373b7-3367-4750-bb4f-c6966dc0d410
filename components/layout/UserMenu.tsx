import { useRouter } from 'next/router'
import { useEffect, useRef, useState } from 'react'
import { HiChevronDown, HiOutlineArrowRightOnRectangle, HiOutlineCog6Tooth, HiOutlineUser } from 'react-icons/hi2'
import { useAuth } from '../../lib/auth'

export default function UserMenu() {
  const [isOpen, setIsOpen] = useState(false)
  const { profile, signOut } = useAuth()
  const router = useRouter()
  const menuRef = useRef<HTMLDivElement>(null)

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleSignOut = async () => {
    try {
      await signOut()
      setIsOpen(false)
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  if (!profile) return null

  return (
    <div className="relative" ref={menuRef} data-testid="user-menu">
      {/* User Menu Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2"
        aria-expanded={isOpen}
        aria-haspopup="true"
        aria-label="User menu"
        data-testid="user-menu-button"
      >
        {/* Avatar */}
        <div className="w-8 h-8 bg-black text-white rounded-full flex items-center justify-center text-sm font-medium">
          {profile.avatar_url ? (
            <img
              src={profile.avatar_url}
              alt={profile.username}
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            (profile.display_name?.charAt(0) || profile.username.charAt(0) || 'U').toUpperCase()
          )}
        </div>

        {/* User Info (hidden on mobile) */}
        <div className="hidden md:block text-left">
          <p className="font-medium text-sm text-black">{profile.display_name ?? profile.username}</p>
          <p className="text-xs text-gray-500">@{profile.username}</p>
        </div>

        {/* Chevron */}
        <HiChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          className="absolute right-0 md:right-auto md:left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-[9999]"
          role="menu"
          aria-orientation="vertical"
        >
          {/* User Info */}
          <div className="px-4 py-3 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              {/* Avatar */}
              <div className="w-10 h-10 bg-black text-white rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">
                {profile.avatar_url ? (
                  <img
                    src={profile.avatar_url}
                    alt={profile.username}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  (profile.display_name?.charAt(0) || profile.username.charAt(0) || 'U').toUpperCase()
                )}
              </div>

              {/* User Details */}
              <div className="flex-1 min-w-0">
                <p className="font-medium text-sm text-black truncate">
                  {profile.display_name ?? profile.username}
                </p>
                <p className="text-xs text-gray-500 truncate">@{profile.username}</p>
                {profile.bio && (
                  <p className="text-xs text-gray-600 mt-1 line-clamp-2">{profile.bio}</p>
                )}
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="py-1">
            <button
              onClick={() => {
                setIsOpen(false)
                if (profile?.username) {
                  router.push(`/profile/${profile.username}`)
                }
              }}
              className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors focus:outline-none focus:bg-gray-100"
              role="menuitem"
              data-testid="profile-link"
            >
              <HiOutlineUser className="w-4 h-4 mr-3 text-gray-500" />
              View Profile
            </button>

            <button
              onClick={() => {
                setIsOpen(false)
                // TODO: Navigate to settings page
                console.log('Navigate to settings')
              }}
              className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors focus:outline-none focus:bg-gray-100"
              role="menuitem"
            >
              <HiOutlineCog6Tooth className="w-4 h-4 mr-3 text-gray-500" />
              Settings
            </button>
          </div>

          {/* Sign Out */}
          <div className="border-t border-gray-100 py-1">
            <button
              onClick={handleSignOut}
              className="w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors focus:outline-none focus:bg-red-50"
              role="menuitem"
              data-testid="sign-out-button"
            >
              <HiOutlineArrowRightOnRectangle className="w-4 h-4 mr-3" />
              Sign Out
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
