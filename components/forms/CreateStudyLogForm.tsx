import { useEffect, useState } from 'react'
import { HiXMark } from 'react-icons/hi2'
import { getUserBookmarks } from '../../lib/api/bookmarks'
import { RecipeWithMaterials } from '../../lib/api/recipes'
import { createStudyLog } from '../../lib/api/studyLogs'
import { useAuth } from '../../lib/auth'
import { calculateStudyExperience } from '../../lib/gamification/rankingSystem'
import { supabase } from '../../lib/supabase'
import { ActionFeedback, ExperienceGainAnimation } from '../gamification/NotificationSystem'
import { useGamificationActions } from '../providers/GamificationProvider'
import TimerStopwatch from '../timer/TimerStopwatch'

interface CreateStudyLogFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

export default function CreateStudyLogForm({ isOpen, onClose, onSuccess }: CreateStudyLogFormProps) {
  const { user } = useAuth()
  const { triggerExperienceGain } = useGamificationActions()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Gamification state
  const [showExperienceGain, setShowExperienceGain] = useState(false)
  const [experienceGained, setExperienceGained] = useState(0)
  const [showSuccessFeedback, setShowSuccessFeedback] = useState(false)

  // Form state
  const [selectedRecipeId, setSelectedRecipeId] = useState('')
  const [selectedMaterialId, setSelectedMaterialId] = useState('')
  const [title, setTitle] = useState('')
  const [notes, setNotes] = useState('')
  const [durationMinutes, setDurationMinutes] = useState(1)

  // Recipe options
  const [availableRecipes, setAvailableRecipes] = useState<(RecipeWithMaterials & { isOwnRecipe: boolean })[]>([])
  const [loadingRecipes, setLoadingRecipes] = useState(false)

  // Load user's recipes (bookmarked recipes feature will be added later)
  useEffect(() => {
    if (isOpen && user) {
      loadAvailableRecipes()
    }
  }, [isOpen, user])


  const loadAvailableRecipes = async () => {
    setLoadingRecipes(true)
    try {
      // Get user's own recipes
      const { data: userRecipes, error: userRecipesError } = await supabase
        .from('study_recipes')
        .select('*')
        .eq('user_id', user!.id)

      if (userRecipesError) throw userRecipesError

      // Get bookmarked recipe IDs
      let bookmarkedRecipeIds: string[] = []
      try {
        bookmarkedRecipeIds = await getUserBookmarks()
      } catch (bookmarkError) {
        console.warn('Failed to load bookmarks, continuing with user recipes only:', bookmarkError)
      }

      // Get bookmarked recipes if any exist
      let bookmarkedRecipes: any[] = []
      if (bookmarkedRecipeIds.length > 0) {
        const { data: recipes, error: bookmarkedRecipesError } = await supabase
          .from('study_recipes')
          .select('*')
          .in('id', bookmarkedRecipeIds)

        if (bookmarkedRecipesError) {
          console.warn('Failed to load bookmarked recipes:', bookmarkedRecipesError)
        } else {
          bookmarkedRecipes = recipes || []
        }
      }

      // Combine all recipes and remove duplicates
      const allRecipes = [...(userRecipes || []), ...bookmarkedRecipes]
      const uniqueRecipes = allRecipes.filter((recipe, index, self) =>
        index === self.findIndex(r => r.id === recipe.id)
      )

      if (uniqueRecipes.length === 0) {
        setAvailableRecipes([])
        return
      }

      // Get user profiles for all recipe authors
      const allUserIds = [...new Set(uniqueRecipes.map(r => r.user_id))]
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('id, username, display_name')
        .in('id', allUserIds)

      if (profilesError) throw profilesError

      // Get materials for all recipes
      const recipeIds = uniqueRecipes.map(r => r.id)
      const { data: materials, error: materialsError } = await supabase
        .from('study_materials')
        .select('*')
        .in('recipe_id', recipeIds)
        .order('order_index')

      if (materialsError) throw materialsError

      // Combine recipes with author info and materials
      const recipesWithDetails = uniqueRecipes.map(recipe => {
        const profile = profiles?.find(p => p.id === recipe.user_id)
        const recipeMaterials = materials?.filter(m => m.recipe_id === recipe.id) || []
        const isOwnRecipe = recipe.user_id === user!.id

        return {
          ...recipe,
          author: {
            username: profile?.username || 'Unknown',
            display_name: profile?.display_name || null
          },
          materials: recipeMaterials.sort((a, b) => a.order_index - b.order_index),
          isOwnRecipe // Add flag to identify own recipes
        }
      })

      // Sort recipes: own recipes first, then bookmarked recipes
      const sortedRecipes = recipesWithDetails.sort((a, b) => {
        if (a.isOwnRecipe && !b.isOwnRecipe) return -1
        if (!a.isOwnRecipe && b.isOwnRecipe) return 1
        return 0
      })

      setAvailableRecipes(sortedRecipes)
    } catch (error: any) {
      console.error('Error loading recipes:', error)
      setError('Failed to load available recipes')
    } finally {
      setLoadingRecipes(false)
    }
  }

  // Handle timer duration change from TimerStopwatch component
  const handleDurationChange = (minutes: number) => {
    setDurationMinutes(minutes)
  }

  // Generate title based on recipe selection (without material name)
  const generateTitle = (recipeId: string) => {
    const selectedRecipe = availableRecipes.find(recipe => recipe.id === recipeId)
    if (!selectedRecipe) return ''

    if (selectedRecipe.isOwnRecipe) {
      return selectedRecipe.title
    } else {
      return `${selectedRecipe.title} (@${selectedRecipe.author.username})`
    }
  }

  // Handle recipe selection change
  const handleRecipeChange = (recipeId: string) => {
    setSelectedRecipeId(recipeId)
    setSelectedMaterialId('') // Reset material selection when recipe changes
    if (recipeId) {
      const generatedTitle = generateTitle(recipeId)
      setTitle(generatedTitle)
    } else {
      setTitle('')
    }
  }

  // Handle material selection change
  const handleMaterialChange = (materialId: string) => {
    setSelectedMaterialId(materialId)
    // Title doesn't change when material is selected
  }

  const resetForm = () => {
    setSelectedRecipeId('')
    setSelectedMaterialId('')
    setTitle('')
    setNotes('')
    setDurationMinutes(1)
    setError(null)
    setShowExperienceGain(false)
    setExperienceGained(0)
    setShowSuccessFeedback(false)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user) {
      setError('You must be logged in to create a study log')
      return
    }

    if (!selectedRecipeId) {
      setError('Please select a study recipe')
      return
    }

    // Check if selected recipe has materials and if material is selected
    const selectedRecipe = availableRecipes.find(recipe => recipe.id === selectedRecipeId)
    if (selectedRecipe && selectedRecipe.materials.length > 0 && !selectedMaterialId) {
      setError('Please select a study material')
      return
    }

    if (!title.trim()) {
      setError('Title is required')
      return
    }

    if (durationMinutes <= 0) {
      setError('Please set a valid study duration')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const logData = {
        recipe_id: selectedRecipeId,
        material_id: selectedMaterialId || null,
        title: title.trim(),
        duration_minutes: Math.max(1, durationMinutes), // Ensure minimum 1 minute
        duration_seconds: Math.max(1, durationMinutes) * 60, // Convert minutes to seconds
        notes: notes.trim() || undefined,
        timer_type: 'stopwatch' as const,
        // completed_at will be set automatically by the database default (NOW())
      }

      console.log('📝 Submitting study log data:', logData)

      const result = await createStudyLog(logData)
      
      if (!result) {
        throw new Error('Failed to create study log')
      }

      // Calculate and show experience gained
      const xpGained = calculateStudyExperience(Math.max(1, durationMinutes))
      setExperienceGained(xpGained)
      setShowExperienceGain(true)

      // Trigger gamification updates (temporarily disabled to avoid RLS issues)
      try {
        triggerExperienceGain(xpGained)
      } catch (gamificationError) {
        console.warn('Gamification update failed:', gamificationError)
        // Continue without gamification updates
      }

      // Show success feedback
      setShowSuccessFeedback(true)

      // Success
      resetForm()
      onSuccess?.()

      // Close after showing experience gain animation
      setTimeout(() => {
        onClose()
      }, 2500)
    } catch (error: any) {
      console.error('Error creating study log:', error)
      setError(error.message || 'Failed to create study log')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
          <h2 className="text-xl font-bold">Create Study Log</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <HiXMark className="w-6 h-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Error Message */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          {/* Recipe Selection */}
          <div>
            <label htmlFor="recipe" className="block text-sm font-medium text-gray-700 mb-1">
              Study Recipe *
            </label>
            {loadingRecipes ? (
              <div className="text-sm text-gray-500">Loading recipes...</div>
            ) : (
              <select
                id="recipe"
                value={selectedRecipeId}
                onChange={(e) => handleRecipeChange(e.target.value)}
                className="input"
                required
              >
                <option value="">Select a recipe</option>
                {availableRecipes.map((recipe) => (
                  <option key={recipe.id} value={recipe.id}>
                    {recipe.isOwnRecipe ? '📝 ' : '🔖 '}{recipe.title}
                    {!recipe.isOwnRecipe && ` (by @${recipe.author.username})`}
                  </option>
                ))}
              </select>
            )}
            <p className="text-xs text-gray-500 mt-1">Choose from your recipes or bookmarked recipes</p>
          </div>

          {/* Material Selection */}
          {selectedRecipeId && (
            <div>
              <label htmlFor="material" className="block text-sm font-medium text-gray-700 mb-1">
                Study Material *
              </label>
              {(() => {
                const selectedRecipe = availableRecipes.find(recipe => recipe.id === selectedRecipeId)
                if (!selectedRecipe) return null

                if (selectedRecipe.materials.length === 0) {
                  return (
                    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <p className="text-yellow-800 text-sm">
                        This recipe has no study materials. Please add materials to the recipe first.
                      </p>
                    </div>
                  )
                }

                return (
                  <>
                    <select
                      id="material"
                      value={selectedMaterialId}
                      onChange={(e) => handleMaterialChange(e.target.value)}
                      className="input"
                      required
                    >
                      <option value="">Select a material</option>
                      {selectedRecipe.materials.map((material) => (
                        <option key={material.id} value={material.id}>
                          📚 {material.material_name} ({material.frequency}, {material.time_per_session} {material.time_unit})
                        </option>
                      ))}
                    </select>
                    <p className="text-xs text-gray-500 mt-1">
                      Choose which material you studied in this session
                    </p>
                  </>
                )
              })()}
            </div>
          )}

          {/* Hidden Title Field - Auto-generated */}
          <input
            type="hidden"
            value={title}
            readOnly
          />

          {/* Timer Interface - New TimerStopwatch Component */}
          <TimerStopwatch onDurationChange={handleDurationChange} />

          {/* Manual Duration Input */}
          <div>
            <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-1">
              Study Duration (minutes) *
            </label>
            <input
              id="duration"
              type="number"
              value={durationMinutes || ''}
              onChange={(e) => setDurationMinutes(parseInt(e.target.value) || 0)}
              className="input"
              placeholder="Enter duration manually or use timer"
              min="1"
              max="600"
              required
            />
            <p className="text-xs text-gray-500 mt-1">Duration will be set automatically when you stop the timer</p>
          </div>

          {/* Notes */}
          <div>
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              Notes & Reflections
            </label>
            <textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="input resize-y"
              style={{ minHeight: '100px' }}
              placeholder="How did the study session go? Any insights or observations..."
              maxLength={1000}
            />
          </div>

          {/* Submit Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary flex-1"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn-primary flex-1"
              disabled={loading || !selectedRecipeId || !title.trim() || durationMinutes <= 0}
            >
              {loading ? 'Creating...' : 'Create Study Log'}
            </button>
          </div>
        </form>
      </div>

      {/* Gamification Notifications */}
      <ExperienceGainAnimation
        points={experienceGained}
        show={showExperienceGain}
        onComplete={() => setShowExperienceGain(false)}
      />

      <ActionFeedback
        message="Study session completed! Keep up the great work!"
        type="success"
        show={showSuccessFeedback}
        onComplete={() => setShowSuccessFeedback(false)}
      />
    </div>
  )
}
