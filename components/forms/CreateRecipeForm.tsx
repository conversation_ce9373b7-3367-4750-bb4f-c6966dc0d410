import { useState } from 'react'
import { Hi<PERSON><PERSON>, HiTrash, HiXMark } from 'react-icons/hi2'
import { useAuth } from '../../lib/auth'
import { supabase } from '../../lib/supabase'

interface StudyMaterial {
  material_name: string
  frequency: string
  time_per_session: number
  time_unit: string
  order_index: number
}

interface CreateRecipeFormProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

export default function CreateRecipeForm({ isOpen, onClose, onSuccess }: CreateRecipeFormProps) {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Form state
  const [title, setTitle] = useState('')
  const [estimatedDurationTime, setEstimatedDurationTime] = useState('')
  const [estimatedDurationUnit, setEstimatedDurationUnit] = useState('weeks')
  const [tips, setTips] = useState('')
  const [tags, setTags] = useState<string[]>([])
  const [tagInput, setTagInput] = useState('')
  const [materials, setMaterials] = useState<StudyMaterial[]>([
    {
      material_name: '',
      frequency: 'daily',
      time_per_session: 0,
      time_unit: 'minutes',
      order_index: 0
    }
  ])

  const addMaterial = () => {
    setMaterials([
      ...materials,
      {
        material_name: '',
        frequency: 'daily',
        time_per_session: 0,
        time_unit: 'minutes',
        order_index: materials.length
      }
    ])
  }

  const removeMaterial = (index: number) => {
    if (materials.length > 1) {
      setMaterials(materials.filter((_, i) => i !== index))
    }
  }

  const updateMaterial = (index: number, field: keyof StudyMaterial, value: string | number) => {
    const updatedMaterials = materials.map((material, i) => {
      if (i === index) {
        return { ...material, [field]: value }
      }
      return material
    })
    setMaterials(updatedMaterials)
  }

  const addTag = (tagToAdd?: string) => {
    const newTag = (tagToAdd || tagInput).trim()
    if (newTag && !tags.includes(newTag)) {
      setTags([...tags, newTag])
      setTagInput('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleTagInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addTag()
    }
  }

  const resetForm = () => {
    setTitle('')
    setEstimatedDurationTime('')
    setEstimatedDurationUnit('weeks')
    setTips('')
    setTags([])
    setTagInput('')
    setMaterials([{
      material_name: '',
      frequency: 'daily',
      time_per_session: 0,
      time_unit: 'minutes',
      order_index: 0
    }])
    setError(null)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user) {
      setError('You must be logged in to create a recipe')
      return
    }

    setLoading(true)
    setError(null)

    try {
      // Use tags array directly
      const tagArray = tags

      // Prepare estimated duration
      const estimatedDuration = estimatedDurationTime.trim()
        ? `${estimatedDurationTime.trim()} ${estimatedDurationUnit}`
        : null

      // Prepare recipe data
      const recipeData = {
        user_id: user.id,
        title: title.trim(),
        estimated_duration: estimatedDuration,
        tips: tips.trim() || null,
        tags: tagArray,
      }

      // Create the recipe
      const { data: recipe, error: recipeError } = await supabase
        .from('study_recipes')
        .insert([recipeData])
        .select()
        .single()

      if (recipeError) throw recipeError

      // Create study materials
      const validMaterials = materials.filter(
        material => material.material_name.trim() && material.frequency
      )

      if (validMaterials.length > 0) {
        const materialsToInsert = validMaterials.map((material, index) => ({
          recipe_id: recipe.id,
          material_name: material.material_name.trim(),
          frequency: material.frequency,
          time_per_session: material.time_per_session,
          time_unit: material.time_unit,
          order_index: index,
        }))

        const { error: materialsError } = await supabase
          .from('study_materials')
          .insert(materialsToInsert)

        if (materialsError) throw materialsError
      }

      // Success
      resetForm()
      onSuccess?.()
      onClose()
    } catch (error: any) {
      console.error('Error creating recipe:', error)
      setError(error.message || 'Failed to create recipe')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" data-testid="create-recipe-modal">
      <div className="bg-white rounded-lg max-w-2xl w-full overflow-y-auto" style={{ maxHeight: '90vh' }}>
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
          <h2 className="text-xl font-bold">Create Study Recipe</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            data-testid="close-recipe-modal"
          >
            <HiXMark className="w-6 h-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Error Message */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md" data-testid="error-message">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          {/* Title */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
              Recipe Title *
            </label>
            <input
              id="title"
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="input"
              placeholder="e.g., JLPT N4 Preparation Plan"
              required
              maxLength={100}
              data-testid="recipe-title-input"
            />
            <p className="text-xs text-gray-500 mt-1">Include your learning goal in the title</p>
          </div>

          {/* Estimated Duration */}
          <div data-testid="estimated-duration-section">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              To Goal
            </label>
            <div className="flex space-x-2">
              <input
                type="number"
                value={estimatedDurationTime}
                onChange={(e) => setEstimatedDurationTime(e.target.value)}
                className="input flex-1"
                placeholder="e.g., 3"
                min="1"
                max="999"
                data-testid="estimated-duration-time"
              />
              <select
                value={estimatedDurationUnit}
                onChange={(e) => setEstimatedDurationUnit(e.target.value)}
                className="input w-24"
                data-testid="estimated-duration-unit"
              >
                <option value="days">Days</option>
                <option value="weeks">Weeks</option>
                <option value="months">Months</option>
                <option value="years">Years</option>
              </select>
            </div>
          </div>

          {/* Tags */}
          <div data-testid="tags-section">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tags
            </label>
            <div className="space-y-3">
              {/* Tag Input */}
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyDown={handleTagInputKeyDown}
                  className="input flex-1"
                  placeholder="Add tags (press Enter to add)"
                  maxLength={30}
                  data-testid="tag-input"
                />
                <button
                  type="button"
                  onClick={() => addTag()}
                  disabled={!tagInput.trim()}
                  className="btn-secondary flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                  data-testid="add-tag-button"
                >
                  <HiPlus className="w-4 h-4 mr-1" />
                  Add
                </button>
              </div>

              {/* Tag Chips */}
              {tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag, index) => (
                    <div
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-orange-100 text-orange-800 border border-orange-200"
                      data-testid={`tag-chip-${index}`}
                    >
                      <span>{tag}</span>
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="ml-2 text-orange-600 hover:text-orange-800"
                        data-testid={`remove-tag-${index}`}
                      >
                        <HiXMark className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
            <p className="text-xs text-gray-500 mt-1">You can also add tags by pressing the Enter key</p>
          </div>

          {/* Study Materials */}
          <div data-testid="study-materials-section">
            <div className="flex items-center justify-between mb-3">
              <label className="block text-sm font-medium text-gray-700">
                Study Materials
              </label>
              <button
                type="button"
                onClick={addMaterial}
                className="btn-secondary text-sm flex items-center"
                data-testid="add-material-button"
              >
                <HiPlus className="w-4 h-4 mr-1" />
                Add Material
              </button>
            </div>

            <div className="space-y-4">
              {materials.map((material, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4" data-testid={`study-material-${index}`}>
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm font-medium text-gray-700">
                      Material {index + 1}
                    </span>
                    {materials.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeMaterial(index)}
                        className="text-red-500 hover:text-red-700"
                        data-testid={`remove-material-${index}`}
                      >
                        <HiTrash className="w-4 h-4" />
                      </button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div className="md:col-span-2">
                      <input
                        type="text"
                        value={material.material_name}
                        onChange={(e) => updateMaterial(index, 'material_name', e.target.value)}
                        className="input"
                        placeholder="Material name (e.g., Genki II Textbook)"
                        maxLength={100}
                        data-testid={`material-name-${index}`}
                      />
                    </div>
                    <div>
                      <select
                        value={material.frequency}
                        onChange={(e) => updateMaterial(index, 'frequency', e.target.value)}
                        className="input"
                        data-testid={`material-frequency-${index}`}
                      >
                        <option value="daily">Daily</option>
                        <option value="1_time_week">Once a week</option>
                        <option value="2_times_week">2 times a week</option>
                        <option value="3_times_week">3 times a week</option>
                        <option value="4_times_week">4 times a week</option>
                        <option value="5_times_week">5 times a week</option>
                        <option value="6_times_week">6 times a week</option>
                        <option value="1_time_month">Once a month</option>
                        <option value="2_times_month">2 times a month</option>
                        <option value="3_times_month">3 times a month</option>
                        <option value="4_times_month">4 times a month</option>
                      </select>
                    </div>
                    <div className="flex space-x-2">
                      <input
                        type="number"
                        value={material.time_per_session || ''}
                        onChange={(e) => updateMaterial(index, 'time_per_session', parseInt(e.target.value) || 0)}
                        className="input flex-1"
                        placeholder="Time"
                        min="0"
                        max="999"
                        data-testid={`material-time-${index}`}
                      />
                      <select
                        value={material.time_unit}
                        onChange={(e) => updateMaterial(index, 'time_unit', e.target.value)}
                        className="input w-24"
                        data-testid={`material-time-unit-${index}`}
                      >
                        <option value="minutes">min</option>
                        <option value="hours">hrs</option>
                      </select>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Tips */}
          <div>
            <label htmlFor="tips" className="block text-sm font-medium text-gray-700 mb-1">
              Tips & Notes
            </label>
            <textarea
              id="tips"
              value={tips}
              onChange={(e) => setTips(e.target.value)}
              className="input resize-y"
              style={{ minHeight: '100px' }}
              placeholder="Share your tips and advice for this study plan..."
              maxLength={1000}
              data-testid="tips-input"
            />
          </div>

          {/* Submit Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary flex-1"
              disabled={loading}
              data-testid="cancel-recipe-button"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn-primary flex-1"
              disabled={loading || !title.trim()}
              data-testid="create-recipe-submit"
            >
              {loading ? 'Creating...' : 'Create Recipe'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
