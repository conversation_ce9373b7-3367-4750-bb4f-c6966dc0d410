import { ReactionSummary } from '@/types'

interface ReactionButtonProps {
  reaction: ReactionSummary
  isUserReaction: boolean
  onToggle: (emoji: string) => void
  disabled?: boolean
  loading?: boolean
}

export default function ReactionButton({ 
  reaction, 
  isUserReaction, 
  onToggle, 
  disabled = false,
  loading = false
}: ReactionButtonProps) {
  
  const handleClick = () => {
    if (!disabled && !loading) {
      onToggle(reaction.emoji)
    }
  }

  return (
    <button
      onClick={handleClick}
      disabled={disabled || loading}
      className={`
        flex items-center space-x-1 px-2 py-1 rounded-full text-sm transition-all border
        ${isUserReaction
          ? 'bg-white text-black border-gray-300 hover:bg-gray-50'
          : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
        }
        ${loading ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'}
        disabled:opacity-50 disabled:cursor-not-allowed
      `}
      title={`${reaction.count} ${reaction.count === 1 ? 'person' : 'people'} reacted with ${reaction.emoji}`}
    >
      <span className="text-base">{reaction.emoji}</span>
      <span className="font-medium">{reaction.count}</span>
    </button>
  )
}
