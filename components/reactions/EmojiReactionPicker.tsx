import { useState, useRef, useEffect } from 'react'
import { AVAILABLE_EMOJIS } from '@/lib/api/reactions'
import { HiOutlineFaceSmile } from 'react-icons/hi2'

interface EmojiReactionPickerProps {
  onEmojiSelect: (emoji: string) => void
  disabled?: boolean
  userReactions: string[]
}

export default function EmojiReactionPicker({ 
  onEmojiSelect, 
  disabled = false,
  userReactions = []
}: EmojiReactionPickerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside or when disabled
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Close picker when disabled
  useEffect(() => {
    if (disabled && isOpen) {
      setIsOpen(false)
    }
  }, [disabled, isOpen])

  const handleEmojiClick = (emoji: string) => {
    if (!disabled) {
      onEmojiSelect(emoji)
      setIsOpen(false)
    }
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          flex items-center space-x-2 transition-colors
          ${disabled
            ? 'text-gray-400 cursor-not-allowed'
            : 'text-black hover:text-gray-600'
          }
        `}
        title={disabled ? "Loading..." : "Add reaction"}
      >
        <HiOutlineFaceSmile className="w-5 h-5" />
        <span className="text-sm font-medium">React</span>
      </button>

      {isOpen && (
        <div className="absolute bottom-full left-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg p-2 z-50 min-w-max">
          <div className="flex gap-1">
            {AVAILABLE_EMOJIS.map(({ emoji, label }) => {
              const hasReacted = userReactions.includes(emoji)

              return (
                <button
                  key={emoji}
                  onClick={() => handleEmojiClick(emoji)}
                  className={`
                    text-xl p-2 rounded-lg transition-all hover:scale-110 hover:bg-gray-100
                    ${hasReacted ? 'bg-gray-100 ring-2 ring-black' : ''}
                  `}
                  title={label}
                >
                  {emoji}
                </button>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}
