import { LogReactions } from '@/types'
import ReactionButton from './ReactionButton'

interface ReactionDisplayProps {
  reactions: LogReactions
  onEmojiToggle: (emoji: string) => void
  loadingEmoji?: string | null
  disabled?: boolean
  onAuthRequired?: (action: () => void) => void
}

export default function ReactionDisplay({ 
  reactions, 
  onEmojiToggle, 
  loadingEmoji = null,
  disabled = false 
}: ReactionDisplayProps) {
  const hasReactions = reactions.reactions && reactions.reactions.length > 0

  return (
    <div className="flex flex-wrap items-center gap-2 mt-3">
      {/* Existing Reactions */}
      {hasReactions && reactions.reactions.map((reaction) => (
        <ReactionButton
          key={reaction.emoji}
          reaction={reaction}
          isUserReaction={reactions.userReactions.includes(reaction.emoji)}
          onToggle={onEmojiToggle}
          disabled={disabled}
          loading={loadingEmoji === reaction.emoji}
        />
      ))}

    </div>
  )
}
