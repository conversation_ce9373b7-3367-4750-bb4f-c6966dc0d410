import { NotificationSystem } from '@/components/gamification/NotificationSystem'
import { useAuth } from '@/lib/auth'
import { useGamification } from '@/lib/hooks/useGamificationRealtime'
import { Achievement, UserStats } from '@/types'
import { createContext, ReactNode, useContext } from 'react'

interface GamificationContextType {
  userStats: UserStats | null
  newAchievements: Achievement[]
  levelUpNotification: {
    show: boolean
    oldLevel: number
    newLevel: number
  } | null
  loading: boolean
  error: string | null
  refreshData: () => void
}

const GamificationContext = createContext<GamificationContextType | null>(null)

interface GamificationProviderProps {
  children: ReactNode
}

export function GamificationProvider({ children }: GamificationProviderProps) {
  const { user } = useAuth()
  
  const {
    userStats,
    newAchievements,
    levelUpNotification,
    loading,
    error,
    dismissLevelUpNotification,
    dismissAchievement,
    refreshData
  } = useGamification(user?.id)

  const contextValue: GamificationContextType = {
    userStats,
    newAchievements,
    levelUpNotification,
    loading,
    error,
    refreshData
  }

  return (
    <GamificationContext.Provider value={contextValue}>
      {children}
      
      {/* Global Notification System */}
      {user && (
        <NotificationSystem
          levelUpNotification={levelUpNotification}
          newAchievements={newAchievements}
          onDismissLevelUp={dismissLevelUpNotification}
          onDismissAchievement={dismissAchievement}
        />
      )}
    </GamificationContext.Provider>
  )
}

export function useGamificationContext() {
  const context = useContext(GamificationContext)
  if (!context) {
    throw new Error('useGamificationContext must be used within a GamificationProvider')
  }
  return context
}

// Hook for components that need to trigger gamification updates
export function useGamificationActions() {
  const { refreshData } = useGamificationContext()
  
  const triggerExperienceGain = async (points: number) => {
    // This could trigger a visual animation
    console.log(`Experience gained: ${points} XP`)
    
    // Refresh data to get updated stats
    setTimeout(refreshData, 500) // Small delay to ensure database is updated
  }

  const triggerAchievementCheck = async () => {
    // Refresh data to check for new achievements
    setTimeout(refreshData, 500)
  }

  return {
    triggerExperienceGain,
    triggerAchievementCheck,
    refreshData
  }
}
