import { useEffect, useRef, useState } from 'react'
import { searchUsersForMention } from '../../lib/api/comments'

interface MentionSuggestion {
  username: string
  display_name?: string
}

interface CommentFormProps {
  onSubmit: (content: string, parentId?: string) => Promise<void>
  placeholder?: string
  parentId?: string
  isReply?: boolean
  onCancel?: () => void
  loading?: boolean
  autoFocus?: boolean
  initialContent?: string
}

export default function CommentForm({
  onSubmit,
  placeholder = "Write a comment...",
  parentId,
  isReply = false,
  onCancel,
  loading = false,
  autoFocus = false,
  initialContent = ''
}: CommentFormProps) {
  const [content, setContent] = useState(initialContent)
  const [showMentions, setShowMentions] = useState(false)
  const [mentionSuggestions, setMentionSuggestions] = useState<MentionSuggestion[]>([])
  const [mentionQuery, setMentionQuery] = useState('')
  const [mentionStartPos, setMentionStartPos] = useState(0)
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(0)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Update content when initialContent changes
  useEffect(() => {
    setContent(initialContent)
  }, [initialContent])

  // Auto-focus if requested
  useEffect(() => {
    if (autoFocus && textareaRef.current) {
      textareaRef.current.focus()
    }
  }, [autoFocus])

  // Handle mention detection and suggestions
  useEffect(() => {
    const handleMentions = async () => {
      const cursorPos = textareaRef.current?.selectionStart || 0
      const textBeforeCursor = content.slice(0, cursorPos)
      const mentionMatch = textBeforeCursor.match(/@(\w*)$/)

      if (mentionMatch) {
        const query = mentionMatch[1]
        setMentionQuery(query)
        setMentionStartPos(cursorPos - mentionMatch[0].length)
        setShowMentions(true)
        setSelectedSuggestionIndex(0)

        if (query.length >= 1) {
          try {
            const suggestions = await searchUsersForMention(query)
            setMentionSuggestions(suggestions)
          } catch (error) {
            console.error('Error fetching mention suggestions:', error)
            setMentionSuggestions([])
          }
        } else {
          setMentionSuggestions([])
        }
      } else {
        setShowMentions(false)
        setMentionSuggestions([])
      }
    }

    handleMentions()
  }, [content])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!content.trim()) {
      return
    }

    if (loading) {
      return
    }

    try {
      await onSubmit(content.trim(), parentId)

      // Clear content only for new comments (not edits or replies with initial content)
      if (!initialContent) {
        setContent('')
      }
      setShowMentions(false)
    } catch (error) {
      console.error('Error submitting comment:', error)
      alert('Failed to submit comment. Please try again.')
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (showMentions && mentionSuggestions.length > 0) {
      if (e.key === 'ArrowDown') {
        e.preventDefault()
        setSelectedSuggestionIndex(prev => 
          prev < mentionSuggestions.length - 1 ? prev + 1 : 0
        )
      } else if (e.key === 'ArrowUp') {
        e.preventDefault()
        setSelectedSuggestionIndex(prev => 
          prev > 0 ? prev - 1 : mentionSuggestions.length - 1
        )
      } else if (e.key === 'Enter' || e.key === 'Tab') {
        e.preventDefault()
        insertMention(mentionSuggestions[selectedSuggestionIndex])
      } else if (e.key === 'Escape') {
        setShowMentions(false)
      }
    } else if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const insertMention = (suggestion: MentionSuggestion) => {
    const beforeMention = content.slice(0, mentionStartPos)
    const afterMention = content.slice(textareaRef.current?.selectionStart || 0)
    const newContent = `${beforeMention}@${suggestion.username} ${afterMention}`
    
    setContent(newContent)
    setShowMentions(false)
    
    // Focus back to textarea
    setTimeout(() => {
      if (textareaRef.current) {
        const newCursorPos = mentionStartPos + suggestion.username.length + 2
        textareaRef.current.focus()
        textareaRef.current.setSelectionRange(newCursorPos, newCursorPos)
      }
    }, 0)
  }

  const handleCancel = () => {
    setContent(initialContent)
    setShowMentions(false)
    if (onCancel) {
      onCancel()
    }
  }

  return (
    <div className="relative">
      <form onSubmit={handleSubmit} className="space-y-3">
        <div className="relative">
          <textarea
            ref={textareaRef}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            rows={isReply ? 2 : 3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
            disabled={loading}
            maxLength={1000}
          />
          
          {/* Character count */}
          <div className="absolute bottom-2 right-2 text-xs text-gray-400">
            {content.length}/1000
          </div>

          {/* Mention suggestions dropdown */}
          {showMentions && mentionSuggestions.length > 0 && (
            <div className="absolute z-50 w-64 bg-white border border-gray-200 rounded-lg shadow-lg mt-1">
              {mentionSuggestions.map((suggestion, index) => (
                <button
                  key={suggestion.username}
                  type="button"
                  onClick={() => insertMention(suggestion)}
                  className={`w-full px-3 py-2 text-left hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg ${
                    index === selectedSuggestionIndex ? 'bg-gray-50' : ''
                  }`}
                >
                  <div className="font-medium">@{suggestion.username}</div>
                  {suggestion.display_name && (
                    <div className="text-sm text-gray-500">{suggestion.display_name}</div>
                  )}
                </button>
              ))}
            </div>
          )}
        </div>

        <div className="flex items-center justify-between">
          <div className="text-xs text-gray-500">
            {isReply ? 'Reply to comment' : 'Tip: Use @ to mention users, Cmd/Ctrl+Enter to submit'}
          </div>
          
          <div className="flex items-center space-x-2">
            {isReply && onCancel && (
              <button
                type="button"
                onClick={handleCancel}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                disabled={loading}
              >
                Cancel
              </button>
            )}
            
            <button
              type="submit"
              disabled={!content.trim() || loading}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                !content.trim() || loading
                  ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                  : 'bg-black text-white hover:bg-gray-800'
              }`}
            >
              {loading ? (
                <div className="inline-flex items-center">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Submitting...
                </div>
              ) : (
                isReply ? 'Reply' : 'Comment'
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  )
}
