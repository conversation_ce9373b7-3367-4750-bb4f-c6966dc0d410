import { Comment } from '@/types'
import { Fragment, useState } from 'react'
import CommentForm from './CommentForm'

interface CommentItemProps {
  comment: Comment
  currentUser: any
  onReply: (content: string, parentId: string) => Promise<void>
  onEdit: (commentId: string, content: string) => Promise<void>
  onDelete: (commentId: string) => Promise<void>
  level?: number
  replyToUsername?: string
}

// Helper function to flatten nested replies for display
const flattenReplies = (replies: Comment[]): Array<{ id: string; comment: Comment; replyToUsername?: string }> => {
  const flattened: Array<{ id: string; comment: Comment; replyToUsername?: string }> = []
  const processedIds = new Set<string>() // Track processed comment IDs to prevent duplicates
  
  const processReplies = (replyList: Comment[], parentUsername?: string) => {
    replyList.forEach(reply => {
      // Skip if already processed to prevent duplicates
      if (processedIds.has(reply.id)) {
        console.warn('⚠️ Duplicate comment detected in flattenReplies:', reply.id, reply.content)
        return
      }
      
      processedIds.add(reply.id)
      flattened.push({
        id: reply.id,
        comment: { ...reply, replies: [] }, // Remove nested replies to prevent infinite recursion
        replyToUsername: parentUsername
      })
      
      // Recursively process nested replies
      if (reply.replies && reply.replies.length > 0) {
        processReplies(reply.replies, reply.author?.username)
      }
    })
  }
  
  processReplies(replies)
  
  // Sort by creation time to maintain chronological order
  return flattened.sort((a, b) => 
    new Date(a.comment.created_at).getTime() - new Date(b.comment.created_at).getTime()
  )
}

export default function CommentItem({
  comment,
  currentUser,
  onReply,
  onEdit,
  onDelete,
  level = 0,
  replyToUsername
}: CommentItemProps) {
  const [showReplyForm, setShowReplyForm] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [replyLoading, setReplyLoading] = useState(false)
  const [editLoading, setEditLoading] = useState(false)


  // Validate comment data
  if (!comment || typeof comment !== 'object' || !comment.id) {
    console.error('❌ CommentItem: Invalid comment data:', comment)

    // Send rendering errors to Sentry
    import('@sentry/nextjs').then(Sentry => {
      Sentry.captureException(new Error('Invalid comment data in CommentItem'), {
        tags: {
          component: 'CommentItem',
          errorType: 'invalid_comment_data'
        },
        extra: {
          function: 'CommentItem.render',
          comment,
          level
        }
      })
    })

    return null
  }

  const formatTimeAgo = (dateString: string) => {
    try {
      if (!dateString) return 'Unknown time'

      const date = new Date(dateString)
      if (isNaN(date.getTime())) return 'Invalid date'

      const now = new Date()
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

      if (diffInMinutes < 1) return 'Just now'
      if (diffInMinutes < 60) return `${diffInMinutes}m ago`

      const diffInHours = Math.floor(diffInMinutes / 60)
      if (diffInHours < 24) return `${diffInHours}h ago`

      const diffInDays = Math.floor(diffInHours / 24)
      if (diffInDays < 7) return `${diffInDays}d ago`

      return date.toLocaleDateString()
    } catch (error) {
      console.error('Error formatting date:', error)
      return 'Unknown time'
    }
  }

  const renderContent = (content: string) => {
    if (!content || typeof content !== 'string') {
      return null
    }

    // Split content by mentions and create React elements
    const parts = content.split(/(@\w+)/g)
    return (
      <>
        {parts.map((part, index) => {
          if (part.match(/^@\w+$/)) {
            return (
              <span key={`mention-${index}`} className="text-blue-600 font-medium">
                {part}
              </span>
            )
          }
          return <Fragment key={`text-${index}`}>{part}</Fragment>
        })}
      </>
    )
  }

  const handleReply = async (content: string) => {
    setReplyLoading(true)
    try {
      await onReply(content, comment.id)
      setShowReplyForm(false)
    } catch (error) {
      console.error('Error replying to comment:', error)

      // Send reply errors to Sentry
      import('@sentry/nextjs').then(Sentry => {
        Sentry.captureException(error, {
          tags: {
            component: 'CommentItem',
            errorType: 'comment_reply_error'
          },
          extra: {
            function: 'CommentItem.handleReply',
            commentId: comment.id,
            content: content?.substring(0, 100)
          }
        })
      })
    } finally {
      setReplyLoading(false)
    }
  }

  const handleEdit = async (content: string) => {
    setEditLoading(true)
    try {
      await onEdit(comment.id, content)
      setIsEditing(false)
    } catch (error) {
      console.error('Error editing comment:', error)

      // Send edit errors to Sentry
      import('@sentry/nextjs').then(Sentry => {
        Sentry.captureException(error, {
          tags: {
            component: 'CommentItem',
            errorType: 'comment_edit_error'
          },
          extra: {
            function: 'CommentItem.handleEdit',
            commentId: comment.id,
            content: content?.substring(0, 100)
          }
        })
      })
    } finally {
      setEditLoading(false)
    }
  }

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this comment?')) return
    
    setIsDeleting(true)
    try {
      await onDelete(comment.id)
    } catch (error) {
      console.error('Error deleting comment:', error)

      // Send delete errors to Sentry
      import('@sentry/nextjs').then(Sentry => {
        Sentry.captureException(error, {
          tags: {
            component: 'CommentItem',
            errorType: 'comment_delete_error'
          },
          extra: {
            function: 'CommentItem.handleDelete',
            commentId: comment.id
          }
        })
      })

      setIsDeleting(false)
    }
  }

  const isOwnComment = currentUser?.id && comment.user_id && currentUser.id === comment.user_id
  const canReply = currentUser

  return (
    <div className={`${level > 0 && level < 2 ? 'ml-8 border-l border-gray-200 pl-4' : ''}`}>
      <div className="space-y-3">
        {/* Comment Header */}
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium">
              {(comment.author?.display_name || comment.author?.username || '?').charAt(0).toUpperCase()}
            </span>
          </div>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <span className="font-medium text-gray-900">
                {comment.author?.display_name || comment.author?.username || 'Unknown User'}
              </span>
              <span className="text-sm text-gray-500">
                @{comment.author?.username || 'unknown'}
              </span>
              <span className="text-sm text-gray-400">•</span>
              <span className="text-sm text-gray-500">
                {formatTimeAgo(comment.created_at)}
              </span>
            </div>
          </div>
        </div>

        {/* Comment Content */}
        <div className="ml-11">
          {/* Show reply-to indicator for reply comments */}
          {(replyToUsername || comment.parent_comment?.author?.username) && (
            <div className="mb-2 text-sm text-gray-500">
              <span className="text-blue-600">
                @{replyToUsername || comment.parent_comment?.author?.username}
              </span>への返信
            </div>
          )}
          
          {isEditing ? (
            <CommentForm
              onSubmit={handleEdit}
              placeholder="Edit your comment..."
              loading={editLoading}
              onCancel={() => setIsEditing(false)}
              isReply={true}
              autoFocus={true}
              initialContent={comment.content || ''}
            />
          ) : (
            <div className="text-gray-900 leading-relaxed whitespace-pre-wrap">
              {renderContent(comment.content || '')}
            </div>
          )}
        </div>

        {/* Comment Actions */}
        {!isEditing && (
          <div className="ml-11 flex items-center space-x-4">
            {canReply && (
              <button
                onClick={() => setShowReplyForm(!showReplyForm)}
                className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
              >
                Reply
              </button>
            )}

            {isOwnComment && (
              <>
                <button
                  onClick={() => setIsEditing(true)}
                  className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
                >
                  Edit
                </button>

                <button
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="text-sm text-red-500 hover:text-red-700 transition-colors disabled:opacity-50"
                >
                  {isDeleting ? 'Deleting...' : 'Delete'}
                </button>
              </>
            )}
          </div>
        )}

        {/* Reply Form */}
        {showReplyForm && canReply && (
          <div className="ml-11 mt-3">
            <CommentForm
              onSubmit={handleReply}
              placeholder={`Reply to @${comment.author?.username || 'user'}...`}
              parentId={comment.id}
              isReply={true}
              onCancel={() => setShowReplyForm(false)}
              loading={replyLoading}
              autoFocus={true}
            />
          </div>
        )}

        {/* Replies - Show replies with proper hierarchy control */}
        {comment.replies && Array.isArray(comment.replies) && comment.replies.length > 0 && (
          <div className="space-y-4">
            {level === 0 ? (
              // For top-level comments, show replies normally
              comment.replies.map((reply) => {
                if (!reply || typeof reply !== 'object' || !reply.id) {
                  console.error('Invalid reply data:', reply)
                  return null
                }
                return (
                  <CommentItem
                    key={reply.id}
                    comment={reply}
                    currentUser={currentUser}
                    onReply={onReply}
                    onEdit={onEdit}
                    onDelete={onDelete}
                    level={level + 1}
                    replyToUsername={comment.author?.username}
                  />
                )
              })
            ) : level === 1 ? (
              // For 2nd level comments, flatten all deeper replies to avoid deep nesting
              (() => {
                const flattenedReplies = flattenReplies(comment.replies)
                return flattenedReplies.map((reply) => (
                  <CommentItem
                    key={reply.id}
                    comment={reply.comment}
                    currentUser={currentUser}
                    onReply={onReply}
                    onEdit={onEdit}
                    onDelete={onDelete}
                    level={2}
                    replyToUsername={reply.replyToUsername}
                  />
                ))
              })()
            ) : null}
          </div>
        )}
      </div>
    </div>
  )
}
