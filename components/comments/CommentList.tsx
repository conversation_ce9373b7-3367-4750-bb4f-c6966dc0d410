import { useEffect, useState } from 'react'
import { HiOutlineChatBubbleLeft } from 'react-icons/hi2'
import {
  addComment,
  deleteComment,
  getLogComments,
  getRecipeComments,
  updateComment
} from '../../lib/api/comments'
import { Comment } from '../../types'
import CommentForm from './CommentForm'
import CommentItem from './CommentItem'

interface CommentListProps {
  recipeId?: string
  logId?: string
  currentUser: any
  initialCommentCount?: number
  onCommentCountChange?: (count: number) => void
  isOpen?: boolean
  onToggle?: () => void
  onAuthRequired?: (action: () => void) => void
}

export default function CommentList({
  recipeId,
  logId,
  currentUser,
  initialCommentCount = 0,
  onCommentCountChange,
  isOpen = false,
  onToggle,
  onAuthRequired
}: CommentListProps) {
  const [comments, setComments] = useState<Comment[]>([])
  const [loading, setLoading] = useState(false)
  const [submitLoading, setSubmitLoading] = useState(false)
  const [showComments, setShowComments] = useState(isOpen)
  const [commentCount, setCommentCount] = useState(initialCommentCount)


  // Sync with external isOpen prop
  useEffect(() => {
    setShowComments(isOpen)
  }, [isOpen])

  // Load comments when component mounts or when showComments becomes true
  useEffect(() => {
    if (showComments && comments.length === 0) {
      loadComments()
    }
  }, [showComments])

  const loadComments = async () => {
    if (!recipeId && !logId) return

    setLoading(true)
    try {
      const fetchedComments = recipeId
        ? await getRecipeComments(recipeId)
        : await getLogComments(logId!)

      setComments(fetchedComments)

      // Count total comments including replies
      const totalCount = countTotalComments(fetchedComments)
      setCommentCount(totalCount)
      onCommentCountChange?.(totalCount)
    } catch (error) {
      console.error('Error loading comments:', error)
    } finally {
      setLoading(false)
    }
  }

  const countTotalComments = (commentList: Comment[]): number => {
    return commentList.reduce((total, comment) => {
      return total + 1 + (comment.replies ? countTotalComments(comment.replies) : 0)
    }, 0)
  }

  const handleAddComment = async (content: string, parentId?: string) => {
    if (!currentUser) {
      alert('You must be logged in to comment')
      return
    }

    if (!recipeId && !logId) {
      alert('Invalid recipe or log ID')
      return
    }

    if (!content || content.trim().length === 0) {
      alert('Comment content cannot be empty')
      return
    }

    if (submitLoading) {
      return
    }

    setSubmitLoading(true)

    try {
      const newComment = await addComment(content, recipeId, logId, parentId)

      // Reload comments to get the correct structure with parent_comment info
      await loadComments()
    } catch (error) {
      console.error('Error adding comment:', error)
      alert('Failed to add comment. Please try again.')
    } finally {
      setSubmitLoading(false)
    }
  }

  const addReplyToComments = (commentList: Comment[], parentId: string, reply: Comment): Comment[] => {
    return commentList.map(comment => {
      if (comment.id === parentId) {
        return {
          ...comment,
          replies: [...(comment.replies || []), reply]
        }
      } else if (comment.replies) {
        return {
          ...comment,
          replies: addReplyToComments(comment.replies, parentId, reply)
        }
      }
      return comment
    })
  }

  const handleEditComment = async (commentId: string, content: string) => {
    try {
      const updatedComment = await updateComment(commentId, content)

      setComments(prevComments => {
        const newComments = updateCommentInList(prevComments, commentId, updatedComment)
        return newComments
      })
    } catch (error) {
      console.error('Error editing comment:', error)
      throw error
    }
  }

  const updateCommentInList = (commentList: Comment[], commentId: string, updatedComment: Comment): Comment[] => {
    return commentList.map(comment => {
      if (comment.id === commentId) {
        // Preserve existing replies when updating a comment
        const result = {
          ...updatedComment,
          replies: comment.replies || []
        }
        return result
      } else if (comment.replies) {
        return {
          ...comment,
          replies: updateCommentInList(comment.replies, commentId, updatedComment)
        }
      }
      return comment
    })
  }

  const handleDeleteComment = async (commentId: string) => {
    try {
      await deleteComment(commentId)
      
      // Remove comment and count how many were removed (including replies)
      let removedCount = 0
      const newComments = removeCommentFromList(comments, commentId, (count) => {
        removedCount = count
      })
      
      setComments(newComments)
      
      // Update comment count
      const newCount = Math.max(0, commentCount - removedCount)
      setCommentCount(newCount)
      onCommentCountChange?.(newCount)
    } catch (error) {
      console.error('Error deleting comment:', error)
      throw error
    }
  }

  const removeCommentFromList = (
    commentList: Comment[], 
    commentId: string, 
    onRemoved: (count: number) => void
  ): Comment[] => {
    const result: Comment[] = []
    
    for (const comment of commentList) {
      if (comment.id === commentId) {
        // Count this comment and all its replies
        const removedCount = 1 + countTotalComments(comment.replies || [])
        onRemoved(removedCount)
        // Don't add to result (effectively removing it)
      } else {
        if (comment.replies) {
          const updatedReplies = removeCommentFromList(comment.replies, commentId, onRemoved)
          result.push({
            ...comment,
            replies: updatedReplies
          })
        } else {
          result.push(comment)
        }
      }
    }
    
    return result
  }

  const toggleComments = () => {
    if (onToggle) {
      onToggle()
    } else {
      setShowComments(!showComments)
    }
  }

  return (
    <>
      {/* Comments Section - Only show if comments are toggled */}
      {showComments && (
        <div className="mt-4 space-y-4 pt-4 w-full">
          {/* Add Comment Form */}
          {currentUser ? (
            <CommentForm
              onSubmit={handleAddComment}
              placeholder="Write a comment..."
              loading={submitLoading}
            />
          ) : (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
              <p className="text-gray-600 mb-3">Sign in to post a comment</p>
              <button 
                onClick={() => onAuthRequired?.(() => {})}
                className="bg-black text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-800 transition-colors"
              >
                Sign in to Comment
              </button>
            </div>
          )}

          {/* Comments List */}
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="w-6 h-6 border-2 border-gray-300 border-t-black rounded-full animate-spin" />
            </div>
          ) : comments.length > 0 ? (
            <div className="space-y-6">
              {comments.map((comment) => (
                <CommentItem
                  key={comment.id}
                  comment={comment}
                  currentUser={currentUser}
                  onReply={handleAddComment}
                  onEdit={handleEditComment}
                  onDelete={handleDeleteComment}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <HiOutlineChatBubbleLeft className="w-12 h-12 mx-auto mb-3 text-gray-300" />
              <p>No comments yet. Be the first to comment!</p>
            </div>
          )}
        </div>
      )}
    </>
  )
}
