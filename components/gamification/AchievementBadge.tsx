import { Achievement, ACHIEVEMENT_CATEGORIES, RARITY_COLORS } from '@/lib/gamification/achievements'
import { HiOutlineCheckCircle, HiOutlineLockClosed } from 'react-icons/hi2'

interface AchievementBadgeProps {
  achievement: Achievement
  isEarned: boolean
  size?: 'sm' | 'md' | 'lg'
  showDescription?: boolean
  className?: string
}

export function AchievementBadge({
  achievement, 
  isEarned, 
  size = 'md', 
  showDescription = true,
  className = '' 
}: AchievementBadgeProps) {
  const category = ACHIEVEMENT_CATEGORIES[achievement.category]
  const rarityColor = RARITY_COLORS[achievement.rarity]
  
  const sizeClasses = {
    sm: 'w-8 h-8 text-lg',
    md: 'w-12 h-12 text-2xl',
    lg: 'w-16 h-16 text-3xl'
  }

  const containerClasses = {
    sm: 'p-2',
    md: 'p-3',
    lg: 'p-4'
  }

  return (
    <div 
      className={`
        bg-white border-2 rounded-lg transition-all hover:shadow-md
        ${isEarned ? 'border-black' : 'border-gray-200'}
        ${containerClasses[size]}
        ${className}
      `}
      style={{ 
        borderColor: isEarned ? rarityColor : '#E5E7EB',
        opacity: isEarned ? 1 : 0.6 
      }}
    >
      <div className="flex items-start space-x-3">
        {/* Achievement Icon */}
        <div 
          className={`
            ${sizeClasses[size]} 
            rounded-full 
            flex items-center justify-center 
            border-2
            ${isEarned ? 'bg-white' : 'bg-gray-100'}
          `}
          style={{ 
            borderColor: isEarned ? rarityColor : '#D1D5DB',
            color: isEarned ? rarityColor : '#9CA3AF'
          }}
        >
          {isEarned ? achievement.icon : <HiOutlineLockClosed className="w-1/2 h-1/2" />}
        </div>
        
        {/* Achievement Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <h3 
              className={`font-medium ${size === 'sm' ? 'text-sm' : 'text-base'}`}
              style={{ color: isEarned ? '#000000' : '#6B7280' }}
            >
              {achievement.name}
            </h3>
            {isEarned && (
              <HiOutlineCheckCircle 
                className="w-4 h-4 text-green-600" 
              />
            )}
          </div>
          
          {/* Rarity Badge */}
          <div className="flex items-center space-x-2 mb-2">
            <span 
              className="px-2 py-1 rounded-full text-xs font-medium text-white"
              style={{ backgroundColor: rarityColor }}
            >
              {achievement.rarity.charAt(0).toUpperCase() + achievement.rarity.slice(1)}
            </span>
            <span className="text-xs text-gray-500">
              {category.name}
            </span>
          </div>
          
          {/* Description */}
          {showDescription && (
            <p 
              className={`text-gray-600 ${size === 'sm' ? 'text-xs' : 'text-sm'}`}
            >
              {achievement.description}
            </p>
          )}
          
          {/* Points Reward */}
          <div className="mt-2 flex items-center justify-between">
            <span 
              className={`font-medium ${size === 'sm' ? 'text-xs' : 'text-sm'}`}
              style={{ color: isEarned ? '#000000' : '#6B7280' }}
            >
              +{achievement.pointsReward} XP
            </span>
            {!isEarned && (
              <span className="text-xs text-gray-400">
                Not earned
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// Compact version for profile display
interface CompactAchievementProps {
  achievement: Achievement
  isEarned: boolean
  className?: string
}

export function CompactAchievement({ achievement, isEarned, className = '' }: CompactAchievementProps) {
  const rarityColor = RARITY_COLORS[achievement.rarity]
  
  return (
    <div 
      className={`
        inline-flex items-center space-x-2 px-3 py-2 rounded-lg border
        ${isEarned ? 'bg-white border-black' : 'bg-gray-50 border-gray-200'}
        ${className}
      `}
      title={achievement.description}
    >
      <span className="text-lg">
        {isEarned ? achievement.icon : <HiOutlineLockClosed className="w-4 h-4 text-gray-400" />}
      </span>
      <span 
        className={`text-sm font-medium ${isEarned ? 'text-black' : 'text-gray-500'}`}
      >
        {achievement.name}
      </span>
    </div>
  )
}

// Achievement notification component
interface AchievementNotificationProps {
  achievement: Achievement
  onClose: () => void
}

export function AchievementNotification({ achievement, onClose }: AchievementNotificationProps) {
  const category = ACHIEVEMENT_CATEGORIES[achievement.category]
  const rarityColor = RARITY_COLORS[achievement.rarity]
  
  return (
    <div className="fixed top-4 right-4 bg-white border-2 border-black rounded-lg p-4 shadow-lg z-50 max-w-sm">
      <div className="flex items-start space-x-3">
        <div 
          className="w-12 h-12 rounded-full flex items-center justify-center text-2xl border-2"
          style={{ borderColor: rarityColor, backgroundColor: '#ffffff' }}
        >
          {achievement.icon}
        </div>
        
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-1">
            <span className="text-sm font-medium text-gray-600">Achievement Unlocked!</span>
            <HiOutlineCheckCircle className="w-4 h-4 text-green-600" />
          </div>
          
          <h3 className="font-bold text-black mb-1">
            {achievement.name}
          </h3>
          
          <p className="text-sm text-gray-600 mb-2">
            {achievement.description}
          </p>
          
          <div className="flex items-center justify-between">
            <span 
              className="px-2 py-1 rounded-full text-xs font-medium text-white"
              style={{ backgroundColor: rarityColor }}
            >
              {achievement.rarity.charAt(0).toUpperCase() + achievement.rarity.slice(1)}
            </span>
            <span className="text-sm font-medium text-black">
              +{achievement.pointsReward} XP
            </span>
          </div>
        </div>
        
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors"
        >
          ×
        </button>
      </div>
    </div>
  )
}

// Achievement grid for profile page
interface AchievementGridProps {
  achievements: Achievement[]
  earnedAchievements: string[]
  category?: Achievement['category']
  className?: string
}

export function AchievementGrid({ 
  achievements, 
  earnedAchievements, 
  category,
  className = '' 
}: AchievementGridProps) {
  const filteredAchievements = category 
    ? achievements.filter(a => a.category === category)
    : achievements

  const sortedAchievements = filteredAchievements.sort((a, b) => {
    const aEarned = earnedAchievements.includes(a.id)
    const bEarned = earnedAchievements.includes(b.id)
    
    // Earned achievements first
    if (aEarned && !bEarned) return -1
    if (!aEarned && bEarned) return 1
    
    // Then by rarity (legendary first)
    const rarityOrder = { legendary: 0, epic: 1, rare: 2, common: 3 }
    return rarityOrder[a.rarity] - rarityOrder[b.rarity]
  })

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 gap-4 ${className}`}>
      {sortedAchievements.map((achievement) => (
        <AchievementBadge
          key={achievement.id}
          achievement={achievement}
          isEarned={earnedAchievements.includes(achievement.id)}
          size="md"
        />
      ))}
    </div>
  )
}
