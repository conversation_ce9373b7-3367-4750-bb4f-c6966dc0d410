import { useState, useEffect } from 'react'
import { Achievement } from '@/types'
import { LevelUpNotification } from './RankBadge'
import { AchievementNotification } from './AchievementBadge'
import { HiOutlineXMark } from 'react-icons/hi2'

interface NotificationSystemProps {
  levelUpNotification: {
    show: boolean
    oldLevel: number
    newLevel: number
  } | null
  newAchievements: Achievement[]
  onDismissLevelUp: () => void
  onDismissAchievement: (achievementId: string) => void
}

export function NotificationSystem({
  levelUpNotification,
  newAchievements,
  onDismissLevelUp,
  onDismissAchievement
}: NotificationSystemProps) {
  const [visibleAchievements, setVisibleAchievements] = useState<Achievement[]>([])
  const [achievementQueue, setAchievementQueue] = useState<Achievement[]>([])

  // Manage achievement notification queue
  useEffect(() => {
    if (newAchievements.length > 0) {
      setAchievementQueue(prev => [...prev, ...newAchievements])
    }
  }, [newAchievements])

  // Show achievements one by one
  useEffect(() => {
    if (achievementQueue.length > 0 && visibleAchievements.length === 0) {
      const nextAchievement = achievementQueue[0]
      setVisibleAchievements([nextAchievement])
      setAchievementQueue(prev => prev.slice(1))

      // Auto-dismiss after 5 seconds
      const timer = setTimeout(() => {
        handleDismissAchievement(nextAchievement.id)
      }, 5000)

      return () => clearTimeout(timer)
    }
  }, [achievementQueue, visibleAchievements])

  const handleDismissAchievement = (achievementId: string) => {
    setVisibleAchievements(prev => prev.filter(a => a.id !== achievementId))
    onDismissAchievement(achievementId)
  }

  return (
    <>
      {/* Level Up Notification (Modal) */}
      {levelUpNotification?.show && (
        <LevelUpNotification
          oldLevel={levelUpNotification.oldLevel}
          newLevel={levelUpNotification.newLevel}
          onClose={onDismissLevelUp}
        />
      )}

      {/* Achievement Notifications (Toast) - Temporarily disabled */}
      {/* <div className="fixed top-4 right-4 z-50 space-y-2">
        {visibleAchievements.map((achievement) => (
          <AchievementNotification
            key={achievement.id}
            achievement={achievement}
            onClose={() => handleDismissAchievement(achievement.id)}
          />
        ))}
      </div> */}
    </>
  )
}

// Experience Points Gain Animation Component
interface ExperienceGainProps {
  points: number
  show: boolean
  onComplete: () => void
}

export function ExperienceGainAnimation({ points, show, onComplete }: ExperienceGainProps) {
  useEffect(() => {
    if (show) {
      const timer = setTimeout(onComplete, 2000)
      return () => clearTimeout(timer)
    }
  }, [show, onComplete])

  if (!show) return null

  return (
    <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50">
      <div className="animate-bounce">
        <div className="bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg">
          <div className="text-center">
            <div className="text-2xl font-bold">+{points} XP</div>
            <div className="text-sm opacity-90">Experience Gained!</div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Progress Bar Animation Component
interface ProgressAnimationProps {
  from: number
  to: number
  duration?: number
  className?: string
}

export function ProgressAnimation({ 
  from, 
  to, 
  duration = 1000, 
  className = '' 
}: ProgressAnimationProps) {
  const [currentProgress, setCurrentProgress] = useState(from)

  useEffect(() => {
    const startTime = Date.now()
    const difference = to - from

    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)
      
      // Easing function (ease-out)
      const easeOut = 1 - Math.pow(1 - progress, 3)
      const newValue = from + (difference * easeOut)
      
      setCurrentProgress(newValue)

      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }

    requestAnimationFrame(animate)
  }, [from, to, duration])

  return (
    <div className={`bg-gray-200 rounded-full h-2 ${className}`}>
      <div 
        className="bg-black rounded-full h-2 transition-all duration-300"
        style={{ width: `${Math.min(currentProgress, 100)}%` }}
      />
    </div>
  )
}

// Streak Fire Animation Component
interface StreakAnimationProps {
  days: number
  show: boolean
}

export function StreakAnimation({ days, show }: StreakAnimationProps) {
  if (!show) return null

  return (
    <div className="inline-flex items-center space-x-1 animate-pulse">
      <span className="text-orange-500 animate-bounce">🔥</span>
      <span className="font-bold text-orange-600">{days} day streak!</span>
    </div>
  )
}

// Floating Action Feedback Component
interface ActionFeedbackProps {
  message: string
  type: 'success' | 'info' | 'warning'
  show: boolean
  onComplete: () => void
}

export function ActionFeedback({ message, type, show, onComplete }: ActionFeedbackProps) {
  useEffect(() => {
    if (show) {
      const timer = setTimeout(onComplete, 3000)
      return () => clearTimeout(timer)
    }
  }, [show, onComplete])

  if (!show) return null

  const typeStyles = {
    success: 'bg-green-500 text-white',
    info: 'bg-blue-500 text-white',
    warning: 'bg-yellow-500 text-black'
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className={`px-4 py-2 rounded-lg shadow-lg animate-slide-up ${typeStyles[type]}`}>
        <div className="flex items-center space-x-2">
          <span>{message}</span>
          <button
            onClick={onComplete}
            className="text-current opacity-70 hover:opacity-100"
          >
            <HiOutlineXMark className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  )
}

// Custom CSS for animations (add to globals.css)
export const notificationStyles = `
@keyframes slide-up {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

@keyframes fade-in-scale {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-fade-in-scale {
  animation: fade-in-scale 0.3s ease-out;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.6);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}
`
