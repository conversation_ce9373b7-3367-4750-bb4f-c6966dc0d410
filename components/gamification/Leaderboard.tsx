import { getLeaderboard, getUserRank } from '@/lib/api/gamification'
import { useAuth } from '@/lib/auth'
import { useLeaderboard } from '@/lib/hooks/useGamificationRealtime'
import { LeaderboardEntry } from '@/types'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { HiOutlineArrowPath, HiOutlineTrophy } from 'react-icons/hi2'
import { CompactRankBadge } from './RankBadge'

interface LeaderboardProps {
  limit?: number
  showCurrentUser?: boolean
  useRealtime?: boolean
  className?: string
}

export function Leaderboard({
  limit = 10,
  showCurrentUser = true,
  useRealtime = true,
  className = ''
}: LeaderboardProps) {
  const { user } = useAuth()
  const [userRank, setUserRank] = useState<number | null>(null)
  const [manualLoading, setManualLoading] = useState(false)

  // Use realtime hook if enabled, otherwise fall back to manual loading
  const realtimeData = useLeaderboard(useRealtime ? limit : 0, user?.id)
  const [manualData, setManualData] = useState<{
    leaderboard: LeaderboardEntry[]
    loading: boolean
    error: string | null
  }>({
    leaderboard: [],
    loading: !useRealtime,
    error: null
  })

  // Choose data source based on useRealtime prop
  const { leaderboard, loading, error } = useRealtime ? realtimeData : manualData

  // Manual loading function for non-realtime mode
  const loadLeaderboard = async () => {
    if (useRealtime) return // Don't manually load if using realtime

    try {
      setManualLoading(true)
      setManualData(prev => ({ ...prev, loading: true, error: null }))

      const data = await getLeaderboard(limit)
      setManualData({
        leaderboard: data,
        loading: false,
        error: null
      })

      if (user && showCurrentUser) {
        const rank = await getUserRank(user.id)
        setUserRank(rank)
      }
    } catch (err) {
      console.error('Error loading leaderboard:', err)
      setManualData({
        leaderboard: [],
        loading: false,
        error: 'Failed to load leaderboard'
      })
    } finally {
      setManualLoading(false)
    }
  }

  // Load data on mount for non-realtime mode
  useEffect(() => {
    if (!useRealtime) {
      loadLeaderboard()
    }
  }, [limit, useRealtime])

  // Load user rank when user changes
  useEffect(() => {
    if (user && showCurrentUser && useRealtime) {
      getUserRank(user.id).then(setUserRank).catch(console.error)
    }
  }, [user, showCurrentUser, useRealtime])

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <span className="text-2xl">🥇</span>
      case 2:
        return <span className="text-2xl">🥈</span>
      case 3:
        return <span className="text-2xl">🥉</span>
      default:
        return (
          <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
            <span className="text-sm font-bold text-gray-600">#{rank}</span>
          </div>
        )
    }
  }

  const formatStudyTime = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    if (hours < 24) return `${hours}h`
    const days = Math.floor(hours / 24)
    return `${days}d`
  }

  if (loading) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/3"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <HiOutlineTrophy className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">{error}</p>
          <button
            onClick={loadLeaderboard}
            className="mt-4 btn-secondary"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <HiOutlineTrophy className="w-6 h-6 text-black" />
          <h2 className="text-xl font-bold text-black">Leaderboard</h2>
          {useRealtime && (
            <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
              Live
            </span>
          )}
        </div>
        {!useRealtime && (
          <button
            onClick={loadLeaderboard}
            disabled={manualLoading}
            className="flex items-center space-x-1 text-gray-600 hover:text-black transition-colors disabled:opacity-50"
          >
            <HiOutlineArrowPath className={`w-4 h-4 ${manualLoading ? 'animate-spin' : ''}`} />
            <span className="text-sm">Refresh</span>
          </button>
        )}
      </div>

      {/* Current User Rank (if not in top list) */}
      {showCurrentUser && user && userRank && userRank > limit && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg border">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Your Rank:</span>
            <div className="flex items-center space-x-2">
              <span className="font-bold text-black">#{userRank}</span>
              <span className="text-sm text-gray-500">of all users</span>
            </div>
          </div>
        </div>
      )}

      {/* Leaderboard List */}
      <div className="space-y-3">
        {leaderboard.map((entry, index) => {
          const isCurrentUser = user?.id === entry.id
          
          return (
            <div
              key={entry.id}
              className={`
                flex items-center space-x-4 p-3 rounded-lg transition-colors
                ${isCurrentUser ? 'bg-black text-white' : 'hover:bg-gray-50'}
              `}
            >
              {/* Rank */}
              <div className="flex-shrink-0">
                {getRankIcon(entry.global_rank)}
              </div>

              {/* User Info */}
              <Link 
                href={`/profile/${entry.username}`}
                className="flex-1 min-w-0"
              >
                <div className="flex items-center space-x-3">
                  {/* Avatar */}
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                    isCurrentUser ? 'bg-white text-black' : 'bg-gray-200 text-gray-600'
                  }`}>
                    {entry.avatar_url ? (
                      <img
                        src={entry.avatar_url}
                        alt={entry.username}
                        className="w-full h-full rounded-full object-cover"
                      />
                    ) : (
                      (entry.display_name?.charAt(0) || entry.username.charAt(0)).toUpperCase()
                    )}
                  </div>

                  {/* Name and Level */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className={`font-medium truncate ${
                        isCurrentUser ? 'text-white' : 'text-black'
                      }`}>
                        {entry.display_name || entry.username}
                      </p>
                      <CompactRankBadge 
                        level={entry.rank_level} 
                        className={isCurrentUser ? 'bg-white text-black' : ''}
                      />
                    </div>
                    <p className={`text-sm truncate ${
                      isCurrentUser ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      @{entry.username}
                    </p>
                  </div>
                </div>
              </Link>

              {/* Stats */}
              <div className="flex items-center space-x-4 text-sm">
                {/* Experience Points */}
                <div className="text-center">
                  <div className={`font-bold ${isCurrentUser ? 'text-white' : 'text-black'}`}>
                    {entry.total_experience_points.toLocaleString()}
                  </div>
                  <div className={`text-xs ${isCurrentUser ? 'text-gray-300' : 'text-gray-500'}`}>
                    XP
                  </div>
                </div>

                {/* Study Time */}
                <div className="text-center">
                  <div className={`font-bold ${isCurrentUser ? 'text-white' : 'text-black'}`}>
                    {formatStudyTime(entry.total_study_time)}
                  </div>
                  <div className={`text-xs ${isCurrentUser ? 'text-gray-300' : 'text-gray-500'}`}>
                    Study
                  </div>
                </div>

                {/* Streak */}
                <div className="text-center">
                  <div className={`font-bold ${isCurrentUser ? 'text-white' : 'text-black'}`}>
                    {entry.current_streak_days}
                  </div>
                  <div className={`text-xs ${isCurrentUser ? 'text-gray-300' : 'text-gray-500'}`}>
                    Streak
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* View More Link */}
      {leaderboard.length >= limit && (
        <div className="mt-6 text-center">
          <Link 
            href="/leaderboard"
            className="text-black hover:text-gray-700 font-medium text-sm"
          >
            View Full Leaderboard →
          </Link>
        </div>
      )}
    </div>
  )
}

// Compact leaderboard for sidebar
interface CompactLeaderboardProps {
  limit?: number
  className?: string
}

export function CompactLeaderboard({ limit = 5, className = '' }: CompactLeaderboardProps) {
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadData = async () => {
      try {
        const data = await getLeaderboard(limit)
        setLeaderboard(data)
      } catch (error) {
        console.error('Error loading compact leaderboard:', error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [limit])

  if (loading) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
        <div className="animate-pulse space-y-3">
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center space-x-2">
              <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
              <div className="h-3 bg-gray-200 rounded flex-1"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
      <h3 className="font-medium text-black mb-3 flex items-center space-x-2">
        <HiOutlineTrophy className="w-4 h-4" />
        <span>Top Learners</span>
      </h3>
      
      <div className="space-y-2">
        {leaderboard.slice(0, 3).map((entry, index) => (
          <Link
            key={entry.id}
            href={`/profile/${entry.username}`}
            className="flex items-center space-x-2 p-2 rounded hover:bg-gray-50 transition-colors"
          >
            <span className="text-lg">
              {index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉'}
            </span>
            <span className="text-sm font-medium text-black truncate">
              {entry.display_name || entry.username}
            </span>
            <span className="text-xs text-gray-500 ml-auto">
              {entry.total_experience_points.toLocaleString()} XP
            </span>
          </Link>
        ))}
      </div>
      
      <Link
        href="/leaderboard"
        className="block text-center text-xs text-gray-500 hover:text-gray-700 mt-3"
      >
        View All →
      </Link>
    </div>
  )
}
