import { getUserLevel, LEVEL_THRESHOLDS } from '../../lib/gamification/rankingSystem'

interface RankBadgeProps {
  level: number
  experiencePoints?: number
  size?: 'sm' | 'md' | 'lg'
  showProgress?: boolean
  className?: string
}

export function RankBadge({
  level, 
  experiencePoints, 
  size = 'md', 
  showProgress = false,
  className = '' 
}: RankBadgeProps) {
  const levelInfo = LEVEL_THRESHOLDS.find(l => l.level === level) || LEVEL_THRESHOLDS[0]
  
  const sizeClasses = {
    sm: 'w-6 h-6 text-xs',
    md: 'w-8 h-8 text-sm',
    lg: 'w-12 h-12 text-base'
  }

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Badge Circle */}
      <div 
        className={`
          ${sizeClasses[size]} 
          rounded-full 
          flex items-center justify-center 
          font-bold text-white
          border-2 border-gray-300
          shadow-sm
        `}
        style={{ backgroundColor: levelInfo.color }}
      >
        {level}
      </div>
      
      {/* Level Name */}
      <div className="flex flex-col">
        <span className={`font-medium text-black ${textSizeClasses[size]}`}>
          {levelInfo.name}
        </span>
        
        {/* Progress Bar (if enabled and experience points provided) */}
        {showProgress && experiencePoints !== undefined && (
          <div className="mt-1">
            <LevelProgressBar 
              experiencePoints={experiencePoints} 
              size={size === 'lg' ? 'md' : 'sm'} 
            />
          </div>
        )}
      </div>
    </div>
  )
}

interface LevelProgressBarProps {
  experiencePoints: number
  size?: 'sm' | 'md'
  showText?: boolean
}

export function LevelProgressBar({ 
  experiencePoints, 
  size = 'md', 
  showText = true 
}: LevelProgressBarProps) {
  const currentLevel = getUserLevel(experiencePoints)
  const nextLevelIndex = LEVEL_THRESHOLDS.findIndex(l => l.level === currentLevel.level) + 1
  const nextLevel = nextLevelIndex < LEVEL_THRESHOLDS.length ? LEVEL_THRESHOLDS[nextLevelIndex] : null
  
  if (!nextLevel) {
    return (
      <div className="flex items-center space-x-2">
        <div className={`flex-1 bg-black rounded-full ${size === 'sm' ? 'h-1' : 'h-2'}`} />
        {showText && (
          <span className="text-xs text-gray-600 font-medium">MAX</span>
        )}
      </div>
    )
  }

  const pointsInCurrentLevel = experiencePoints - currentLevel.minXP
  const pointsNeededForNext = nextLevel.minXP - currentLevel.minXP
  const progress = Math.min((pointsInCurrentLevel / pointsNeededForNext) * 100, 100)
  const pointsToNext = nextLevel.minXP - experiencePoints

  return (
    <div className="space-y-1">
      {/* Progress Bar */}
      <div className="flex items-center space-x-2">
        <div className={`flex-1 bg-gray-200 rounded-full ${size === 'sm' ? 'h-1' : 'h-2'}`}>
          <div 
            className={`bg-black rounded-full transition-all duration-300 ${size === 'sm' ? 'h-1' : 'h-2'}`}
            style={{ width: `${progress}%` }}
          />
        </div>
        {showText && (
          <span className="text-xs text-gray-600 font-medium">
            {pointsToNext} XP
          </span>
        )}
      </div>
      
      {/* Progress Text */}
      {showText && size === 'md' && (
        <div className="flex justify-between text-xs text-gray-500">
          <span>Level {currentLevel.level}</span>
          <span>Level {nextLevel.level}</span>
        </div>
      )}
    </div>
  )
}

// Compact version for cards
interface CompactRankBadgeProps {
  level: number
  className?: string
}

export function CompactRankBadge({ level, className = '' }: CompactRankBadgeProps) {
  const levelInfo = LEVEL_THRESHOLDS.find(l => l.level === level) || LEVEL_THRESHOLDS[0]
  
  return (
    <div 
      className={`
        inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white
        ${className}
      `}
      style={{ backgroundColor: levelInfo.color }}
    >
      <span>Lv.{level}</span>
    </div>
  )
}

// Level up notification component
interface LevelUpNotificationProps {
  oldLevel: number
  newLevel: number
  onClose: () => void
}

export function LevelUpNotification({ oldLevel, newLevel, onClose }: LevelUpNotificationProps) {
  const newLevelInfo = LEVEL_THRESHOLDS.find(l => l.level === newLevel) || LEVEL_THRESHOLDS[0]
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-8 max-w-md mx-4 text-center">
        <div className="mb-6">
          <div className="text-6xl mb-4">🎉</div>
          <h2 className="text-2xl font-bold text-black mb-2">Level Up!</h2>
          <p className="text-gray-600">
            You've reached Level {newLevel}
          </p>
        </div>
        
        <div className="mb-6">
          <div className="flex justify-center">
            <RankBadge level={newLevel} size="lg" />
          </div>
        </div>
        
        <div className="mb-6">
          <p className="text-lg font-medium text-black mb-1">
            {newLevelInfo.name}
          </p>
          <p className="text-gray-600 text-sm">
            Keep up the great work!
          </p>
        </div>
        
        <button
          onClick={onClose}
          className="btn-primary w-full"
        >
          Continue
        </button>
      </div>
    </div>
  )
}
