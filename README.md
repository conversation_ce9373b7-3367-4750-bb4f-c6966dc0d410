# StudyShare

学習記録共有アプリケーション

## プロジェクト構造

```
study-share/
├── app/                    # Next.js App Router
├── components/             # Reactコンポーネント
├── pages/                  # Next.js Pages Router
├── lib/                    # ユーティリティとライブラリ
├── hooks/                  # カスタムReactフック
├── types/                  # TypeScript型定義
├── styles/                 # スタイルファイル
├── supabase/              # Supabaseスキーマとマイグレーション
├── tests/                  # テストファイル
├── scripts/                # ビルドとデプロイスクリプト
├── docs/                   # ドキュメント
├── database/               # データベース関連ファイル
├── test-artifacts/         # テスト結果と画像
├── backups/                # バックアップファイル
└── config/                 # 設定ファイル（予約）
```

## 開発環境

- Next.js 14
- TypeScript
- Tailwind CSS
- Supabase
- Playwright (テスト)

## セットアップ

```bash
npm install
npm run dev
```

## テスト

```bash
# ローカルテスト
npm run test

# ステージング環境テスト
npm run test:staging
```

## デプロイ

Vercelを使用してデプロイされます。
