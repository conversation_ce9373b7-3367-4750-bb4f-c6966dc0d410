// This file configures the initialization of Sentry on the server.
// The config you add here will be used whenever the server handles a request.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import { isDevelopment } from "@/lib/config/environment";
import * as Sentry from "@sentry/nextjs";

// Disable Sentry in development environment
if (isDevelopment()) {
  console.log('🔧 Sentry Server: Disabled in development environment');
} else {
  Sentry.init({
    dsn: "https://<EMAIL>/4509542160596992",

    // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.
    tracesSampleRate: 1,

    // Setting this option to true will print useful information to the console while you're setting up Sentry.
    debug: false,
  });
}
