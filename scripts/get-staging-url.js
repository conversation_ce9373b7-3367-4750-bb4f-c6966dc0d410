#!/usr/bin/env node

/**
 * Vercelから最新のPreview URLを取得するスクリプト
 * 
 * 使用方法:
 * 1. 環境変数STAGING_URLが設定されている場合はそれを使用
 * 2. Vercel CLIを使用して最新のデプロイメントURLを取得
 * 3. フォールバックとして既知の最新URLを使用
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 設定
const FALLBACK_URL = 'https://forple-9huyi0418-daichiumamichis-projects.vercel.app';
const PROJECT_NAME = 'forple'; // Vercelプロジェクト名

/**
 * 最新のVercel Preview URLを取得
 */
async function getLatestStagingURL() {
  console.log('🔍 最新のステージングURLを取得中...');
  
  // 1. 環境変数から取得
  if (process.env.STAGING_URL) {
    console.log(`✅ 環境変数から取得: ${process.env.STAGING_URL}`);
    return process.env.STAGING_URL;
  }
  
  // 2. Vercel CLIを使用して取得
  try {
    console.log('📡 Vercel CLIから最新デプロイメントを取得中...');
    
    // Vercel CLIがインストールされているかチェック
    try {
      execSync('vercel --version', { stdio: 'pipe' });
    } catch (error) {
      console.log('⚠️  Vercel CLIがインストールされていません');
      throw new Error('Vercel CLI not found');
    }
    
    // 最新のデプロイメント一覧を取得（テキスト形式）
    const deploymentsOutput = execSync(
      `vercel ls`,
      { encoding: 'utf8', stdio: 'pipe' }
    );

    // テキスト出力をパースしてURLを抽出
    const lines = deploymentsOutput.split('\n');
    console.log(`📊 デプロイメント出力の行数: ${lines.length}`);

    // Vercel CLIの出力形式に合わせて解析
    // 表形式の出力から最新のReadyなデプロイメントを取得
    const deploymentLines = lines.filter(line =>
      line.includes('https://forple-') &&
      line.includes('Ready') &&
      (line.includes('Preview') || line.includes('Production'))
    );

    if (deploymentLines.length > 0) {
      // 最初の行（最新）からURLを抽出
      const latestLine = deploymentLines[0];
      const urlMatch = latestLine.match(/https:\/\/forple-[a-z0-9]+-daichiumamichis-projects\.vercel\.app/);

      if (urlMatch) {
        const stagingURL = urlMatch[0];
        console.log(`✅ Vercel CLIから取得: ${stagingURL}`);

        // 環境タイプを確認
        const isProduction = latestLine.includes('Production');
        const envType = isProduction ? 'Production' : 'Preview';
        console.log(`📍 環境タイプ: ${envType}`);

        // URLをファイルに保存（他のスクリプトで使用するため）
        saveStagingURL(stagingURL);

        return stagingURL;
      }
    } else {
      // フォールバック: URLのみの行から最新を取得
      const urlOnlyLines = lines.filter(line =>
        line.trim().startsWith('https://forple-') &&
        line.trim().endsWith('.vercel.app')
      );

      if (urlOnlyLines.length > 0) {
        const stagingURL = urlOnlyLines[0].trim();
        console.log(`✅ URLのみから取得: ${stagingURL}`);
        console.log(`📍 環境タイプ: 不明（最新デプロイメント）`);

        // URLをファイルに保存
        saveStagingURL(stagingURL);

        return stagingURL;
      }
    }

    throw new Error('No ready deployments found');
    
  } catch (error) {
    console.log(`⚠️  Vercel CLIからの取得に失敗: ${error.message}`);
  }
  
  // 3. フォールバックURL
  console.log(`🔄 フォールバックURLを使用: ${FALLBACK_URL}`);
  return FALLBACK_URL;
}

/**
 * ステージングURLをファイルに保存
 */
function saveStagingURL(url) {
  const configPath = path.join(__dirname, '..', '.staging-url');
  fs.writeFileSync(configPath, url, 'utf8');
  console.log(`💾 ステージングURLを保存: ${configPath}`);
}

/**
 * 保存されたステージングURLを読み込み
 */
function loadStagingURL() {
  const configPath = path.join(__dirname, '..', '.staging-url');
  try {
    if (fs.existsSync(configPath)) {
      const url = fs.readFileSync(configPath, 'utf8').trim();
      console.log(`📖 保存されたURLを読み込み: ${url}`);
      return url;
    }
  } catch (error) {
    console.log(`⚠️  保存されたURLの読み込みに失敗: ${error.message}`);
  }
  return null;
}

/**
 * メイン実行関数
 */
async function main() {
  try {
    // コマンドライン引数をチェック
    const args = process.argv.slice(2);
    const command = args[0];
    
    if (command === 'load') {
      // 保存されたURLを読み込み
      const savedURL = loadStagingURL();
      if (savedURL) {
        console.log(savedURL);
        return;
      }
    }
    
    // 最新URLを取得
    const stagingURL = await getLatestStagingURL();
    console.log(stagingURL);
    
  } catch (error) {
    console.error(`❌ エラー: ${error.message}`);
    console.log(FALLBACK_URL);
    process.exit(1);
  }
}

// スクリプトが直接実行された場合
if (require.main === module) {
  main();
}

module.exports = {
  getLatestStagingURL,
  saveStagingURL,
  loadStagingURL
};
