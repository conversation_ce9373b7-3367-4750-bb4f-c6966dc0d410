#!/usr/bin/env node

/**
 * Apply comment_count migration to Supabase environments
 * Usage: node scripts/apply-comment-count-migration.js [environment]
 * Environments: local, staging
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const MIGRATION_FILE = '20250625000000_add_comment_count_to_recipes.sql';
const MIGRATION_PATH = path.join(__dirname, '..', 'supabase', 'migrations', MIGRATION_FILE);

// Environment configurations
const ENVIRONMENTS = {
  local: {
    name: 'Local Development',
    command: 'npx supabase db reset',
    description: 'Reset local database with all migrations'
  },
  staging: {
    name: 'Staging Environment',
    command: 'npx supabase db push',
    description: 'Push migrations to staging database'
  }
};

function main() {
  const environment = process.argv[2] || 'local';
  
  if (!ENVIRONMENTS[environment]) {
    console.error('❌ Invalid environment. Use: local or staging');
    console.log('Available environments:');
    Object.keys(ENVIRONMENTS).forEach(env => {
      console.log(`  - ${env}: ${ENVIRONMENTS[env].description}`);
    });
    process.exit(1);
  }

  const config = ENVIRONMENTS[environment];
  
  console.log(`🚀 Applying comment_count migration to ${config.name}`);
  console.log(`📁 Migration file: ${MIGRATION_FILE}`);
  
  // Check if migration file exists
  if (!fs.existsSync(MIGRATION_PATH)) {
    console.error(`❌ Migration file not found: ${MIGRATION_PATH}`);
    process.exit(1);
  }

  console.log(`📋 Migration content preview:`);
  console.log('─'.repeat(50));
  const migrationContent = fs.readFileSync(MIGRATION_PATH, 'utf8');
  const lines = migrationContent.split('\n').slice(0, 10);
  lines.forEach(line => console.log(`  ${line}`));
  if (migrationContent.split('\n').length > 10) {
    console.log('  ... (truncated)');
  }
  console.log('─'.repeat(50));

  // Confirmation prompt for staging
  if (environment === 'staging') {
    console.log('⚠️  WARNING: This will modify the staging database!');
    console.log('📝 Please ensure:');
    console.log('   1. You have tested this migration locally');
    console.log('   2. You have backup of staging data if needed');
    console.log('   3. You have permission to modify staging environment');
    console.log('');
    
    // In a real scenario, you might want to add interactive confirmation
    // For now, we'll just show the warning
  }

  try {
    console.log(`🔄 Executing: ${config.command}`);
    
    if (environment === 'local') {
      // For local, reset the database which will apply all migrations
      console.log('📊 Resetting local database with all migrations...');
      execSync(config.command, { stdio: 'inherit' });
      
      // Verify the migration was applied
      console.log('✅ Verifying migration was applied...');
      execSync('npx supabase db diff --schema public', { stdio: 'inherit' });
      
    } else if (environment === 'staging') {
      // For staging, push the new migration
      console.log('📤 Pushing migrations to staging...');
      execSync(config.command, { stdio: 'inherit' });
    }
    
    console.log(`✅ Migration applied successfully to ${config.name}!`);
    console.log('');
    console.log('📋 Next steps:');
    console.log('   1. Test the affected functionality:');
    console.log('      - Profile page Upvoted Recipes');
    console.log('      - Profile page Bookmarked Recipes');
    console.log('      - Comment count display in UI');
    console.log('   2. Monitor Sentry for the resolved issues:');
    console.log('      - Issue 6705752713 (upvoted recipes)');
    console.log('      - Issue 6705752241 (bookmarked recipes)');
    console.log('   3. Verify comment count updates when adding/removing comments');
    
  } catch (error) {
    console.error(`❌ Error applying migration to ${config.name}:`);
    console.error(error.message);
    
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('   1. Check Supabase CLI is installed and authenticated');
    console.log('   2. Verify database connection');
    console.log('   3. Check migration file syntax');
    console.log('   4. Review Supabase logs for detailed error information');
    
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, ENVIRONMENTS };
