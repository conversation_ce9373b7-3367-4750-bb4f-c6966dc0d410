const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:')
  console.error('- NEXT_PUBLIC_SUPABASE_URL')
  console.error('- SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function applyMigration() {
  try {
    console.log('🔄 Starting material_id migration...')
    
    // Read migration file
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20250630000000_add_material_id_to_study_logs.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    
    console.log('📄 Migration SQL:')
    console.log(migrationSQL)
    console.log('')
    
    // Execute migration
    console.log('⚡ Executing migration...')
    const { data, error } = await supabase.rpc('exec_sql', { sql: migrationSQL })
    
    if (error) {
      console.error('❌ Migration failed:', error)
      process.exit(1)
    }
    
    console.log('✅ Migration executed successfully!')
    
    // Verify the migration
    console.log('🔍 Verifying migration...')
    
    // Check if material_id column exists
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', 'study_logs')
      .eq('column_name', 'material_id')
    
    if (columnsError) {
      console.error('❌ Verification failed:', columnsError)
      process.exit(1)
    }
    
    if (columns && columns.length > 0) {
      console.log('✅ material_id column verified:', columns[0])
    } else {
      console.error('❌ material_id column not found')
      process.exit(1)
    }
    
    // Check if index exists
    const { data: indexes, error: indexError } = await supabase
      .from('pg_indexes')
      .select('indexname, indexdef')
      .eq('tablename', 'study_logs')
      .like('indexname', '%material_id%')
    
    if (indexError) {
      console.warn('⚠️ Could not verify index:', indexError)
    } else if (indexes && indexes.length > 0) {
      console.log('✅ Index verified:', indexes[0].indexname)
    }
    
    console.log('')
    console.log('🎉 Migration completed successfully!')
    console.log('📝 The study_logs table now has a material_id column.')
    console.log('🔗 Foreign key constraint to study_materials table is active.')
    
  } catch (error) {
    console.error('❌ Unexpected error:', error)
    process.exit(1)
  }
}

// Alternative method using direct SQL execution
async function applyMigrationDirect() {
  try {
    console.log('🔄 Starting material_id migration (direct method)...')
    
    // Add material_id column
    console.log('⚡ Adding material_id column...')
    const { error: columnError } = await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE public.study_logs 
        ADD COLUMN IF NOT EXISTS material_id UUID REFERENCES public.study_materials(id) ON DELETE SET NULL;
      `
    })
    
    if (columnError) {
      console.error('❌ Failed to add column:', columnError)
      process.exit(1)
    }
    
    console.log('✅ material_id column added successfully!')
    
    // Add index
    console.log('⚡ Adding index...')
    const { error: indexError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE INDEX IF NOT EXISTS idx_study_logs_material_id ON public.study_logs(material_id);
      `
    })
    
    if (indexError) {
      console.error('❌ Failed to add index:', indexError)
      process.exit(1)
    }
    
    console.log('✅ Index added successfully!')
    
    // Add comment
    console.log('⚡ Adding column comment...')
    const { error: commentError } = await supabase.rpc('exec_sql', {
      sql: `
        COMMENT ON COLUMN public.study_logs.material_id IS 'References the specific study material that was studied in this session';
      `
    })
    
    if (commentError) {
      console.warn('⚠️ Failed to add comment:', commentError)
    } else {
      console.log('✅ Column comment added successfully!')
    }
    
    console.log('')
    console.log('🎉 Migration completed successfully!')
    
  } catch (error) {
    console.error('❌ Unexpected error:', error)
    process.exit(1)
  }
}

// Check if exec_sql function exists, if not use direct method
async function main() {
  try {
    // Test if exec_sql function exists
    const { error: testError } = await supabase.rpc('exec_sql', { sql: 'SELECT 1;' })
    
    if (testError && testError.message.includes('function exec_sql')) {
      console.log('📝 exec_sql function not available, using direct method...')
      await applyMigrationDirect()
    } else {
      console.log('📝 Using exec_sql function...')
      await applyMigration()
    }
  } catch (error) {
    console.log('📝 Falling back to direct method...')
    await applyMigrationDirect()
  }
}

main()
