-- Initialize achievements data for StudyShare

-- Clear existing achievements
DELETE FROM public.achievements;

-- Insert predefined achievements
INSERT INTO public.achievements (name, description, icon, category, requirement_type, requirement_value, points_reward) VALUES
-- Study Achievements
('First Steps', 'Complete your first study session', '🎯', 'study', 'study_sessions', 1, 50),
('Getting Started', 'Complete 10 study sessions', '📚', 'study', 'study_sessions', 10, 100),
('Dedicated Learner', 'Complete 50 study sessions', '🎓', 'study', 'study_sessions', 50, 300),
('Study Master', 'Complete 100 study sessions', '👑', 'study', 'study_sessions', 100, 500),
('Marathon Learner', 'Complete a 3-hour study session', '⏰', 'study', 'single_session_minutes', 180, 200),
('Time Investor', 'Study for a total of 10 hours', '⏱️', 'study', 'total_study_time', 600, 150),
('Committed Scholar', 'Study for a total of 100 hours', '🔥', 'study', 'total_study_time', 6000, 800),

-- Consistency Achievements
('Building Habits', 'Study for 3 consecutive days', '🌱', 'consistency', 'current_streak', 3, 75),
('Week Warrior', 'Study for 7 consecutive days', '🗓️', 'consistency', 'current_streak', 7, 200),
('Monthly Master', 'Study for 30 consecutive days', '📅', 'consistency', 'current_streak', 30, 1000),
('Consistency Legend', 'Study for 100 consecutive days', '🏆', 'consistency', 'current_streak', 100, 2500),

-- Community Achievements
('Recipe Creator', 'Create your first study recipe', '📝', 'community', 'recipes_created', 1, 100),
('Content Creator', 'Create 5 study recipes', '✍️', 'community', 'recipes_created', 5, 300),
('Community Approved', 'Receive your first upvote', '👍', 'community', 'upvotes_received', 1, 50),
('Popular Creator', 'Get 10 upvotes on a single recipe', '⭐', 'community', 'single_recipe_upvotes', 10, 250),
('Viral Creator', 'Get 50 upvotes on a single recipe', '🌟', 'community', 'single_recipe_upvotes', 50, 1000),
('Helpful Community Member', 'Make 25 comments', '💬', 'community', 'comments_made', 25, 200),
('Knowledge Collector', 'Bookmark 20 recipes', '🔖', 'community', 'bookmarks_made', 20, 150),

-- Milestone Achievements
('Rising Star', 'Reach Level 5', '🌟', 'milestone', 'level', 5, 500),
('StudyShare Legend', 'Reach the maximum level', '👑', 'milestone', 'level', 10, 2000),
('Elite Member', 'Reach top 10 on the leaderboard', '🏅', 'milestone', 'leaderboard_rank', 10, 1500);

-- Initialize levels data
DELETE FROM public.levels;

INSERT INTO public.levels (level, name, min_experience_points, max_experience_points, badge_color) VALUES
(1, 'Beginner', 0, 99, '#6B7280'),
(2, 'Learner', 100, 299, '#6B7280'),
(3, 'Student', 300, 599, '#6B7280'),
(4, 'Scholar', 600, 999, '#374151'),
(5, 'Dedicated', 1000, 1599, '#374151'),
(6, 'Committed', 1600, 2499, '#374151'),
(7, 'Expert', 2500, 3999, '#1F2937'),
(8, 'Master', 4000, 6499, '#1F2937'),
(9, 'Guru', 6500, 9999, '#111827'),
(10, 'Legend', 10000, NULL, '#000000');
