-- Fix Comments Table Cascade Delete for Staging Environment
-- Run this script to fix the cascade delete issue in comments table

-- First, drop the existing foreign key constraint for parent_id
ALTER TABLE public.comments 
DROP CONSTRAINT IF EXISTS comments_parent_id_fkey;

-- Add the foreign key constraint with CASCADE DELETE
ALTER TABLE public.comments 
ADD CONSTRAINT comments_parent_id_fkey 
FOREIGN KEY (parent_id) 
REFERENCES public.comments(id) 
ON DELETE CASCADE;

-- Also ensure other foreign keys have proper cascade behavior
-- Drop and recreate recipe_id constraint with CASCADE
ALTER TABLE public.comments 
DROP CONSTRAINT IF EXISTS comments_recipe_id_fkey;

ALTER TABLE public.comments 
ADD CONSTRAINT comments_recipe_id_fkey 
FOREIGN KEY (recipe_id) 
REFERENCES public.study_recipes(id) 
ON DELETE CASCADE;

-- Drop and recreate log_id constraint with CASCADE
ALTER TABLE public.comments 
DROP CONSTRAINT IF EXISTS comments_log_id_fkey;

ALTER TABLE public.comments 
ADD CONSTRAINT comments_log_id_fkey 
FOREIGN KEY (log_id) 
REFERENCES public.study_logs(id) 
ON DELETE CASCADE;

-- Drop and recreate user_id constraint with CASCADE
ALTER TABLE public.comments 
DROP CONSTRAINT IF EXISTS comments_user_id_fkey;

ALTER TABLE public.comments 
ADD CONSTRAINT comments_user_id_fkey 
FOREIGN KEY (user_id) 
REFERENCES public.profiles(id) 
ON DELETE CASCADE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_comments_parent_id ON public.comments(parent_id);
CREATE INDEX IF NOT EXISTS idx_comments_recipe_id ON public.comments(recipe_id);
CREATE INDEX IF NOT EXISTS idx_comments_log_id ON public.comments(log_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON public.comments(user_id);

-- Verify the constraints
SELECT 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
    JOIN information_schema.referential_constraints AS rc
      ON tc.constraint_name = rc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_name = 'comments'
  AND tc.table_schema = 'public';
