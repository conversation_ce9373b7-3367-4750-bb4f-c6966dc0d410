#!/usr/bin/env node

/**
 * ステージング環境のデータベース状態確認スクリプト
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// ステージング環境の設定を読み込み
const envPath = path.join(__dirname, '..', '.env.staging');
if (!fs.existsSync(envPath)) {
  console.error('❌ .env.staging file not found. Run: vercel env pull .env.staging --environment=preview');
  process.exit(1);
}

// 環境変数を読み込み
require('dotenv').config({ path: envPath });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env.staging');
  process.exit(1);
}

console.log('🔍 Checking Staging Database Status');
console.log('=====================================');
console.log(`📍 Supabase URL: ${supabaseUrl}`);
console.log(`🔑 Service Key: ${supabaseServiceKey.substring(0, 20)}...`);

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function checkDatabase() {
  try {
    console.log('\n1. 📊 Checking database connection...');
    
    // Test basic connection
    const { data: connectionTest, error: connectionError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (connectionError) {
      console.error('❌ Database connection failed:', connectionError.message);
      return;
    }
    
    console.log('✅ Database connection successful');

    console.log('\n2. 📋 Checking table existence...');
    
    // Check if required tables exist
    const tables = ['profiles', 'study_recipes', 'study_logs', 'comments', 'user_stats'];
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('count')
          .limit(1);
        
        if (error) {
          console.log(`❌ Table '${table}': ${error.message}`);
        } else {
          console.log(`✅ Table '${table}': exists`);
        }
      } catch (err) {
        console.log(`❌ Table '${table}': ${err.message}`);
      }
    }

    console.log('\n3. 👥 Checking user data...');
    
    // Check profiles
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, username, display_name')
      .limit(5);
    
    if (profilesError) {
      console.log('❌ Profiles query failed:', profilesError.message);
    } else {
      console.log(`✅ Found ${profiles.length} profiles:`);
      profiles.forEach(profile => {
        console.log(`   - ${profile.username} (${profile.display_name || 'No display name'})`);
      });
    }

    console.log('\n4. 📚 Checking recipe data...');
    
    // Check study recipes
    const { data: recipes, error: recipesError } = await supabase
      .from('study_recipes')
      .select('id, title, user_id')
      .limit(5);
    
    if (recipesError) {
      console.log('❌ Recipes query failed:', recipesError.message);
    } else {
      console.log(`✅ Found ${recipes.length} recipes:`);
      recipes.forEach(recipe => {
        console.log(`   - ${recipe.title} (ID: ${recipe.id})`);
      });
    }

    console.log('\n5. 💬 Checking comment data...');
    
    // Check comments
    const { data: comments, error: commentsError } = await supabase
      .from('comments')
      .select('id, content, user_id, recipe_id, log_id')
      .limit(5);
    
    if (commentsError) {
      console.log('❌ Comments query failed:', commentsError.message);
    } else {
      console.log(`✅ Found ${comments.length} comments:`);
      comments.forEach(comment => {
        const target = comment.recipe_id ? `recipe:${comment.recipe_id}` : `log:${comment.log_id}`;
        console.log(`   - "${comment.content.substring(0, 50)}..." on ${target}`);
      });
    }

    console.log('\n6. 🔐 Checking RLS policies...');
    
    // Check RLS status (this requires admin access)
    try {
      const { data: rlsStatus } = await supabase
        .rpc('check_rls_status');
      
      console.log('✅ RLS policies checked');
    } catch (err) {
      console.log('⚠️  Could not check RLS policies (may require custom function)');
    }

    console.log('\n7. 🧪 Testing comment insertion...');
    
    // Test comment insertion with test user
    const testUserId = profiles.length > 0 ? profiles[0].id : null;
    const testRecipeId = recipes.length > 0 ? recipes[0].id : null;
    
    if (testUserId && testRecipeId) {
      const { data: testComment, error: insertError } = await supabase
        .from('comments')
        .insert([{
          user_id: testUserId,
          recipe_id: testRecipeId,
          content: 'Test comment from staging check script',
          mentions: []
        }])
        .select()
        .single();
      
      if (insertError) {
        console.log('❌ Comment insertion failed:', insertError.message);
        console.log('   Error details:', insertError);
      } else {
        console.log('✅ Comment insertion successful:', testComment.id);
        
        // Clean up test comment
        await supabase
          .from('comments')
          .delete()
          .eq('id', testComment.id);
        
        console.log('✅ Test comment cleaned up');
      }
    } else {
      console.log('⚠️  Cannot test comment insertion: missing test data');
    }

  } catch (error) {
    console.error('❌ Database check failed:', error);
  }
}

checkDatabase().then(() => {
  console.log('\n🏁 Database check completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
