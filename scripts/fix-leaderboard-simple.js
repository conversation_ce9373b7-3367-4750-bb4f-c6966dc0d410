const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function fixLeaderboardSimple() {
  console.log('🔧 Starting simple leaderboard permissions fix...');
  
  try {
    // First, let's try to understand the current state
    console.log('🔍 Checking current leaderboard state...');
    
    // Test current refresh function
    console.log('🔄 Testing current refresh_leaderboard function...');
    const { data: refreshResult, error: refreshError } = await supabase.rpc('refresh_leaderboard');
    
    if (refreshError) {
      console.error('❌ Current refresh error (expected):', refreshError);
      
      if (refreshError.code === '42501' && refreshError.message.includes('must be owner of materialized view')) {
        console.log('✅ Confirmed: This is the ownership issue we need to fix');
        
        // The issue is that we need to recreate the materialized view with proper ownership
        // Since we can't use exec_sql, we need to provide manual instructions
        
        console.log('\n📋 MANUAL FIX REQUIRED:');
        console.log('Please execute the following SQL in Supabase SQL Editor:');
        console.log('=====================================');
        console.log('-- Step 1: Drop existing materialized view');
        console.log('DROP MATERIALIZED VIEW IF EXISTS public.leaderboard;');
        console.log('');
        console.log('-- Step 2: Recreate materialized view');
        console.log(`CREATE MATERIALIZED VIEW public.leaderboard AS
SELECT
  p.id,
  p.username,
  p.display_name,
  p.avatar_url,
  p.rank_level,
  p.total_study_time,
  COALESCE(us.total_experience_points, 0) as total_experience_points,
  COALESCE(us.current_streak_days, 0) as current_streak_days,
  COALESCE(us.total_study_sessions, 0) as total_study_sessions,
  COALESCE(us.recipes_created, 0) as recipes_created,
  COALESCE(us.total_upvotes_received, 0) as total_upvotes_received,
  RANK() OVER (ORDER BY COALESCE(us.total_experience_points, 0) DESC) as global_rank
FROM public.profiles p
LEFT JOIN public.user_stats us ON p.id = us.user_id
ORDER BY COALESCE(us.total_experience_points, 0) DESC;`);
        console.log('');
        console.log('-- Step 3: Grant permissions');
        console.log('GRANT SELECT ON public.leaderboard TO authenticated;');
        console.log('GRANT SELECT ON public.leaderboard TO anon;');
        console.log('');
        console.log('-- Step 4: Recreate function with SECURITY DEFINER');
        console.log(`CREATE OR REPLACE FUNCTION public.refresh_leaderboard()
RETURNS void 
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  REFRESH MATERIALIZED VIEW public.leaderboard;
END;
$$ LANGUAGE plpgsql;`);
        console.log('');
        console.log('-- Step 5: Grant function permissions');
        console.log('GRANT EXECUTE ON FUNCTION public.refresh_leaderboard() TO authenticated;');
        console.log('GRANT EXECUTE ON FUNCTION public.refresh_leaderboard() TO anon;');
        console.log('');
        console.log('-- Step 6: Test the function');
        console.log('SELECT public.refresh_leaderboard();');
        console.log('=====================================');
        
        return false; // Indicates manual fix needed
      }
    } else {
      console.log('✅ Refresh function works correctly - no fix needed!');
      return true;
    }
    
    // Test leaderboard query
    console.log('📋 Testing leaderboard query...');
    const { data: leaderboard, error: queryError } = await supabase
      .from('leaderboard')
      .select('*')
      .limit(5);
    
    if (queryError) {
      console.error('❌ Leaderboard query failed:', queryError);
    } else {
      console.log(`✅ Leaderboard query successful, found ${leaderboard?.length || 0} entries`);
    }
    
  } catch (error) {
    console.error('❌ Failed to check leaderboard status:', error);
    return false;
  }
}

// Alternative: Try to create a workaround function
async function createWorkaroundFunction() {
  console.log('🔄 Creating workaround refresh function...');
  
  try {
    // Create a function that handles the permission issue gracefully
    const workaroundFunction = `
      CREATE OR REPLACE FUNCTION public.refresh_leaderboard_safe()
      RETURNS void 
      SECURITY DEFINER
      SET search_path = public
      AS $$
      BEGIN
        -- Try to refresh, but handle permission errors gracefully
        BEGIN
          REFRESH MATERIALIZED VIEW public.leaderboard;
        EXCEPTION 
          WHEN insufficient_privilege THEN
            -- Log the error but don't fail
            RAISE NOTICE 'Cannot refresh leaderboard: insufficient privileges';
          WHEN OTHERS THEN
            -- Log other errors but don't fail
            RAISE NOTICE 'Cannot refresh leaderboard: %', SQLERRM;
        END;
      END;
      $$ LANGUAGE plpgsql;
    `;
    
    console.log('📝 Workaround function SQL:');
    console.log(workaroundFunction);
    
    console.log('\n⚠️  Please execute this SQL in Supabase SQL Editor to create a safe workaround function.');
    
  } catch (error) {
    console.error('❌ Failed to create workaround function:', error);
  }
}

async function main() {
  const fixSuccessful = await fixLeaderboardSimple();
  
  if (!fixSuccessful) {
    console.log('\n🔧 Creating workaround function...');
    await createWorkaroundFunction();
  }
  
  console.log('\n📋 NEXT STEPS:');
  console.log('1. Execute the SQL commands shown above in Supabase SQL Editor');
  console.log('2. Run this script again to verify the fix');
  console.log('3. Check Sentry to confirm the issues are resolved');
}

main();
