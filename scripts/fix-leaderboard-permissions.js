const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function fixLeaderboardPermissions() {
  console.log('🔧 Starting leaderboard permissions fix...');
  
  try {
    // Read the SQL script
    const sqlPath = path.join(__dirname, '..', 'database', 'fix-leaderboard-permissions.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');
    
    console.log('📄 SQL script loaded');
    
    // Split SQL into individual statements (simple approach)
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      // Skip comments and empty statements
      if (statement.startsWith('--') || statement.trim().length === 0) {
        continue;
      }
      
      console.log(`🔄 Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        const { data, error } = await supabase.rpc('exec_sql', { 
          sql_query: statement + ';' 
        });
        
        if (error) {
          console.error(`❌ Error in statement ${i + 1}:`, error);
          // Continue with other statements
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`);
        }
      } catch (execError) {
        console.error(`❌ Exception in statement ${i + 1}:`, execError);
        // Continue with other statements
      }
    }
    
    // Test the fix
    console.log('\n🧪 Testing the fix...');
    
    // Test refresh function
    console.log('🔄 Testing refresh_leaderboard function...');
    const { data: refreshResult, error: refreshError } = await supabase.rpc('refresh_leaderboard');
    
    if (refreshError) {
      console.error('❌ Refresh test failed:', refreshError);
    } else {
      console.log('✅ Refresh function works correctly');
    }
    
    // Test leaderboard query
    console.log('📋 Testing leaderboard query...');
    const { data: leaderboard, error: queryError } = await supabase
      .from('leaderboard')
      .select('*')
      .limit(5);
    
    if (queryError) {
      console.error('❌ Leaderboard query failed:', queryError);
    } else {
      console.log(`✅ Leaderboard query successful, found ${leaderboard?.length || 0} entries`);
      if (leaderboard && leaderboard.length > 0) {
        console.log('📊 Sample leaderboard entry:', {
          username: leaderboard[0].username,
          experience: leaderboard[0].total_experience_points,
          rank: leaderboard[0].global_rank
        });
      }
    }
    
    console.log('\n🎉 Leaderboard permissions fix completed!');
    
  } catch (error) {
    console.error('❌ Failed to fix leaderboard permissions:', error);
    process.exit(1);
  }
}

// Alternative approach: Execute SQL directly using raw SQL
async function fixLeaderboardPermissionsDirectSQL() {
  console.log('🔧 Starting leaderboard permissions fix (Direct SQL approach)...');
  
  try {
    // Drop and recreate materialized view
    console.log('🗑️  Dropping existing materialized view...');
    const { error: dropError } = await supabase.rpc('exec_sql', {
      sql_query: 'DROP MATERIALIZED VIEW IF EXISTS public.leaderboard;'
    });
    
    if (dropError && !dropError.message.includes('does not exist')) {
      console.error('❌ Error dropping materialized view:', dropError);
    } else {
      console.log('✅ Materialized view dropped (or did not exist)');
    }
    
    // Create materialized view
    console.log('🏗️  Creating materialized view...');
    const createViewSQL = `
      CREATE MATERIALIZED VIEW public.leaderboard AS
      SELECT
        p.id,
        p.username,
        p.display_name,
        p.avatar_url,
        p.rank_level,
        p.total_study_time,
        COALESCE(us.total_experience_points, 0) as total_experience_points,
        COALESCE(us.current_streak_days, 0) as current_streak_days,
        COALESCE(us.total_study_sessions, 0) as total_study_sessions,
        COALESCE(us.recipes_created, 0) as recipes_created,
        COALESCE(us.total_upvotes_received, 0) as total_upvotes_received,
        RANK() OVER (ORDER BY COALESCE(us.total_experience_points, 0) DESC) as global_rank
      FROM public.profiles p
      LEFT JOIN public.user_stats us ON p.id = us.user_id
      ORDER BY COALESCE(us.total_experience_points, 0) DESC;
    `;
    
    const { error: createError } = await supabase.rpc('exec_sql', {
      sql_query: createViewSQL
    });
    
    if (createError) {
      console.error('❌ Error creating materialized view:', createError);
      throw createError;
    } else {
      console.log('✅ Materialized view created successfully');
    }
    
    // Grant permissions
    console.log('🔐 Granting permissions...');
    const grantSQL = `
      GRANT SELECT ON public.leaderboard TO authenticated;
      GRANT SELECT ON public.leaderboard TO anon;
    `;
    
    const { error: grantError } = await supabase.rpc('exec_sql', {
      sql_query: grantSQL
    });
    
    if (grantError) {
      console.error('❌ Error granting permissions:', grantError);
    } else {
      console.log('✅ Permissions granted successfully');
    }
    
    // Recreate function with SECURITY DEFINER
    console.log('⚙️  Creating refresh function...');
    const functionSQL = `
      CREATE OR REPLACE FUNCTION public.refresh_leaderboard()
      RETURNS void 
      SECURITY DEFINER
      SET search_path = public
      AS $$
      BEGIN
        REFRESH MATERIALIZED VIEW public.leaderboard;
      END;
      $$ LANGUAGE plpgsql;
    `;
    
    const { error: functionError } = await supabase.rpc('exec_sql', {
      sql_query: functionSQL
    });
    
    if (functionError) {
      console.error('❌ Error creating function:', functionError);
    } else {
      console.log('✅ Refresh function created successfully');
    }
    
    // Grant function permissions
    console.log('🔐 Granting function permissions...');
    const functionGrantSQL = `
      GRANT EXECUTE ON FUNCTION public.refresh_leaderboard() TO authenticated;
      GRANT EXECUTE ON FUNCTION public.refresh_leaderboard() TO anon;
    `;
    
    const { error: functionGrantError } = await supabase.rpc('exec_sql', {
      sql_query: functionGrantSQL
    });
    
    if (functionGrantError) {
      console.error('❌ Error granting function permissions:', functionGrantError);
    } else {
      console.log('✅ Function permissions granted successfully');
    }
    
    // Test the fix
    console.log('\n🧪 Testing the fix...');
    
    // Test refresh function
    console.log('🔄 Testing refresh_leaderboard function...');
    const { data: refreshResult, error: refreshError } = await supabase.rpc('refresh_leaderboard');
    
    if (refreshError) {
      console.error('❌ Refresh test failed:', refreshError);
    } else {
      console.log('✅ Refresh function works correctly');
    }
    
    // Test leaderboard query
    console.log('📋 Testing leaderboard query...');
    const { data: leaderboard, error: queryError } = await supabase
      .from('leaderboard')
      .select('*')
      .limit(5);
    
    if (queryError) {
      console.error('❌ Leaderboard query failed:', queryError);
    } else {
      console.log(`✅ Leaderboard query successful, found ${leaderboard?.length || 0} entries`);
      if (leaderboard && leaderboard.length > 0) {
        console.log('📊 Sample leaderboard entry:', {
          username: leaderboard[0].username,
          experience: leaderboard[0].total_experience_points,
          rank: leaderboard[0].global_rank
        });
      }
    }
    
    console.log('\n🎉 Leaderboard permissions fix completed successfully!');
    
  } catch (error) {
    console.error('❌ Failed to fix leaderboard permissions:', error);
    process.exit(1);
  }
}

// Check if exec_sql function exists, if not use direct SQL approach
async function main() {
  try {
    // Test if we can use exec_sql function
    const { error: testError } = await supabase.rpc('exec_sql', {
      sql_query: 'SELECT 1;'
    });
    
    if (testError) {
      console.log('⚠️  exec_sql function not available, using direct SQL approach...');
      await fixLeaderboardPermissionsDirectSQL();
    } else {
      console.log('✅ exec_sql function available, using SQL script approach...');
      await fixLeaderboardPermissions();
    }
  } catch (error) {
    console.log('⚠️  Falling back to direct SQL approach...');
    await fixLeaderboardPermissionsDirectSQL();
  }
}

main();
