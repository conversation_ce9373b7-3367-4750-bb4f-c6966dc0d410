#!/bin/bash

# StudyShare実際のルートチェックスクリプト
echo "🔍 Checking actual accessible routes for StudyShare..."

BASE_URL="https://forple-6vyqnpaet-daichiumamichis-projects.vercel.app"

# テストするパスのリスト
PATHS=(
    "/"
    "/signin"
    "/signup"
    "/profile"
    "/settings"
    "/dashboard"
    "/recipes"
    "/logs"
    "/users"
    "/search"
    "/notifications"
    "/help"
    "/about"
    "/privacy"
    "/terms"
    "/contact"
    "/explore"
    "/trending"
    "/bookmarks"
    "/following"
    "/followers"
    "/user/1"
    "/user/umamichi"
    "/recipe/1"
    "/log/1"
    "/api/auth"
    "/api/users"
    "/api/recipes"
    "/api/logs"
    "/404"
    "/nonexistent-page"
)

echo "📋 Testing ${#PATHS[@]} paths..."
echo ""

# 結果を格納する配列
ACCESSIBLE_PUBLIC=()
ACCESSIBLE_AUTH=()
REQUIRES_AUTH=()
NOT_FOUND=()
ERRORS=()

# 各パスをテスト
for path in "${PATHS[@]}"; do
    url="${BASE_URL}${path}"
    echo "🔍 Testing: $path"
    
    # curlでHTTPステータスコードを取得
    status=$(curl -s -o /dev/null -w "%{http_code}" -L "$url" 2>/dev/null)
    
    # リダイレクト先も確認
    redirect_url=$(curl -s -o /dev/null -w "%{redirect_url}" -L "$url" 2>/dev/null)
    
    case $status in
        200)
            if [[ "$redirect_url" == *"/signin"* ]] || [[ "$redirect_url" == *"/login"* ]]; then
                echo "   🔒 $path - Requires authentication (redirected to signin)"
                REQUIRES_AUTH+=("$path")
            else
                echo "   ✅ $path - Accessible (200)"
                ACCESSIBLE_PUBLIC+=("$path")
            fi
            ;;
        201|202|204)
            echo "   ✅ $path - Accessible ($status)"
            ACCESSIBLE_PUBLIC+=("$path")
            ;;
        301|302|303|307|308)
            if [[ "$redirect_url" == *"/signin"* ]] || [[ "$redirect_url" == *"/login"* ]]; then
                echo "   🔒 $path - Requires authentication (redirect $status)"
                REQUIRES_AUTH+=("$path")
            else
                echo "   ↗️ $path - Redirects ($status) to $redirect_url"
                ACCESSIBLE_PUBLIC+=("$path")
            fi
            ;;
        401|403)
            echo "   🔒 $path - Requires authentication ($status)"
            REQUIRES_AUTH+=("$path")
            ;;
        404)
            echo "   ❌ $path - Not found (404)"
            NOT_FOUND+=("$path")
            ;;
        500|502|503|504)
            echo "   🚨 $path - Server error ($status)"
            ERRORS+=("$path")
            ;;
        *)
            echo "   ❓ $path - Unknown status ($status)"
            ERRORS+=("$path")
            ;;
    esac
    
    # 少し待機
    sleep 0.5
done

echo ""
echo "📊 === RESULTS SUMMARY ==="
echo ""

echo "🔓 ACCESSIBLE PUBLIC PATHS (${#ACCESSIBLE_PUBLIC[@]}):"
for path in "${ACCESSIBLE_PUBLIC[@]}"; do
    echo "✅ $path"
done

echo ""
echo "🔒 REQUIRES AUTHENTICATION (${#REQUIRES_AUTH[@]}):"
for path in "${REQUIRES_AUTH[@]}"; do
    echo "🔒 $path"
done

echo ""
echo "❌ NOT FOUND (${#NOT_FOUND[@]}):"
for path in "${NOT_FOUND[@]}"; do
    echo "❌ $path"
done

echo ""
echo "🚨 ERRORS (${#ERRORS[@]}):"
for path in "${ERRORS[@]}"; do
    echo "🚨 $path"
done

echo ""
echo "📈 STATISTICS:"
echo "📊 Total paths tested: ${#PATHS[@]}"
echo "✅ Accessible public: ${#ACCESSIBLE_PUBLIC[@]}"
echo "🔒 Requires authentication: ${#REQUIRES_AUTH[@]}"
echo "❌ Not found: ${#NOT_FOUND[@]}"
echo "🚨 Errors: ${#ERRORS[@]}"

echo ""
echo "📋 ACTUAL ACCESSIBLE PATHS LIST:"
echo "================================"

count=1
for path in "${ACCESSIBLE_PUBLIC[@]}"; do
    echo "$count. $path [PUBLIC]"
    ((count++))
done

for path in "${REQUIRES_AUTH[@]}"; do
    echo "$count. $path [AUTH REQUIRED]"
    ((count++))
done

echo ""
echo "✅ Route check completed!"
