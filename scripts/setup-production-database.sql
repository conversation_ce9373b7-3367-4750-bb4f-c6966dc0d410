-- StudyShare Production Database Setup
-- Run this script in Supabase SQL Editor to set up the production database

-- Create profiles table (extends auth.users)
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  username TEXT UNIQUE NOT NULL,
  display_name TEXT,
  bio TEXT,
  avatar_url TEXT,
  rank_level INTEGER DEFAULT 1,
  total_study_time INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Study Recipes Table
CREATE TABLE IF NOT EXISTS public.study_recipes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) NOT NULL,
  title TEXT NOT NULL,
  estimated_duration TEXT,
  tips TEXT,
  tags TEXT[] DEFAULT '{}',
  upvote_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Study Materials Table
CREATE TABLE IF NOT EXISTS public.study_materials (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  recipe_id UUID REFERENCES public.study_recipes(id) ON DELETE CASCADE,
  material_name TEXT NOT NULL,
  frequency TEXT NOT NULL,
  time_per_session INTEGER NOT NULL,
  time_unit TEXT DEFAULT 'minutes',
  order_index INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Study Logs Table
CREATE TABLE IF NOT EXISTS public.study_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) NOT NULL,
  recipe_id UUID REFERENCES public.study_recipes(id) NOT NULL,
  title TEXT NOT NULL,
  duration_minutes INTEGER NOT NULL,
  notes TEXT,
  timer_type TEXT CHECK (timer_type IN ('countdown', 'stopwatch')),
  completed_at TIMESTAMPTZ DEFAULT NOW()
);

-- Votes table
CREATE TABLE IF NOT EXISTS public.votes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) NOT NULL,
  recipe_id UUID REFERENCES public.study_recipes(id) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, recipe_id)
);

-- Bookmarks table
CREATE TABLE IF NOT EXISTS public.bookmarks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) NOT NULL,
  recipe_id UUID REFERENCES public.study_recipes(id) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, recipe_id)
);

-- Comments table
CREATE TABLE IF NOT EXISTS public.comments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) NOT NULL,
  recipe_id UUID REFERENCES public.study_recipes(id),
  log_id UUID REFERENCES public.study_logs(id),
  parent_id UUID REFERENCES public.comments(id),
  content TEXT NOT NULL,
  mentions UUID[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  CHECK ((recipe_id IS NOT NULL) OR (log_id IS NOT NULL))
);

-- Reactions table
CREATE TABLE IF NOT EXISTS public.reactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) NOT NULL,
  log_id UUID REFERENCES public.study_logs(id) NOT NULL,
  emoji TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, log_id, emoji)
);

-- Set up RLS policies
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.study_recipes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.study_materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.study_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookmarks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reactions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;

DROP POLICY IF EXISTS "Public recipes are viewable by everyone" ON public.study_recipes;
DROP POLICY IF EXISTS "Users can insert their own recipes" ON public.study_recipes;
DROP POLICY IF EXISTS "Users can update own recipes" ON public.study_recipes;
DROP POLICY IF EXISTS "Users can delete own recipes" ON public.study_recipes;

DROP POLICY IF EXISTS "Public materials are viewable by everyone" ON public.study_materials;
DROP POLICY IF EXISTS "Users can insert materials for their own recipes" ON public.study_materials;
DROP POLICY IF EXISTS "Users can update materials for their own recipes" ON public.study_materials;
DROP POLICY IF EXISTS "Users can delete materials for their own recipes" ON public.study_materials;

-- Basic RLS policies
CREATE POLICY "Public profiles are viewable by everyone" ON public.profiles FOR SELECT USING (true);
CREATE POLICY "Users can insert their own profile" ON public.profiles FOR INSERT WITH CHECK (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.profiles FOR UPDATE USING (auth.uid() = id);

-- Study Recipes policies
CREATE POLICY "Public recipes are viewable by everyone" ON public.study_recipes FOR SELECT USING (true);
CREATE POLICY "Users can insert their own recipes" ON public.study_recipes FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own recipes" ON public.study_recipes FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own recipes" ON public.study_recipes FOR DELETE USING (auth.uid() = user_id);

-- Study Materials policies
CREATE POLICY "Public materials are viewable by everyone" ON public.study_materials FOR SELECT USING (true);
CREATE POLICY "Users can insert materials for their own recipes" ON public.study_materials FOR INSERT WITH CHECK (
  EXISTS (SELECT 1 FROM public.study_recipes WHERE id = recipe_id AND user_id = auth.uid())
);
CREATE POLICY "Users can update materials for their own recipes" ON public.study_materials FOR UPDATE USING (
  EXISTS (SELECT 1 FROM public.study_recipes WHERE id = recipe_id AND user_id = auth.uid())
);
CREATE POLICY "Users can delete materials for their own recipes" ON public.study_materials FOR DELETE USING (
  EXISTS (SELECT 1 FROM public.study_recipes WHERE id = recipe_id AND user_id = auth.uid())
);

-- Study Logs policies
CREATE POLICY "Public logs are viewable by everyone" ON public.study_logs FOR SELECT USING (true);
CREATE POLICY "Users can insert their own logs" ON public.study_logs FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own logs" ON public.study_logs FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own logs" ON public.study_logs FOR DELETE USING (auth.uid() = user_id);

-- Votes policies
CREATE POLICY "Public votes are viewable by everyone" ON public.votes FOR SELECT USING (true);
CREATE POLICY "Users can insert their own votes" ON public.votes FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can delete own votes" ON public.votes FOR DELETE USING (auth.uid() = user_id);

-- Bookmarks policies
CREATE POLICY "Users can view their own bookmarks" ON public.bookmarks FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own bookmarks" ON public.bookmarks FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can delete own bookmarks" ON public.bookmarks FOR DELETE USING (auth.uid() = user_id);

-- Comments policies
CREATE POLICY "Public comments are viewable by everyone" ON public.comments FOR SELECT USING (true);
CREATE POLICY "Users can insert their own comments" ON public.comments FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own comments" ON public.comments FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own comments" ON public.comments FOR DELETE USING (auth.uid() = user_id);

-- Reactions policies
CREATE POLICY "Public reactions are viewable by everyone" ON public.reactions FOR SELECT USING (true);
CREATE POLICY "Users can insert their own reactions" ON public.reactions FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can delete own reactions" ON public.reactions FOR DELETE USING (auth.uid() = user_id);

-- Create a test user profile for testing
-- Note: This will only work if a user with this ID exists in auth.users
-- You'll need to create the user through Supabase Auth first

-- Insert some sample data for testing (optional)
-- INSERT INTO public.profiles (id, username, display_name, bio) 
-- VALUES ('00000000-0000-0000-0000-000000000000', 'testuser', 'Test User', 'This is a test user for development');

-- Verify tables were created
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
