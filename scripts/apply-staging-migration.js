#!/usr/bin/env node

/**
 * ステージング環境にマイグレーションを適用するスクリプト
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// ステージング環境の設定を読み込み
const envPath = path.join(__dirname, '..', '.env.staging');
if (!fs.existsSync(envPath)) {
  console.error('❌ .env.staging file not found. Run: vercel env pull .env.staging --environment=preview');
  process.exit(1);
}

// 環境変数を読み込み
require('dotenv').config({ path: envPath });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env.staging');
  process.exit(1);
}

console.log('🔧 Applying Migration to Staging Database');
console.log('==========================================');
console.log(`📍 Supabase URL: ${supabaseUrl}`);

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function applyMigration() {
  try {
    console.log('\n1. 📋 Reading migration file...');
    
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20240620000000_add_gamification.sql');
    
    if (!fs.existsSync(migrationPath)) {
      console.error('❌ Migration file not found:', migrationPath);
      return;
    }
    
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log('✅ Migration file loaded');
    console.log(`📄 File size: ${migrationSQL.length} characters`);

    console.log('\n2. 🔍 Checking if user_stats table already exists...');
    
    const { data: existingTable, error: checkError } = await supabase
      .from('user_stats')
      .select('count')
      .limit(1);
    
    if (!checkError) {
      console.log('⚠️  user_stats table already exists. Skipping migration.');
      return;
    }

    console.log('\n3. 🚀 Applying migration...');
    
    // Split the migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📊 Found ${statements.length} SQL statements to execute`);

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.length === 0) continue;
      
      console.log(`\n   Executing statement ${i + 1}/${statements.length}...`);
      console.log(`   📝 ${statement.substring(0, 100)}${statement.length > 100 ? '...' : ''}`);
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          console.log(`   ❌ Error: ${error.message}`);
          // Continue with next statement for non-critical errors
          if (error.message.includes('already exists')) {
            console.log('   ⚠️  Object already exists, continuing...');
          } else {
            console.log('   ⚠️  Error occurred, but continuing with next statement...');
          }
        } else {
          console.log('   ✅ Success');
        }
      } catch (err) {
        console.log(`   ❌ Exception: ${err.message}`);
        console.log('   ⚠️  Continuing with next statement...');
      }
    }

    console.log('\n4. 🧪 Verifying migration...');
    
    // Check if user_stats table now exists
    const { data: newTable, error: verifyError } = await supabase
      .from('user_stats')
      .select('count')
      .limit(1);
    
    if (verifyError) {
      console.log('❌ user_stats table still does not exist:', verifyError.message);
    } else {
      console.log('✅ user_stats table created successfully');
    }

    // Check if leaderboard view exists
    try {
      const { data: leaderboard, error: leaderboardError } = await supabase
        .from('leaderboard')
        .select('*')
        .limit(1);
      
      if (leaderboardError) {
        console.log('❌ leaderboard view not accessible:', leaderboardError.message);
      } else {
        console.log('✅ leaderboard view created successfully');
      }
    } catch (err) {
      console.log('❌ leaderboard view check failed:', err.message);
    }

    console.log('\n5. 🔧 Creating initial user stats for existing user...');
    
    // Create user stats for the existing user
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, username');
    
    if (profilesError) {
      console.log('❌ Could not fetch profiles:', profilesError.message);
    } else {
      for (const profile of profiles) {
        const { error: insertError } = await supabase
          .from('user_stats')
          .insert([{
            user_id: profile.id,
            total_study_sessions: 0,
            recipes_created: 0,
            total_upvotes_received: 0,
            total_experience_points: 0
          }])
          .select();
        
        if (insertError) {
          if (insertError.message.includes('duplicate key')) {
            console.log(`   ⚠️  User stats already exist for ${profile.username}`);
          } else {
            console.log(`   ❌ Failed to create user stats for ${profile.username}:`, insertError.message);
          }
        } else {
          console.log(`   ✅ Created user stats for ${profile.username}`);
        }
      }
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}

applyMigration().then(() => {
  console.log('\n🏁 Migration process completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
