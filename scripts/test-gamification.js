// Test script for gamification functionality
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://tyfcubkezcnnekyvkzii.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR5ZmN1YmtlemNubmVreXZremlpIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTQzNjI4MywiZXhwIjoyMDY1MDEyMjgzfQ.SufCtxGtpggJ5U2JAteSIoW7u1jYLZfJntwMSBeD2-A'

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function testGamificationDatabase() {
  console.log('🧪 Testing Gamification Database...\n')

  try {
    // Test 1: Check if all tables exist by querying them directly
    console.log('1. Checking database tables...')
    const tables = ['user_stats', 'achievements', 'user_achievements', 'levels', 'activity_log']
    let tableCount = 0

    for (const table of tables) {
      try {
        const { error } = await supabase.from(table).select('*').limit(1)
        if (!error) tableCount++
      } catch (e) {
        console.log(`   ⚠️  Table ${table} not accessible`)
      }
    }
    console.log(`✅ Found ${tableCount}/${tables.length} gamification tables accessible`)

    // Test 2: Check achievements data
    console.log('\n2. Checking achievements data...')
    const { data: achievements, error: achievementsError } = await supabase
      .from('achievements')
      .select('*')

    if (achievementsError) throw achievementsError
    console.log(`✅ Found ${achievements.length} achievements`)

    // Test 3: Check levels data
    console.log('\n3. Checking levels data...')
    const { data: levels, error: levelsError } = await supabase
      .from('levels')
      .select('*')
      .order('level')

    if (levelsError) throw levelsError
    console.log(`✅ Found ${levels.length} levels`)
    levels.forEach(level => {
      console.log(`   Level ${level.level}: ${level.name} (${level.min_experience_points}-${level.max_experience_points || '∞'} XP)`)
    })

    // Test 4: Test user stats initialization
    console.log('\n4. Testing user stats initialization...')
    
    // Get a test user (first profile)
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, username')
      .limit(1)

    if (profilesError) throw profilesError
    
    if (profiles.length === 0) {
      console.log('⚠️  No profiles found for testing')
      return
    }

    const testUser = profiles[0]
    console.log(`   Using test user: ${testUser.username}`)

    // Check if user stats exist
    const { data: existingStats, error: statsError } = await supabase
      .from('user_stats')
      .select('*')
      .eq('user_id', testUser.id)
      .single()

    if (statsError && statsError.code !== 'PGRST116') {
      throw statsError
    }

    if (!existingStats) {
      // Initialize user stats
      const { data: newStats, error: initError } = await supabase
        .from('user_stats')
        .insert([{ user_id: testUser.id }])
        .select()
        .single()

      if (initError) throw initError
      console.log('✅ User stats initialized successfully')
    } else {
      console.log('✅ User stats already exist')
    }

    // Test 5: Test leaderboard view
    console.log('\n5. Testing leaderboard view...')
    const { data: leaderboard, error: leaderboardError } = await supabase
      .from('leaderboard')
      .select('*')
      .limit(5)

    if (leaderboardError) throw leaderboardError
    console.log(`✅ Leaderboard query successful, found ${leaderboard.length} entries`)

    // Test 6: Test refresh function
    console.log('\n6. Testing leaderboard refresh function...')
    const { error: refreshError } = await supabase.rpc('refresh_leaderboard')
    
    if (refreshError) throw refreshError
    console.log('✅ Leaderboard refresh function works')

    // Test 7: Test RLS policies
    console.log('\n7. Testing RLS policies...')
    
    // Test user_stats policy (should be readable by everyone)
    const { data: statsData, error: rlsError } = await supabase
      .from('user_stats')
      .select('user_id, total_experience_points')
      .limit(1)

    if (rlsError) throw rlsError
    console.log('✅ RLS policies working correctly')

    console.log('\n🎉 All gamification database tests passed!')

  } catch (error) {
    console.error('❌ Test failed:', error)
    process.exit(1)
  }
}

// Run tests
testGamificationDatabase()
