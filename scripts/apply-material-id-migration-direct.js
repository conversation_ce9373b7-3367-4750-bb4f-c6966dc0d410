const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:')
  console.error('- NEXT_PUBLIC_SUPABASE_URL')
  console.error('- SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function applyMigration() {
  try {
    console.log('🔄 Starting material_id migration (direct method)...')
    
    // Step 1: Check if column already exists
    console.log('🔍 Checking if material_id column already exists...')
    const { data: existingColumns, error: checkError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'study_logs')
      .eq('column_name', 'material_id')
    
    if (checkError) {
      console.error('❌ Failed to check existing columns:', checkError)
      process.exit(1)
    }
    
    if (existingColumns && existingColumns.length > 0) {
      console.log('✅ material_id column already exists, skipping column creation')
    } else {
      // Step 2: Add material_id column using raw SQL
      console.log('⚡ Adding material_id column...')
      
      // Use the raw SQL approach
      const { error: columnError } = await supabase
        .rpc('exec', {
          sql: 'ALTER TABLE public.study_logs ADD COLUMN material_id UUID REFERENCES public.study_materials(id) ON DELETE SET NULL;'
        })
      
      if (columnError) {
        // Try alternative approach using direct query
        console.log('📝 Trying alternative approach...')
        
        // Create a temporary function to execute the SQL
        const alterTableSQL = `
          DO $$
          BEGIN
            IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns 
              WHERE table_schema = 'public' 
              AND table_name = 'study_logs' 
              AND column_name = 'material_id'
            ) THEN
              ALTER TABLE public.study_logs 
              ADD COLUMN material_id UUID REFERENCES public.study_materials(id) ON DELETE SET NULL;
            END IF;
          END $$;
        `
        
        // Since we can't execute raw SQL directly, we'll need to use Supabase SQL Editor
        console.log('❌ Cannot execute ALTER TABLE directly through Supabase client.')
        console.log('📝 Please execute the following SQL in Supabase SQL Editor:')
        console.log('')
        console.log('-- Add material_id column to study_logs table')
        console.log('ALTER TABLE public.study_logs')
        console.log('ADD COLUMN IF NOT EXISTS material_id UUID REFERENCES public.study_materials(id) ON DELETE SET NULL;')
        console.log('')
        console.log('-- Add index for better query performance')
        console.log('CREATE INDEX IF NOT EXISTS idx_study_logs_material_id ON public.study_logs(material_id);')
        console.log('')
        console.log('-- Add comment for documentation')
        console.log("COMMENT ON COLUMN public.study_logs.material_id IS 'References the specific study material that was studied in this session';")
        console.log('')
        console.log('🔗 Go to: https://supabase.com/dashboard/project/[your-project]/sql')
        
        return
      }
      
      console.log('✅ material_id column added successfully!')
    }
    
    // Step 3: Check if index already exists
    console.log('🔍 Checking if index already exists...')
    const { data: existingIndexes, error: indexCheckError } = await supabase
      .from('pg_indexes')
      .select('indexname')
      .eq('schemaname', 'public')
      .eq('tablename', 'study_logs')
      .eq('indexname', 'idx_study_logs_material_id')
    
    if (indexCheckError) {
      console.warn('⚠️ Could not check existing indexes:', indexCheckError)
    } else if (existingIndexes && existingIndexes.length > 0) {
      console.log('✅ Index already exists, skipping index creation')
    } else {
      console.log('⚡ Adding index...')
      // Index creation would also need to be done in SQL Editor
      console.log('📝 Index creation also needs to be done in SQL Editor')
    }
    
    // Step 4: Verify the migration
    console.log('🔍 Verifying migration...')
    
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_schema', 'public')
      .eq('table_name', 'study_logs')
      .eq('column_name', 'material_id')
    
    if (columnsError) {
      console.error('❌ Verification failed:', columnsError)
      process.exit(1)
    }
    
    if (columns && columns.length > 0) {
      console.log('✅ material_id column verified:', columns[0])
      console.log('')
      console.log('🎉 Migration verification completed!')
      console.log('📝 The study_logs table now has a material_id column.')
      console.log('🔗 Foreign key constraint to study_materials table is active.')
    } else {
      console.log('❌ material_id column not found - manual SQL execution required')
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error)
    console.log('')
    console.log('📝 Please execute the following SQL manually in Supabase SQL Editor:')
    console.log('')
    console.log('-- Add material_id column to study_logs table')
    console.log('ALTER TABLE public.study_logs')
    console.log('ADD COLUMN IF NOT EXISTS material_id UUID REFERENCES public.study_materials(id) ON DELETE SET NULL;')
    console.log('')
    console.log('-- Add index for better query performance')
    console.log('CREATE INDEX IF NOT EXISTS idx_study_logs_material_id ON public.study_logs(material_id);')
    console.log('')
    console.log('-- Add comment for documentation')
    console.log("COMMENT ON COLUMN public.study_logs.material_id IS 'References the specific study material that was studied in this session';")
    console.log('')
    console.log('🔗 Go to: https://supabase.com/dashboard/project/[your-project]/sql')
  }
}

applyMigration()
