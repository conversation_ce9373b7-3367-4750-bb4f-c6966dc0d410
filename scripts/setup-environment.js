#!/usr/bin/env node

/**
 * Environment Setup Script
 * 環境設定を支援するスクリプト
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function main() {
  console.log('🚀 StudyShare Environment Setup');
  console.log('================================\n');

  const environment = await question('Which environment do you want to set up? (development/staging/production): ');
  
  if (!['development', 'staging', 'production'].includes(environment)) {
    console.error('❌ Invalid environment. Please choose development, staging, or production.');
    process.exit(1);
  }

  console.log(`\n📝 Setting up ${environment} environment...\n`);

  // Supabase project information
  const supabaseUrl = await question('Enter Supabase Project URL: ');
  const supabaseAnonKey = await question('Enter Supabase Anon Key: ');
  const supabaseServiceKey = await question('Enter Supabase Service Role Key: ');
  const postgresUrl = await question('Enter Postgres URL: ');
  const postgresPassword = await question('Enter Postgres Password: ');

  // Extract project ID from URL
  const projectId = supabaseUrl.match(/https:\/\/([^.]+)\.supabase\.co/)?.[1];
  if (!projectId) {
    console.error('❌ Invalid Supabase URL format');
    process.exit(1);
  }

  // Generate environment file content
  const envContent = generateEnvContent(environment, {
    projectId,
    supabaseUrl,
    supabaseAnonKey,
    supabaseServiceKey,
    postgresUrl,
    postgresPassword
  });

  // Write to appropriate file
  const envFile = environment === 'development' ? '.env.local' : `.env.${environment}`;
  const envPath = path.join(process.cwd(), envFile);

  try {
    fs.writeFileSync(envPath, envContent);
    console.log(`\n✅ Environment file created: ${envFile}`);
    
    // Update Next.js config if needed
    if (environment !== 'production') {
      await updateNextConfig(projectId, environment);
    }

    console.log('\n🎉 Environment setup completed!');
    console.log('\nNext steps:');
    console.log('1. Verify the environment variables in the created file');
    console.log('2. Run database migrations if needed');
    console.log('3. Test the connection with: npm run dev');
    
  } catch (error) {
    console.error('❌ Error creating environment file:', error.message);
    process.exit(1);
  }

  rl.close();
}

function generateEnvContent(environment, config) {
  const {
    projectId,
    supabaseUrl,
    supabaseAnonKey,
    supabaseServiceKey,
    postgresUrl,
    postgresPassword
  } = config;

  const baseContent = `# ${environment.charAt(0).toUpperCase() + environment.slice(1)} Environment
# ${getEnvironmentDescription(environment)}

# Database Configuration
POSTGRES_URL="${postgresUrl}"
POSTGRES_USER="postgres"
POSTGRES_HOST="db.${projectId}.supabase.co"
SUPABASE_JWT_SECRET="[JWT_SECRET_FROM_SUPABASE_SETTINGS]"
NEXT_PUBLIC_SUPABASE_ANON_KEY="${supabaseAnonKey}"
POSTGRES_PRISMA_URL="${postgresUrl}"
POSTGRES_PASSWORD="${postgresPassword}"
POSTGRES_DATABASE="postgres"
SUPABASE_URL="${supabaseUrl}"
NEXT_PUBLIC_SUPABASE_URL="${supabaseUrl}"
SUPABASE_SERVICE_ROLE_KEY="${supabaseServiceKey}"
POSTGRES_URL_NON_POOLING="${postgresUrl.replace(':6543', ':5432')}"

# Environment
NODE_ENV="${environment === 'development' ? 'development' : 'production'}"

# App specific settings
NEXT_PUBLIC_APP_ENV="${environment}"
NEXT_PUBLIC_DEBUG_MODE="${environment === 'development' ? 'true' : 'false'}"
ENVIRONMENT="${environment}"
APP_URL="${getAppUrl(environment)}"

# Feature flags
ENABLE_TEST_FEATURES="${environment !== 'production' ? 'true' : 'false'}"
TEST_USER_AUTO_CLEANUP="${environment === 'staging' ? 'true' : 'false'}"
`;

  return baseContent;
}

function getEnvironmentDescription(environment) {
  switch (environment) {
    case 'development':
      return 'ローカル開発環境用の設定';
    case 'staging':
      return 'ステージング環境用の設定';
    case 'production':
      return '本番環境用の設定';
    default:
      return '環境設定';
  }
}

function getAppUrl(environment) {
  switch (environment) {
    case 'development':
      return 'http://localhost:3000';
    case 'staging':
      return 'https://staging.studyshare.com';
    case 'production':
      return 'https://studyshare.com';
    default:
      return 'http://localhost:3000';
  }
}

async function updateNextConfig(projectId, environment) {
  const configPath = path.join(process.cwd(), 'next.config.js');
  
  try {
    let configContent = fs.readFileSync(configPath, 'utf8');
    
    // Add the new domain to the images configuration
    const newDomain = `${projectId}.supabase.co`;
    
    if (!configContent.includes(newDomain)) {
      configContent = configContent.replace(
        /domains: \[([\s\S]*?)\]/,
        (match, domains) => {
          const updatedDomains = domains.trim();
          const newDomainsArray = updatedDomains 
            ? `${updatedDomains},\n      '${newDomain}', // ${environment.charAt(0).toUpperCase() + environment.slice(1)}`
            : `'${newDomain}', // ${environment.charAt(0).toUpperCase() + environment.slice(1)}`;
          
          return `domains: [\n      ${newDomainsArray}\n    ]`;
        }
      );
      
      fs.writeFileSync(configPath, configContent);
      console.log(`✅ Updated next.config.js with ${environment} domain`);
    }
  } catch (error) {
    console.warn(`⚠️  Could not update next.config.js: ${error.message}`);
  }
}

// Run the script
main().catch(error => {
  console.error('❌ Setup failed:', error);
  process.exit(1);
});
