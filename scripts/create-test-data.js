const { createClient } = require('@supabase/supabase-js')

// Load environment variables manually
const supabaseUrl = 'http://127.0.0.1:54321'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables')
  process.exit(1)
}

// Create admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function createTestData() {
  console.log('🚀 Creating test data...')

  try {
    // Get existing test user
    console.log('👤 Getting existing test user...')
    const { data: existingProfile } = await supabase
      .from('profiles')
      .select('id')
      .eq('username', 'testuser')
      .single()

    let userId
    if (existingProfile) {
      userId = existingProfile.id
      console.log('✅ Using existing user:', userId)
    } else {
      // Create test user if doesn't exist
      console.log('👤 Creating new test user...')
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: '<EMAIL>',
        password: 'testpassword123',
        email_confirm: true
      })

      if (authError) {
        console.error('❌ Error creating user:', authError)
        return
      }

      userId = authData.user.id
      console.log('✅ User created:', userId)

      // Create profile
      console.log('📝 Creating profile...')
      const { error: profileError } = await supabase
        .from('profiles')
        .insert([{
          id: userId,
          username: 'testuser',
          display_name: 'Test User',
          bio: 'This is a test user for development'
        }])

      if (profileError) {
        console.error('❌ Error creating profile:', profileError)
        return
      }

      console.log('✅ Profile created')
    }

    // Create test recipes
    console.log('📚 Creating test recipes...')
    
    const recipes = [
      {
        user_id: userId,
        title: 'Learn JavaScript Fundamentals',
        estimated_duration: '2 weeks',
        tips: 'Practice coding every day for best results. Start with basic syntax and gradually move to more complex concepts.',
        tags: ['javascript', 'programming', 'beginner'],
        upvote_count: 5
      },
      {
        user_id: userId,
        title: 'Master React Hooks',
        estimated_duration: '1 month',
        tips: 'Build small projects to understand useState, useEffect, and custom hooks. Practice is key!',
        tags: ['react', 'hooks', 'frontend'],
        upvote_count: 12
      },
      {
        user_id: userId,
        title: 'Database Design Principles',
        estimated_duration: '3 weeks',
        tips: 'Start with normalization concepts, then practice with real-world examples.',
        tags: ['database', 'sql', 'design'],
        upvote_count: 8
      }
    ]

    for (let i = 0; i < recipes.length; i++) {
      const recipe = recipes[i]
      
      const { data: recipeData, error: recipeError } = await supabase
        .from('study_recipes')
        .insert([recipe])
        .select()
        .single()

      if (recipeError) {
        console.error(`❌ Error creating recipe ${i + 1}:`, recipeError)
        continue
      }

      console.log(`✅ Recipe ${i + 1} created:`, recipeData.title)

      // Create materials for each recipe
      const materials = [
        {
          recipe_id: recipeData.id,
          material_name: `${recipe.title} - Video Course`,
          frequency: 'daily',
          time_per_session: 60,
          time_unit: 'minutes',
          order_index: 0
        },
        {
          recipe_id: recipeData.id,
          material_name: `${recipe.title} - Practice Exercises`,
          frequency: 'daily',
          time_per_session: 30,
          time_unit: 'minutes',
          order_index: 1
        }
      ]

      const { error: materialsError } = await supabase
        .from('study_materials')
        .insert(materials)

      if (materialsError) {
        console.error(`❌ Error creating materials for recipe ${i + 1}:`, materialsError)
      } else {
        console.log(`✅ Materials created for recipe ${i + 1}`)
      }
    }

    // Create some study logs
    console.log('📊 Creating test study logs...')
    
    const { data: recipesForLogs } = await supabase
      .from('study_recipes')
      .select('id, title')
      .eq('user_id', userId)
      .limit(2)

    if (recipesForLogs && recipesForLogs.length > 0) {
      const logs = [
        {
          user_id: userId,
          recipe_id: recipesForLogs[0].id,
          title: `Completed: ${recipesForLogs[0].title}`,
          duration_minutes: 90,
          notes: 'Great session! Learned about variables and functions.',
          completed_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // Yesterday
        },
        {
          user_id: userId,
          recipe_id: recipesForLogs[1] ? recipesForLogs[1].id : recipesForLogs[0].id,
          title: `Progress on: ${recipesForLogs[1] ? recipesForLogs[1].title : recipesForLogs[0].title}`,
          duration_minutes: 45,
          notes: 'Focused on understanding the concepts better.',
          completed_at: new Date().toISOString() // Today
        }
      ]

      for (let i = 0; i < logs.length; i++) {
        const { error: logError } = await supabase
          .from('study_logs')
          .insert([logs[i]])

        if (logError) {
          console.error(`❌ Error creating log ${i + 1}:`, logError)
        } else {
          console.log(`✅ Study log ${i + 1} created`)
        }
      }
    }

    console.log('🎉 Test data creation completed!')
    console.log('📧 Test user email: <EMAIL>')
    console.log('🔑 Test user password: testpassword123')

  } catch (error) {
    console.error('❌ Unexpected error:', error)
  }
}

createTestData()
