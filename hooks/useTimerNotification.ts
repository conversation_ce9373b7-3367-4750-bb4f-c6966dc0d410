import { useCallback, useEffect, useState } from 'react'

export interface NotificationState {
  permission: NotificationPermission
  isSupported: boolean
}

export interface NotificationActions {
  requestPermission: () => Promise<NotificationPermission>
  showNotification: (title: string, options?: NotificationOptions) => void
  playSound: () => void
  vibrate: (pattern?: number | number[]) => void
}

export function useTimerNotification(): [NotificationState, NotificationActions] {
  const [permission, setPermission] = useState<NotificationPermission>('default')
  const [isSupported, setIsSupported] = useState(false)

  // Check if notifications are supported
  useEffect(() => {
    const supported = 'Notification' in window
    setIsSupported(supported)
    
    if (supported) {
      setPermission(Notification.permission)
    }
  }, [])

  const requestPermission = useCallback(async (): Promise<NotificationPermission> => {
    if (!isSupported) return 'denied'

    try {
      const result = await Notification.requestPermission()
      setPermission(result)
      return result
    } catch (error) {
      console.warn('Failed to request notification permission:', error)
      return 'denied'
    }
  }, [isSupported])

  const showNotification = useCallback((title: string, options?: NotificationOptions) => {
    if (!isSupported || permission !== 'granted') return

    try {
      const notification = new Notification(title, {
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: 'timer-notification',
        requireInteraction: true,
        ...options,
      })

      // Auto close after 5 seconds
      setTimeout(() => {
        notification.close()
      }, 5000)

      // Handle click to focus window
      notification.onclick = () => {
        window.focus()
        notification.close()
      }
    } catch (error) {
      console.warn('Failed to show notification:', error)
    }
  }, [isSupported, permission])

  const playSound = useCallback(() => {
    try {
      // Create audio context for beep sound
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
      oscillator.type = 'sine'

      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5)

      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.5)
    } catch (error) {
      console.warn('Failed to play sound:', error)
    }
  }, [])

  const vibrate = useCallback((pattern: number | number[] = 200) => {
    if ('vibrate' in navigator) {
      try {
        navigator.vibrate(pattern)
      } catch (error) {
        console.warn('Failed to vibrate:', error)
      }
    }
  }, [])

  const state: NotificationState = {
    permission,
    isSupported,
  }

  const actions: NotificationActions = {
    requestPermission,
    showNotification,
    playSound,
    vibrate,
  }

  return [state, actions]
}

// Utility function for timer end notification
export function useTimerEndNotification() {
  const [notificationState, notificationActions] = useTimerNotification()

  const notifyTimerEnd = useCallback(async (mode: 'timer' | 'stopwatch', duration?: string) => {
    // Request permission if not granted
    if (notificationState.permission === 'default') {
      await notificationActions.requestPermission()
    }

    const title = mode === 'timer' ? 'Timer Finished!' : 'Stopwatch Stopped!'
    const body = duration 
      ? `Study session completed: ${duration}`
      : 'Your study session has ended.'

    // Show notification
    notificationActions.showNotification(title, {
      body,
      icon: '/favicon.ico',
    })

    // Play sound
    notificationActions.playSound()

    // Vibrate on mobile
    notificationActions.vibrate([200, 100, 200])
  }, [notificationState.permission, notificationActions])

  return {
    notifyTimerEnd,
    ...notificationState,
    ...notificationActions,
  }
}
