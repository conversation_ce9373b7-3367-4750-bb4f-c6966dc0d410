import { useCallback, useEffect, useRef, useState } from 'react'

export type TimerMode = 'timer' | 'stopwatch'

export interface TimerState {
  mode: TimerMode
  isRunning: boolean
  isPaused: boolean
  timeElapsed: number        // seconds for stopwatch
  timeRemaining: number      // seconds for timer
  initialTime: number        // seconds for timer initial setting
}

export interface TimerActions {
  setMode: (mode: TimerMode) => void
  start: () => void
  pause: () => void
  stop: () => void
  reset: () => void
  setInitialTime: (minutes: number) => void
  setInitialTimeSeconds: (totalSeconds: number) => void
  addTime: (seconds: number) => void
}

const DEFAULT_TIMER_MINUTES = 25

export function useTimer(): [TimerState, TimerActions] {
  const [mode, setMode] = useState<TimerMode>('timer')
  const [isRunning, setIsRunning] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [timeElapsed, setTimeElapsed] = useState(0)
  const [initialTime, setInitialTime] = useState(DEFAULT_TIMER_MINUTES * 60)
  const [timeRemaining, setTimeRemaining] = useState(DEFAULT_TIMER_MINUTES * 60)
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const onTimerEndRef = useRef<(() => void) | null>(null)

  // Timer interval effect
  useEffect(() => {
    if (isRunning && !isPaused) {
      intervalRef.current = setInterval(() => {
        if (mode === 'stopwatch') {
          setTimeElapsed(prev => prev + 1)
        } else {
          setTimeRemaining(prev => {
            if (prev <= 1) {
              setIsRunning(false)
              setIsPaused(false)
              // Timer ended callback
              if (onTimerEndRef.current) {
                onTimerEndRef.current()
              }
              return 0
            }
            return prev - 1
          })
        }
      }, 1000)
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isRunning, isPaused, mode])

  // Reset timer when mode changes
  useEffect(() => {
    setIsRunning(false)
    setIsPaused(false)
    setTimeElapsed(0)
    setTimeRemaining(initialTime)
  }, [mode, initialTime])

  // Save state to localStorage
  useEffect(() => {
    const state = {
      mode,
      initialTime,
      timeElapsed: mode === 'stopwatch' ? timeElapsed : 0,
      timeRemaining: mode === 'timer' ? timeRemaining : initialTime,
    }
    localStorage.setItem('timer-state', JSON.stringify(state))
  }, [mode, initialTime, timeElapsed, timeRemaining])

  // Load state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem('timer-state')
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState)
        setMode(parsed.mode || 'timer')
        setInitialTime(parsed.initialTime || DEFAULT_TIMER_MINUTES * 60)
        if (parsed.mode === 'stopwatch') {
          setTimeElapsed(parsed.timeElapsed || 0)
        } else {
          setTimeRemaining(parsed.timeRemaining || parsed.initialTime || DEFAULT_TIMER_MINUTES * 60)
        }
      } catch (error) {
        console.warn('Failed to load timer state from localStorage:', error)
      }
    }
  }, [])

  const start = useCallback(() => {
    setIsRunning(true)
    setIsPaused(false)
  }, [])

  const pause = useCallback(() => {
    setIsPaused(true)
  }, [])

  const stop = useCallback(() => {
    setIsRunning(false)
    setIsPaused(false)
  }, [])

  const reset = useCallback(() => {
    setIsRunning(false)
    setIsPaused(false)
    setTimeElapsed(0)
    setTimeRemaining(initialTime)
  }, [initialTime])

  const setInitialTimeMinutes = useCallback((minutes: number) => {
    const seconds = Math.max(1, minutes) * 60
    setInitialTime(seconds)
    if (!isRunning) {
      setTimeRemaining(seconds)
    }
  }, [isRunning])

  const setInitialTimeSeconds = useCallback((totalSeconds: number) => {
    const seconds = Math.max(1, totalSeconds)
    setInitialTime(seconds)
    if (!isRunning) {
      setTimeRemaining(seconds)
    }
  }, [isRunning])

  const addTime = useCallback((seconds: number) => {
    if (mode === 'timer') {
      setInitialTime(prev => {
        const newTime = Math.max(60, prev + seconds) // minimum 1 minute
        if (!isRunning) {
          setTimeRemaining(newTime)
        }
        return newTime
      })
    }
  }, [mode, isRunning])

  const setTimerMode = useCallback((newMode: TimerMode) => {
    setMode(newMode)
  }, [])

  // Set timer end callback
  const setOnTimerEnd = useCallback((callback: () => void) => {
    onTimerEndRef.current = callback
  }, [])

  const state: TimerState = {
    mode,
    isRunning,
    isPaused,
    timeElapsed,
    timeRemaining,
    initialTime,
  }

  const actions: TimerActions = {
    setMode: setTimerMode,
    start,
    pause,
    stop,
    reset,
    setInitialTime: setInitialTimeMinutes,
    setInitialTimeSeconds,
    addTime,
  }

  return [state, actions]
}

// Utility functions
export function formatTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

export function getProgress(state: TimerState): number {
  if (state.mode === 'stopwatch') {
    // For stopwatch, we'll show progress up to 60 minutes (3600 seconds)
    const maxTime = 3600
    return Math.min(state.timeElapsed / maxTime, 1)
  } else {
    // For timer, show remaining time as progress
    if (state.initialTime === 0) return 0
    return (state.initialTime - state.timeRemaining) / state.initialTime
  }
}
