// Check if gamification tables exist in the database
const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')

// Read environment variables from .env.local
let supabaseUrl, supabaseAnonKey

try {
  const envContent = fs.readFileSync('.env.local', 'utf8')
  const envLines = envContent.split('\n')

  for (const line of envLines) {
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_URL=')) {
      supabaseUrl = line.split('=')[1].replace(/"/g, '')
    }
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_ANON_KEY=')) {
      supabaseAnonKey = line.split('=')[1].replace(/"/g, '')
    }
  }
} catch (error) {
  console.error('Error reading .env.local:', error.message)
}

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function checkGamificationTables() {
  console.log('🔍 Checking gamification tables in database...\n')
  
  const tables = [
    'user_stats',
    'achievements', 
    'user_achievements',
    'levels',
    'activity_log'
  ]
  
  const results = {}
  
  for (const table of tables) {
    try {
      console.log(`Checking table: ${table}`)
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1)
      
      if (error) {
        if (error.code === '42P01') {
          console.log(`   ❌ Table '${table}' does not exist`)
          results[table] = 'missing'
        } else if (error.code === 'PGRST204') {
          console.log(`   ✅ Table '${table}' exists but is empty`)
          results[table] = 'empty'
        } else {
          console.log(`   ⚠️  Table '${table}' error: ${error.message}`)
          results[table] = 'error'
        }
      } else {
        console.log(`   ✅ Table '${table}' exists with data`)
        results[table] = 'exists'
      }
    } catch (e) {
      console.log(`   ❌ Failed to check table '${table}': ${e.message}`)
      results[table] = 'failed'
    }
  }
  
  console.log('\n📊 Summary:')
  console.log('='.repeat(50))
  
  const existing = Object.entries(results).filter(([_, status]) => 
    status === 'exists' || status === 'empty'
  )
  const missing = Object.entries(results).filter(([_, status]) => 
    status === 'missing' || status === 'failed'
  )
  
  console.log(`✅ Existing tables: ${existing.length}/${tables.length}`)
  existing.forEach(([table, status]) => {
    console.log(`   - ${table} (${status})`)
  })
  
  console.log(`❌ Missing tables: ${missing.length}/${tables.length}`)
  missing.forEach(([table, status]) => {
    console.log(`   - ${table} (${status})`)
  })
  
  if (missing.length > 0) {
    console.log('\n🔧 To fix this, you need to run the gamification schema:')
    console.log('1. Copy the content from docs/gamification-schema.sql')
    console.log('2. Run it in your Supabase SQL Editor')
    console.log('3. Or run: npm run supabase:reset (if using local Supabase)')
  }
  
  // Also check profiles table for gamification columns
  console.log('\n🔍 Checking profiles table for gamification columns...')
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('rank_level, total_study_time')
      .limit(1)
    
    if (error) {
      console.log('❌ Error checking profiles columns:', error.message)
    } else {
      console.log('✅ Profiles table has gamification columns')
    }
  } catch (e) {
    console.log('❌ Failed to check profiles table:', e.message)
  }
}

checkGamificationTables()
