// Check study_logs table structure
const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')

// Read environment variables from .env.local
let supabaseUrl, supabaseAnonKey

try {
  const envContent = fs.readFileSync('.env.local', 'utf8')
  const envLines = envContent.split('\n')

  for (const line of envLines) {
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_URL=')) {
      supabaseUrl = line.split('=')[1].replace(/"/g, '')
    }
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_ANON_KEY=')) {
      supabaseAnonKey = line.split('=')[1].replace(/"/g, '')
    }
  }
} catch (error) {
  console.error('Error reading .env.local:', error.message)
}

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabase<PERSON>non<PERSON>ey)

async function checkStudyLogsTable() {
  console.log('Checking study_logs table structure...')
  
  try {
    // Try to select from study_logs table to see what columns exist
    const { data, error } = await supabase
      .from('study_logs')
      .select('*')
      .limit(1)
    
    if (error) {
      console.log('❌ Error accessing study_logs table:', error.message)
      console.log('Error code:', error.code)
      console.log('Error details:', error.details)
      
      if (error.code === '42P01') {
        console.log('Table does not exist')
      } else if (error.code === 'PGRST204') {
        console.log('Table exists but is empty (this is OK)')
      }
    } else {
      console.log('✅ study_logs table exists and is accessible')
      console.log('Sample data structure:', data)
    }
    
    // Try to insert a test record to see what columns are expected
    console.log('\nTesting insert to identify missing columns...')
    const testUserId = 'dbcb49be-b796-4656-90e8-62bb3a41f24d'
    const testRecipeId = 'c587acfd-f315-4f69-b713-5d501dc3ac19'
    
    const { error: insertError } = await supabase
      .from('study_logs')
      .insert([{
        user_id: testUserId,
        recipe_id: testRecipeId,
        duration_minutes: 30,
        notes: 'test',
        timer_type: 'stopwatch'
      }])
    
    if (insertError) {
      console.log('❌ Insert error:', insertError.message)
      console.log('Error code:', insertError.code)
      console.log('Error details:', insertError.details)
      
      if (insertError.message.includes('duration_minutes')) {
        console.log('\n🔍 The duration_minutes column is missing from the table!')
        console.log('Need to add this column to the database.')
      }
    } else {
      console.log('✅ Insert successful (but will be blocked by RLS)')
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error)
  }
}

checkStudyLogsTable()
