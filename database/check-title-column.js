// Check if title column exists in study_logs table
const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')

// Read environment variables from .env.local
let supabaseUrl, supabaseAnon<PERSON>ey

try {
  const envContent = fs.readFileSync('.env.local', 'utf8')
  const envLines = envContent.split('\n')

  for (const line of envLines) {
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_URL=')) {
      supabaseUrl = line.split('=')[1].replace(/"/g, '')
    }
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_ANON_KEY=')) {
      supabaseAnonKey = line.split('=')[1].replace(/"/g, '')
    }
  }
} catch (error) {
  console.error('Error reading .env.local:', error.message)
}

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabase<PERSON>non<PERSON>ey)

async function checkTitleColumn() {
  console.log('Checking title column in study_logs table...')
  
  try {
    const testUserId = 'dbcb49be-b796-4656-90e8-62bb3a41f24d'
    const testRecipeId = 'c587acfd-f315-4f69-b713-5d501dc3ac19'
    
    console.log('\nTesting with title column...')
    const { error: titleError } = await supabase
      .from('study_logs')
      .insert([{
        user_id: testUserId,
        recipe_id: testRecipeId,
        title: 'Test Study Session Title',
        duration: 30,
        content: 'Test study session notes'
      }])
    
    if (titleError) {
      if (titleError.code === '42501') {
        console.log('✅ Title column exists (insert blocked by RLS as expected)')
      } else {
        console.log('❌ Title error:', titleError.message)
        console.log('Error code:', titleError.code)
        
        if (titleError.message.includes('title')) {
          console.log('❌ Title column issue detected')
        }
      }
    } else {
      console.log('✅ Title column works perfectly')
    }
    
    console.log('\nTesting all required fields...')
    const { error: allFieldsError } = await supabase
      .from('study_logs')
      .insert([{
        user_id: testUserId,
        recipe_id: testRecipeId,
        title: 'Complete Test Title',
        duration: 30
      }])
    
    if (allFieldsError) {
      if (allFieldsError.code === '42501') {
        console.log('✅ All required fields work (insert blocked by RLS as expected)')
      } else {
        console.log('❌ All fields error:', allFieldsError.message)
      }
    } else {
      console.log('✅ All required fields sufficient')
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error)
  }
}

checkTitleColumn()
