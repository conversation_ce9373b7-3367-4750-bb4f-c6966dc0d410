-- Fix RLS policies to allow public read access for study recipes and related data

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;

-- Profiles policies
CREATE POLICY "Public profiles are viewable by everyone" ON public.profiles FOR SELECT USING (true);
CREATE POLICY "Users can insert their own profile" ON public.profiles FOR INSERT WITH CHECK (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.profiles FOR UPDATE USING (auth.uid() = id);

-- Study recipes policies - allow public read access
DROP POLICY IF EXISTS "Study recipes are viewable by everyone" ON public.study_recipes;
DROP POLICY IF EXISTS "Users can insert their own recipes" ON public.study_recipes;
DROP POLICY IF EXISTS "Users can update their own recipes" ON public.study_recipes;

CREATE POLICY "Study recipes are viewable by everyone" ON public.study_recipes FOR SELECT USING (true);
CREATE POLICY "Users can insert their own recipes" ON public.study_recipes FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own recipes" ON public.study_recipes FOR UPDATE USING (auth.uid() = user_id);

-- Study materials policies - allow public read access
DROP POLICY IF EXISTS "Study materials are viewable by everyone" ON public.study_materials;
DROP POLICY IF EXISTS "Users can insert materials for their recipes" ON public.study_materials;

CREATE POLICY "Study materials are viewable by everyone" ON public.study_materials FOR SELECT USING (true);
CREATE POLICY "Users can insert materials for their recipes" ON public.study_materials FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.study_recipes 
    WHERE id = recipe_id AND user_id = auth.uid()
  )
);

-- Votes policies
DROP POLICY IF EXISTS "Users can view all votes" ON public.votes;
DROP POLICY IF EXISTS "Users can manage their own votes" ON public.votes;

CREATE POLICY "Users can view all votes" ON public.votes FOR SELECT USING (true);
CREATE POLICY "Users can manage their own votes" ON public.votes FOR ALL USING (auth.uid() = user_id);

-- Bookmarks policies
DROP POLICY IF EXISTS "Users can view all bookmarks" ON public.bookmarks;
DROP POLICY IF EXISTS "Users can manage their own bookmarks" ON public.bookmarks;

CREATE POLICY "Users can view all bookmarks" ON public.bookmarks FOR SELECT USING (true);
CREATE POLICY "Users can manage their own bookmarks" ON public.bookmarks FOR ALL USING (auth.uid() = user_id);

-- Study logs policies
DROP POLICY IF EXISTS "Study logs are viewable by everyone" ON public.study_logs;
DROP POLICY IF EXISTS "Users can manage their own logs" ON public.study_logs;

CREATE POLICY "Study logs are viewable by everyone" ON public.study_logs FOR SELECT USING (true);
CREATE POLICY "Users can manage their own logs" ON public.study_logs FOR ALL USING (auth.uid() = user_id);

-- Comments policies
DROP POLICY IF EXISTS "Comments are viewable by everyone" ON public.comments;
DROP POLICY IF EXISTS "Users can manage their own comments" ON public.comments;

CREATE POLICY "Comments are viewable by everyone" ON public.comments FOR SELECT USING (true);
CREATE POLICY "Users can manage their own comments" ON public.comments FOR ALL USING (auth.uid() = user_id);

-- Reactions policies
DROP POLICY IF EXISTS "Reactions are viewable by everyone" ON public.reactions;
DROP POLICY IF EXISTS "Users can manage their own reactions" ON public.reactions;

CREATE POLICY "Reactions are viewable by everyone" ON public.reactions FOR SELECT USING (true);
CREATE POLICY "Users can manage their own reactions" ON public.reactions FOR ALL USING (auth.uid() = user_id);
