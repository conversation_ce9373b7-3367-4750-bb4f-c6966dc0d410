-- Fix leaderboard materialized view permissions
-- This script resolves the "must be owner of materialized view leaderboard" error

-- First, check current materialized view status
DO $$
BEGIN
    -- Check if leaderboard materialized view exists
    IF EXISTS (
        SELECT 1 FROM pg_matviews 
        WHERE schemaname = 'public' AND matviewname = 'leaderboard'
    ) THEN
        RAISE NOTICE 'Leaderboard materialized view exists';
    ELSE
        RAISE NOTICE 'Leaderboard materialized view does not exist';
    END IF;
END $$;

-- Drop and recreate the materialized view with proper ownership
DROP MATERIALIZED VIEW IF EXISTS public.leaderboard;

-- Recreate the leaderboard materialized view
CREATE MATERIALIZED VIEW public.leaderboard AS
SELECT
  p.id,
  p.username,
  p.display_name,
  p.avatar_url,
  p.rank_level,
  p.total_study_time,
  COALESCE(us.total_experience_points, 0) as total_experience_points,
  COALESCE(us.current_streak_days, 0) as current_streak_days,
  COALESCE(us.total_study_sessions, 0) as total_study_sessions,
  COALESCE(us.recipes_created, 0) as recipes_created,
  COALESCE(us.total_upvotes_received, 0) as total_upvotes_received,
  RANK() OVER (ORDER BY COALESCE(us.total_experience_points, 0) DESC) as global_rank
FROM public.profiles p
LEFT JOIN public.user_stats us ON p.id = us.user_id
ORDER BY COALESCE(us.total_experience_points, 0) DESC;

-- Grant appropriate permissions
GRANT SELECT ON public.leaderboard TO authenticated;
GRANT SELECT ON public.leaderboard TO anon;

-- Recreate the refresh function with proper security
CREATE OR REPLACE FUNCTION public.refresh_leaderboard()
RETURNS void 
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  REFRESH MATERIALIZED VIEW public.leaderboard;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.refresh_leaderboard() TO authenticated;
GRANT EXECUTE ON FUNCTION public.refresh_leaderboard() TO anon;

-- Test the function
SELECT public.refresh_leaderboard();

-- Verify the fix
SELECT COUNT(*) as leaderboard_entries FROM public.leaderboard;

RAISE NOTICE 'Leaderboard permissions fixed successfully';
