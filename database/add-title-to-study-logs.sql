-- Add title column to study_logs table
-- Run this in Supabase SQL Editor

-- Add title column to study_logs table
ALTER TABLE public.study_logs 
ADD COLUMN IF NOT EXISTS title TEXT NOT NULL DEFAULT 'Untitled Study Session';

-- Remove the default after adding the column
ALTER TABLE public.study_logs 
ALTER COLUMN title DROP DEFAULT;

-- Update existing records with recipe titles (if any exist)
-- This will set the title to the recipe title for existing logs
UPDATE public.study_logs 
SET title = (
  SELECT sr.title 
  FROM public.study_recipes sr 
  WHERE sr.id = study_logs.recipe_id
)
WHERE title = 'Untitled Study Session' OR title IS NULL;

-- Verify the changes
SELECT 
  sl.id,
  sl.title,
  sr.title as recipe_title,
  sl.user_id,
  sl.recipe_id
FROM public.study_logs sl
JOIN public.study_recipes sr ON sl.recipe_id = sr.id
LIMIT 5;
