// Setup bookmarks table in Supabase
const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')

// Read environment variables from .env.local
let supabaseUrl, supabaseAnonKey

try {
  const envContent = fs.readFileSync('.env.local', 'utf8')
  const envLines = envContent.split('\n')

  for (const line of envLines) {
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_URL=')) {
      supabaseUrl = line.split('=')[1].replace(/"/g, '')
    }
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_ANON_KEY=')) {
      supabaseAnonKey = line.split('=')[1].replace(/"/g, '')
    }
  }
} catch (error) {
  console.error('Error reading .env.local:', error.message)
}

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function setupBookmarksTable() {
  console.log('Setting up bookmarks table...')
  
  try {
    // Read SQL file
    const sqlContent = fs.readFileSync('create-bookmarks-table.sql', 'utf8')
    
    // Note: We can't execute raw SQL through the client library directly
    // This would need to be run through the Supabase dashboard SQL editor
    // or through a service role key with elevated permissions
    
    console.log('SQL to execute in Supabase dashboard:')
    console.log('=====================================')
    console.log(sqlContent)
    console.log('=====================================')
    
    // Test if bookmarks table exists by trying to query it
    console.log('\nTesting bookmarks table access...')
    const { data, error } = await supabase
      .from('bookmarks')
      .select('*')
      .limit(1)
    
    if (error) {
      if (error.code === '42P01') {
        console.log('❌ Bookmarks table does not exist yet')
        console.log('Please run the SQL above in your Supabase dashboard SQL editor')
      } else {
        console.log('❌ Error accessing bookmarks table:', error.message)
      }
    } else {
      console.log('✅ Bookmarks table exists and is accessible')
    }
    
  } catch (error) {
    console.error('❌ Setup failed:', error)
  }
}

setupBookmarksTable()
