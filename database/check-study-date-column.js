// Check study_date column in study_logs table
const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')

// Read environment variables from .env.local
let supabaseUrl, supabaseAnon<PERSON>ey

try {
  const envContent = fs.readFileSync('.env.local', 'utf8')
  const envLines = envContent.split('\n')

  for (const line of envLines) {
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_URL=')) {
      supabaseUrl = line.split('=')[1].replace(/"/g, '')
    }
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_ANON_KEY=')) {
      supabaseAnonKey = line.split('=')[1].replace(/"/g, '')
    }
  }
} catch (error) {
  console.error('Error reading .env.local:', error.message)
}

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function checkStudyDateColumn() {
  console.log('Checking study_date column requirement...')
  
  try {
    const testUserId = 'dbcb49be-b796-4656-90e8-62bb3a41f24d'
    const testRecipeId = 'c587acfd-f315-4f69-b713-5d501dc3ac19'
    
    console.log('\n1. Testing without study_date...')
    const { error: withoutDateError } = await supabase
      .from('study_logs')
      .insert([{
        user_id: testUserId,
        recipe_id: testRecipeId,
        title: 'Test Study Session',
        duration: 30,
        content: 'Test notes'
      }])
    
    if (withoutDateError) {
      console.log('❌ Error without study_date:', withoutDateError.message)
      
      if (withoutDateError.message.includes('study_date')) {
        console.log('🔍 study_date column is required!')
      }
    } else {
      console.log('✅ Works without study_date')
    }
    
    console.log('\n2. Testing with study_date...')
    const currentDate = new Date().toISOString().split('T')[0] // YYYY-MM-DD format
    
    const { error: withDateError } = await supabase
      .from('study_logs')
      .insert([{
        user_id: testUserId,
        recipe_id: testRecipeId,
        title: 'Test Study Session with Date',
        duration: 30,
        content: 'Test notes',
        study_date: currentDate
      }])
    
    if (withDateError) {
      if (withDateError.code === '42501') {
        console.log('✅ Works with study_date (blocked by RLS as expected)')
      } else {
        console.log('❌ Error with study_date:', withDateError.message)
      }
    } else {
      console.log('✅ Works perfectly with study_date')
    }
    
    console.log('\n3. Testing with current timestamp...')
    const { error: withTimestampError } = await supabase
      .from('study_logs')
      .insert([{
        user_id: testUserId,
        recipe_id: testRecipeId,
        title: 'Test Study Session with Timestamp',
        duration: 30,
        content: 'Test notes',
        study_date: new Date().toISOString()
      }])
    
    if (withTimestampError) {
      if (withTimestampError.code === '42501') {
        console.log('✅ Works with timestamp (blocked by RLS as expected)')
      } else {
        console.log('❌ Error with timestamp:', withTimestampError.message)
      }
    } else {
      console.log('✅ Works perfectly with timestamp')
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error)
  }
}

checkStudyDateColumn()
