// Check what duration-related columns exist in study_logs table
const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')

// Read environment variables from .env.local
let supabaseUrl, supabaseAnon<PERSON>ey

try {
  const envContent = fs.readFileSync('.env.local', 'utf8')
  const envLines = envContent.split('\n')

  for (const line of envLines) {
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_URL=')) {
      supabaseUrl = line.split('=')[1].replace(/"/g, '')
    }
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_ANON_KEY=')) {
      supabaseAnonKey = line.split('=')[1].replace(/"/g, '')
    }
  }
} catch (error) {
  console.error('Error reading .env.local:', error.message)
}

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function checkDurationColumn() {
  console.log('Checking duration-related columns in study_logs table...')
  
  try {
    // Test different column names to see which ones exist
    const testUserId = 'dbcb49be-b796-4656-90e8-62bb3a41f24d'
    const testRecipeId = 'c587acfd-f315-4f69-b713-5d501dc3ac19'
    
    console.log('\n1. Testing with duration_minutes column...')
    const { error: error1 } = await supabase
      .from('study_logs')
      .insert([{
        user_id: testUserId,
        recipe_id: testRecipeId,
        duration_minutes: 30
      }])
    
    if (error1) {
      console.log('❌ duration_minutes error:', error1.message)
    } else {
      console.log('✅ duration_minutes column exists')
    }
    
    console.log('\n2. Testing with duration column...')
    const { error: error2 } = await supabase
      .from('study_logs')
      .insert([{
        user_id: testUserId,
        recipe_id: testRecipeId,
        duration: 30
      }])
    
    if (error2) {
      console.log('❌ duration error:', error2.message)
    } else {
      console.log('✅ duration column exists')
    }
    
    console.log('\n3. Testing minimal insert to see what columns are required...')
    const { error: error3 } = await supabase
      .from('study_logs')
      .insert([{
        user_id: testUserId,
        recipe_id: testRecipeId
      }])
    
    if (error3) {
      console.log('❌ Minimal insert error:', error3.message)
      console.log('This tells us which columns are required')
    } else {
      console.log('✅ Minimal insert successful')
    }
    
    console.log('\n4. Checking existing data structure...')
    const { data, error: selectError } = await supabase
      .from('study_logs')
      .select('*')
      .limit(1)
    
    if (selectError) {
      console.log('❌ Select error:', selectError.message)
    } else {
      console.log('✅ Current table structure (sample):', data)
      if (data && data.length > 0) {
        console.log('Available columns:', Object.keys(data[0]))
      } else {
        console.log('Table is empty, cannot determine column structure from data')
      }
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error)
  }
}

checkDurationColumn()
