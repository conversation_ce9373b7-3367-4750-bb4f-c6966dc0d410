// Create bookmarks table using service role key
const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')

// Read environment variables from .env.local
let supabaseUrl, serviceRoleKey

try {
  const envContent = fs.readFileSync('.env.local', 'utf8')
  const envLines = envContent.split('\n')

  for (const line of envLines) {
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_URL=')) {
      supabaseUrl = line.split('=')[1].replace(/"/g, '')
    }
    if (line.startsWith('SUPABASE_SERVICE_ROLE_KEY=')) {
      serviceRoleKey = line.split('=')[1].replace(/"/g, '')
    }
  }
} catch (error) {
  console.error('Error reading .env.local:', error.message)
}

if (!supabaseUrl || !serviceRoleKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

// Create client with service role key for admin operations
const supabase = createClient(supabaseUrl, serviceRoleKey)

async function createBookmarksTable() {
  console.log('Creating bookmarks table with service role...')
  
  try {
    // Create bookmarks table
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS public.bookmarks (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        user_id UUID REFERENCES public.profiles(id) NOT NULL,
        recipe_id UUID REFERENCES public.study_recipes(id) ON DELETE CASCADE NOT NULL,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        UNIQUE(user_id, recipe_id)
      );
    `
    
    const { data: createResult, error: createError } = await supabase.rpc('exec_sql', {
      sql: createTableSQL
    })
    
    if (createError) {
      console.log('Trying alternative approach...')
      // Alternative: Use direct SQL execution
      const { error: altError } = await supabase
        .from('bookmarks')
        .select('*')
        .limit(1)
      
      if (altError && altError.code === '42P01') {
        console.log('Table does not exist, creating manually...')
        
        // Since we can't execute DDL directly, let's create a simple test
        console.log('Please execute this SQL in your Supabase dashboard:')
        console.log('=====================================')
        console.log(createTableSQL)
        console.log(`
-- Enable RLS
ALTER TABLE public.bookmarks ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own bookmarks" ON public.bookmarks
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own bookmarks" ON public.bookmarks
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own bookmarks" ON public.bookmarks
  FOR DELETE USING (auth.uid() = user_id);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_bookmarks_user_id ON public.bookmarks(user_id);
CREATE INDEX IF NOT EXISTS idx_bookmarks_recipe_id ON public.bookmarks(recipe_id);
CREATE INDEX IF NOT EXISTS idx_bookmarks_user_recipe ON public.bookmarks(user_id, recipe_id);
        `)
        console.log('=====================================')
        return
      }
    }
    
    console.log('✅ Table creation completed')
    
    // Test table access
    const { data, error } = await supabase
      .from('bookmarks')
      .select('*')
      .limit(1)
    
    if (error) {
      console.log('❌ Error accessing bookmarks table:', error.message)
    } else {
      console.log('✅ Bookmarks table is accessible')
    }
    
  } catch (error) {
    console.error('❌ Creation failed:', error)
  }
}

createBookmarksTable()
