// Verify study_logs table fix
const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')

// Read environment variables from .env.local
let supabaseUrl, supabaseAnonKey

try {
  const envContent = fs.readFileSync('.env.local', 'utf8')
  const envLines = envContent.split('\n')

  for (const line of envLines) {
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_URL=')) {
      supabaseUrl = line.split('=')[1].replace(/"/g, '')
    }
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_ANON_KEY=')) {
      supabaseAnonKey = line.split('=')[1].replace(/"/g, '')
    }
  }
} catch (error) {
  console.error('Error reading .env.local:', error.message)
}

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabase<PERSON>non<PERSON>ey)

async function verifyStudyLogsFix() {
  console.log('Verifying study_logs table fix...')
  
  try {
    // Test insert with all required columns
    const testUserId = 'dbcb49be-b796-4656-90e8-62bb3a41f24d'
    const testRecipeId = 'c587acfd-f315-4f69-b713-5d501dc3ac19'
    
    const { error: insertError } = await supabase
      .from('study_logs')
      .insert([{
        user_id: testUserId,
        recipe_id: testRecipeId,
        duration_minutes: 30,
        notes: 'Test study session',
        timer_type: 'stopwatch'
      }])
    
    if (insertError) {
      if (insertError.code === '42501') {
        console.log('✅ Table structure is correct (insert blocked by RLS as expected)')
      } else {
        console.log('❌ Insert error:', insertError.message)
        console.log('Error code:', insertError.code)
        
        if (insertError.message.includes('duration_minutes')) {
          console.log('❌ duration_minutes column is still missing')
        } else if (insertError.message.includes('notes')) {
          console.log('❌ notes column is still missing')
        } else if (insertError.message.includes('timer_type')) {
          console.log('❌ timer_type column is still missing')
        }
      }
    } else {
      console.log('✅ Insert successful - all columns exist')
    }
    
    // Test select to verify table structure
    const { data, error: selectError } = await supabase
      .from('study_logs')
      .select('*')
      .limit(1)
    
    if (selectError) {
      console.log('❌ Select error:', selectError.message)
    } else {
      console.log('✅ Table is accessible for select operations')
    }
    
  } catch (error) {
    console.error('❌ Verification failed:', error)
  }
}

verifyStudyLogsFix()
