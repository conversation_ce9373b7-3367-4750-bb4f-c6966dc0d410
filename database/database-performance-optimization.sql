-- ステージング環境パフォーマンス最適化用SQLスクリプト
-- 実行日: 2025年6月22日

-- 1. 基本的なインデックス追加
-- study_recipesテーブルの最適化
CREATE INDEX IF NOT EXISTS idx_study_recipes_user_id ON study_recipes(user_id);
CREATE INDEX IF NOT EXISTS idx_study_recipes_created_at ON study_recipes(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_study_recipes_upvote_count ON study_recipes(upvote_count DESC);
CREATE INDEX IF NOT EXISTS idx_study_recipes_tags ON study_recipes USING GIN(tags);

-- study_logsテーブルの最適化
CREATE INDEX IF NOT EXISTS idx_study_logs_user_id ON study_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_study_logs_recipe_id ON study_logs(recipe_id);
CREATE INDEX IF NOT EXISTS idx_study_logs_completed_at ON study_logs(completed_at DESC);

-- commentsテーブルの最適化
CREATE INDEX IF NOT EXISTS idx_comments_recipe_id ON comments(recipe_id);
CREATE INDEX IF NOT EXISTS idx_comments_log_id ON comments(log_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON comments(user_id);
CREATE INDEX IF NOT EXISTS idx_comments_parent_id ON comments(parent_id);
CREATE INDEX IF NOT EXISTS idx_comments_created_at ON comments(created_at DESC);

-- votesテーブルの最適化
CREATE INDEX IF NOT EXISTS idx_votes_recipe_id ON votes(recipe_id);
CREATE INDEX IF NOT EXISTS idx_votes_user_recipe ON votes(user_id, recipe_id);

-- bookmarksテーブルの最適化
CREATE INDEX IF NOT EXISTS idx_bookmarks_user_id ON bookmarks(user_id);
CREATE INDEX IF NOT EXISTS idx_bookmarks_recipe_id ON bookmarks(recipe_id);
CREATE INDEX IF NOT EXISTS idx_bookmarks_user_recipe ON bookmarks(user_id, recipe_id);

-- reactionsテーブルの最適化
CREATE INDEX IF NOT EXISTS idx_reactions_log_id ON reactions(log_id);
CREATE INDEX IF NOT EXISTS idx_reactions_user_log ON reactions(user_id, log_id);

-- profilesテーブルの最適化
CREATE INDEX IF NOT EXISTS idx_profiles_username ON profiles(username);
CREATE INDEX IF NOT EXISTS idx_profiles_rank_level ON profiles(rank_level DESC);
CREATE INDEX IF NOT EXISTS idx_profiles_total_study_time ON profiles(total_study_time DESC);

-- 2. 複合インデックス（よく使われるクエリパターン用）
-- レシピフィード用（ユーザー + 作成日時）
CREATE INDEX IF NOT EXISTS idx_study_recipes_user_created ON study_recipes(user_id, created_at DESC);

-- コメント取得用（レシピ + 作成日時）
CREATE INDEX IF NOT EXISTS idx_comments_recipe_created ON comments(recipe_id, created_at DESC);

-- ログ取得用（ユーザー + 完了日時）
CREATE INDEX IF NOT EXISTS idx_study_logs_user_completed ON study_logs(user_id, completed_at DESC);

-- 3. パフォーマンス分析用のビュー作成
-- レシピ統計ビュー
CREATE OR REPLACE VIEW recipe_stats AS
SELECT 
    sr.id,
    sr.title,
    sr.user_id,
    sr.created_at,
    sr.upvote_count,
    COUNT(DISTINCT c.id) as comment_count,
    COUNT(DISTINCT sl.id) as study_log_count,
    COUNT(DISTINCT b.id) as bookmark_count
FROM study_recipes sr
LEFT JOIN comments c ON sr.id = c.recipe_id
LEFT JOIN study_logs sl ON sr.id = sl.recipe_id
LEFT JOIN bookmarks b ON sr.id = b.recipe_id
GROUP BY sr.id, sr.title, sr.user_id, sr.created_at, sr.upvote_count;

-- ユーザー統計ビュー
CREATE OR REPLACE VIEW user_stats AS
SELECT 
    p.id,
    p.username,
    p.display_name,
    p.rank_level,
    p.total_study_time,
    COUNT(DISTINCT sr.id) as recipe_count,
    COUNT(DISTINCT sl.id) as study_log_count,
    COUNT(DISTINCT c.id) as comment_count,
    COUNT(DISTINCT v.id) as vote_count
FROM profiles p
LEFT JOIN study_recipes sr ON p.id = sr.user_id
LEFT JOIN study_logs sl ON p.id = sl.user_id
LEFT JOIN comments c ON p.id = c.user_id
LEFT JOIN votes v ON p.id = v.user_id
GROUP BY p.id, p.username, p.display_name, p.rank_level, p.total_study_time;

-- 4. パフォーマンス監視用の関数
-- クエリ実行時間を測定する関数
CREATE OR REPLACE FUNCTION measure_query_performance(query_text TEXT)
RETURNS TABLE(execution_time_ms NUMERIC, result_count BIGINT) AS $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    result_count_val BIGINT;
BEGIN
    start_time := clock_timestamp();
    
    EXECUTE query_text;
    GET DIAGNOSTICS result_count_val = ROW_COUNT;
    
    end_time := clock_timestamp();
    
    RETURN QUERY SELECT 
        EXTRACT(EPOCH FROM (end_time - start_time)) * 1000 as execution_time_ms,
        result_count_val as result_count;
END;
$$ LANGUAGE plpgsql;

-- 5. 統計情報の更新
-- PostgreSQLの統計情報を更新してクエリプランナーを最適化
ANALYZE study_recipes;
ANALYZE study_logs;
ANALYZE comments;
ANALYZE votes;
ANALYZE bookmarks;
ANALYZE reactions;
ANALYZE profiles;

-- 6. インデックス使用状況の確認クエリ
-- 実行後にこのクエリで効果を確認できます
/*
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;
*/

-- 7. テーブルサイズとインデックスサイズの確認
/*
SELECT 
    tablename,
    pg_size_pretty(pg_total_relation_size(tablename::regclass)) as total_size,
    pg_size_pretty(pg_relation_size(tablename::regclass)) as table_size,
    pg_size_pretty(pg_total_relation_size(tablename::regclass) - pg_relation_size(tablename::regclass)) as index_size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(tablename::regclass) DESC;
*/

-- 実行完了メッセージ
DO $$
BEGIN
    RAISE NOTICE 'データベースパフォーマンス最適化が完了しました。';
    RAISE NOTICE '作成されたインデックス数: 約20個';
    RAISE NOTICE '作成されたビュー数: 2個';
    RAISE NOTICE '統計情報の更新: 完了';
END $$;
