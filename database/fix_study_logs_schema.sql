-- Fix study_logs table schema to match the application expectations

-- Add missing columns
ALTER TABLE public.study_logs 
ADD COLUMN IF NOT EXISTS duration_minutes INTEGER,
ADD COLUMN IF NOT EXISTS notes TEXT,
ADD COLUMN IF NOT EXISTS timer_type TEXT CHECK (timer_type IN ('countdown', 'stopwatch')),
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMPTZ DEFAULT NOW();

-- Copy data from existing columns to new columns
UPDATE public.study_logs 
SET 
  duration_minutes = duration,
  notes = content,
  completed_at = study_date::timestamptz
WHERE duration_minutes IS NULL;

-- Set default timer_type for existing records
UPDATE public.study_logs 
SET timer_type = 'stopwatch' 
WHERE timer_type IS NULL;

-- Make duration_minutes NOT NULL after data migration
ALTER TABLE public.study_logs 
ALTER COLUMN duration_minutes SET NOT NULL;

-- Update foreign key constraint to reference profiles instead of auth.users
-- First, let's check if we need to update the user_id references
-- Note: This might require updating existing data if user_id values don't match profile ids
