// Script to apply database schema
const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')

// Read environment variables from .env.local
let supabaseUrl, supabaseServiceKey
try {
  const envContent = fs.readFileSync('.env.local', 'utf8')
  const envLines = envContent.split('\n')
  
  for (const line of envLines) {
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_URL=')) {
      supabaseUrl = line.split('=')[1].replace(/"/g, '').trim()
    }
    if (line.startsWith('SUPABASE_SERVICE_ROLE_KEY=')) {
      supabaseServiceKey = line.split('=')[1].replace(/"/g, '').trim()
    }
  }
} catch (error) {
  console.error('❌ Could not read .env.local file')
  process.exit(1)
}

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

// Use service role key for admin operations
const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function applySchema() {
  console.log('🔧 Applying database schema...\n')
  
  try {
    // Check if votes table exists
    console.log('1. Checking if votes table exists...')
    const { data: votesCheck, error: votesCheckError } = await supabase
      .from('votes')
      .select('*')
      .limit(1)

    if (votesCheckError && votesCheckError.code === '42P01') {
      console.log('   Votes table does not exist, creating...')
      
      // Create votes table
      const { error: createVotesError } = await supabase.rpc('exec_sql', {
        sql: `
          CREATE TABLE public.votes (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            user_id UUID REFERENCES public.profiles(id) NOT NULL,
            recipe_id UUID REFERENCES public.study_recipes(id) NOT NULL,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            UNIQUE(user_id, recipe_id)
          );
        `
      })

      if (createVotesError) {
        console.error('❌ Error creating votes table:', createVotesError)
      } else {
        console.log('✅ Votes table created successfully')
      }
    } else if (votesCheckError) {
      console.error('❌ Error checking votes table:', votesCheckError)
    } else {
      console.log('✅ Votes table already exists')
    }

    // Check if study_recipes has upvote_count column
    console.log('\n2. Checking upvote_count column in study_recipes...')
    const { data: recipesCheck, error: recipesCheckError } = await supabase
      .from('study_recipes')
      .select('upvote_count')
      .limit(1)

    if (recipesCheckError && recipesCheckError.message.includes('upvote_count')) {
      console.log('   upvote_count column does not exist, adding...')
      
      const { error: addColumnError } = await supabase.rpc('exec_sql', {
        sql: `
          ALTER TABLE public.study_recipes 
          ADD COLUMN IF NOT EXISTS upvote_count INTEGER DEFAULT 0;
        `
      })

      if (addColumnError) {
        console.error('❌ Error adding upvote_count column:', addColumnError)
      } else {
        console.log('✅ upvote_count column added successfully')
      }
    } else if (recipesCheckError) {
      console.error('❌ Error checking study_recipes table:', recipesCheckError)
    } else {
      console.log('✅ upvote_count column already exists')
    }

    // Apply RLS policies for votes
    console.log('\n3. Applying RLS policies for votes...')
    const { error: rlsError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Enable RLS on votes table
        ALTER TABLE public.votes ENABLE ROW LEVEL SECURITY;
        
        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "Public votes are viewable by everyone" ON public.votes;
        DROP POLICY IF EXISTS "Users can insert their own votes" ON public.votes;
        DROP POLICY IF EXISTS "Users can delete own votes" ON public.votes;
        
        -- Create new policies
        CREATE POLICY "Public votes are viewable by everyone" ON public.votes FOR SELECT USING (true);
        CREATE POLICY "Users can insert their own votes" ON public.votes FOR INSERT WITH CHECK (auth.uid() = user_id);
        CREATE POLICY "Users can delete own votes" ON public.votes FOR DELETE USING (auth.uid() = user_id);
      `
    })

    if (rlsError) {
      console.error('❌ Error applying RLS policies:', rlsError)
    } else {
      console.log('✅ RLS policies applied successfully')
    }

    console.log('\n🎉 Database schema applied successfully!')

  } catch (error) {
    console.error('❌ Unexpected error:', error)
  }
}

// Run the schema application
applySchema()
