{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "es6", "es2017"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "downlevelIteration": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["./*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "**/*.spec.ts", "**/*.test.ts"]}