/**
 * Debounce utility functions for performance optimization
 */

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }

    const callNow = immediate && !timeout

    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)

    if (callNow) func(...args)
  }
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false

  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

/**
 * Debounced state updater for React components
 */
export class DebouncedUpdater<T> {
  private timeout: NodeJS.Timeout | null = null
  private updateFunction: (value: T) => void

  constructor(updateFunction: (value: T) => void, private delay: number = 300) {
    this.updateFunction = updateFunction
  }

  update(value: T) {
    if (this.timeout) {
      clearTimeout(this.timeout)
    }

    this.timeout = setTimeout(() => {
      this.updateFunction(value)
      this.timeout = null
    }, this.delay)
  }

  cancel() {
    if (this.timeout) {
      clearTimeout(this.timeout)
      this.timeout = null
    }
  }

  flush(value: T) {
    this.cancel()
    this.updateFunction(value)
  }
}

/**
 * Batch multiple updates into a single operation
 */
export class BatchUpdater<T> {
  private pendingUpdates: T[] = []
  private timeout: NodeJS.Timeout | null = null
  private batchFunction: (updates: T[]) => void

  constructor(batchFunction: (updates: T[]) => void, private delay: number = 100) {
    this.batchFunction = batchFunction
  }

  add(update: T) {
    this.pendingUpdates.push(update)

    if (this.timeout) {
      clearTimeout(this.timeout)
    }

    this.timeout = setTimeout(() => {
      this.flush()
    }, this.delay)
  }

  flush() {
    if (this.pendingUpdates.length > 0) {
      const updates = [...this.pendingUpdates]
      this.pendingUpdates = []
      this.batchFunction(updates)
    }

    if (this.timeout) {
      clearTimeout(this.timeout)
      this.timeout = null
    }
  }

  cancel() {
    this.pendingUpdates = []
    if (this.timeout) {
      clearTimeout(this.timeout)
      this.timeout = null
    }
  }
}

/**
 * Rate limiter for API calls
 */
export class RateLimiter {
  private lastCall: number = 0
  private timeout: NodeJS.Timeout | null = null

  constructor(private minInterval: number) {}

  async execute<T>(fn: () => Promise<T>): Promise<T> {
    const now = Date.now()
    const timeSinceLastCall = now - this.lastCall

    if (timeSinceLastCall >= this.minInterval) {
      this.lastCall = now
      return fn()
    }

    return new Promise((resolve, reject) => {
      const delay = this.minInterval - timeSinceLastCall

      if (this.timeout) {
        clearTimeout(this.timeout)
      }

      this.timeout = setTimeout(async () => {
        try {
          this.lastCall = Date.now()
          const result = await fn()
          resolve(result)
        } catch (error) {
          reject(error)
        }
      }, delay)
    })
  }
}

/**
 * Cache with TTL (Time To Live) for expensive operations
 */
export class TTLCache<K, V> {
  private cache = new Map<K, { value: V; expiry: number }>()

  constructor(private ttl: number = 5 * 60 * 1000) {} // Default 5 minutes

  set(key: K, value: V): void {
    const expiry = Date.now() + this.ttl
    this.cache.set(key, { value, expiry })
  }

  get(key: K): V | undefined {
    const item = this.cache.get(key)
    
    if (!item) {
      return undefined
    }

    if (Date.now() > item.expiry) {
      this.cache.delete(key)
      return undefined
    }

    return item.value
  }

  has(key: K): boolean {
    return this.get(key) !== undefined
  }

  delete(key: K): boolean {
    return this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  size(): number {
    // Clean expired items first
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key)
      }
    }
    return this.cache.size
  }
}

/**
 * Optimized leaderboard update manager
 */
export class LeaderboardUpdateManager {
  private updateBatcher: BatchUpdater<any>
  private rateLimiter: RateLimiter
  private cache: TTLCache<string, any>

  constructor(
    private updateFunction: (updates: any[]) => Promise<void>,
    batchDelay: number = 1000,
    rateLimit: number = 2000,
    cacheTTL: number = 30000
  ) {
    this.updateBatcher = new BatchUpdater(
      (updates) => this.processUpdates(updates),
      batchDelay
    )
    this.rateLimiter = new RateLimiter(rateLimit)
    this.cache = new TTLCache(cacheTTL)
  }

  addUpdate(update: any) {
    this.updateBatcher.add(update)
  }

  private async processUpdates(updates: any[]) {
    try {
      await this.rateLimiter.execute(() => this.updateFunction(updates))
    } catch (error) {
      console.error('Error processing leaderboard updates:', error)
    }
  }

  getCachedData(key: string) {
    return this.cache.get(key)
  }

  setCachedData(key: string, data: any) {
    this.cache.set(key, data)
  }

  flush() {
    this.updateBatcher.flush()
  }

  cleanup() {
    this.updateBatcher.cancel()
    this.cache.clear()
  }
}
