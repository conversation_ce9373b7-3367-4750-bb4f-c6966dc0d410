import { supabase } from '../supabase'

// Adjectives for username generation
const adjectives = [
  'smart', 'clever', 'bright', 'quick', 'sharp', 'wise', 'keen', 'swift',
  'bold', 'brave', 'strong', 'mighty', 'fierce', 'noble', 'proud', 'royal',
  'calm', 'cool', 'zen', 'peaceful', 'serene', 'gentle', 'kind', 'warm',
  'happy', 'joyful', 'cheerful', 'sunny', 'bright', 'radiant', 'golden', 'silver',
  'cosmic', 'stellar', 'lunar', 'solar', 'mystic', 'magic', 'wonder', 'dream',
  'creative', 'artistic', 'musical', 'poetic', 'elegant', 'graceful', 'stylish', 'chic'
]

// Nouns for username generation
const nouns = [
  'learner', 'student', 'scholar', 'thinker', 'explorer', 'seeker', 'finder', 'creator',
  'builder', 'maker', 'designer', 'artist', 'writer', 'reader', 'teacher', 'mentor',
  'guide', 'leader', 'pioneer', 'innovator', 'inventor', 'dreamer', 'visionary', 'genius',
  'master', 'expert', 'pro', 'ace', 'champion', 'hero', 'star', 'legend',
  'warrior', 'knight', 'guardian', 'protector', 'defender', 'fighter', 'hunter', 'ranger',
  'wizard', 'sage', 'oracle', 'prophet', 'mystic', 'shaman', 'monk', 'ninja',
  'phoenix', 'dragon', 'tiger', 'eagle', 'wolf', 'lion', 'bear', 'fox',
  'ocean', 'mountain', 'forest', 'river', 'storm', 'thunder', 'lightning', 'flame'
]

/**
 * Generate a random username with adjective + noun + number pattern
 */
export function generateRandomUsername(): string {
  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)]
  const noun = nouns[Math.floor(Math.random() * nouns.length)]
  const number = Math.floor(Math.random() * 9999) + 1
  
  return `${adjective}_${noun}_${number}`
}

/**
 * Check if a username is available
 */
export async function isUsernameAvailable(username: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('username')
      .eq('username', username)
      .single()

    if (error && error.code === 'PGRST116') {
      // No rows returned, username is available
      return true
    }

    if (error) {
      console.error('Error checking username availability:', error)
      return false
    }

    // If data exists, username is taken
    return false
  } catch (error) {
    console.error('Error checking username availability:', error)
    return false
  }
}

/**
 * Generate a unique username by checking availability
 */
export async function generateUniqueUsername(maxAttempts: number = 10): Promise<string> {
  for (let i = 0; i < maxAttempts; i++) {
    const username = generateRandomUsername()
    const isAvailable = await isUsernameAvailable(username)
    
    if (isAvailable) {
      return username
    }
  }
  
  // Fallback: use timestamp-based username
  const timestamp = Date.now()
  const fallbackUsername = `user_${timestamp}`
  
  // Check if fallback is available, if not add random suffix
  const isFallbackAvailable = await isUsernameAvailable(fallbackUsername)
  if (isFallbackAvailable) {
    return fallbackUsername
  }
  
  return `${fallbackUsername}_${Math.floor(Math.random() * 999)}`
}

/**
 * Validate username format
 */
export function validateUsername(username: string): { isValid: boolean; error?: string } {
  // Username requirements
  const minLength = 3
  const maxLength = 30
  const usernameRegex = /^[a-zA-Z0-9_]+$/

  if (!username) {
    return { isValid: false, error: 'Username is required' }
  }

  if (username.length < minLength) {
    return { isValid: false, error: `Username must be at least ${minLength} characters` }
  }

  if (username.length > maxLength) {
    return { isValid: false, error: `Username must be no more than ${maxLength} characters` }
  }

  if (!usernameRegex.test(username)) {
    return { isValid: false, error: 'Username can only contain letters, numbers, and underscores' }
  }

  if (username.startsWith('_') || username.endsWith('_')) {
    return { isValid: false, error: 'Username cannot start or end with underscore' }
  }

  if (username.includes('__')) {
    return { isValid: false, error: 'Username cannot contain consecutive underscores' }
  }

  return { isValid: true }
}
