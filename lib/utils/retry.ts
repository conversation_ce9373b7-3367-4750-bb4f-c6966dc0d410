/**
 * Retry utility with exponential backoff
 * Provides automatic retry functionality for network errors and temporary failures
 */

export interface RetryOptions {
  maxAttempts?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffMultiplier?: number;
  retryCondition?: (error: any) => boolean;
  onRetry?: (attempt: number, error: any) => void;
}

const DEFAULT_OPTIONS: Required<RetryOptions> = {
  maxAttempts: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffMultiplier: 2,
  retryCondition: (error: any) => {
    // Retry for network errors and temporary failures
    if (error?.message?.includes('Failed to fetch')) return true;
    if (error?.message?.includes('NetworkError')) return true;
    if (error?.message?.includes('timeout')) return true;
    if (error?.code === 'NETWORK_ERROR') return true;
    if (error?.code === 'TIMEOUT') return true;
    // Retry for HTTP 5xx errors
    if (error?.status >= 500 && error?.status < 600) return true;
    // Retry for 429 (Too Many Requests)
    if (error?.status === 429) return true;
    return false;
  },
  onRetry: () => {}
};

/**
 * Execute retry with exponential backoff
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  let lastError: any;

  for (let attempt = 1; attempt <= opts.maxAttempts; attempt++) {
    try {
      console.log(`🔄 Retry: Attempt ${attempt}/${opts.maxAttempts}`);
      const result = await operation();
      
      if (attempt > 1) {
        console.log(`✅ Retry: Success on attempt ${attempt}`);
      }
      
      return result;
    } catch (error) {
      lastError = error;
      console.warn(`❌ Retry: Attempt ${attempt} failed:`, error);

      // Throw error if this is the last attempt or retry condition is not met
      if (attempt === opts.maxAttempts || !opts.retryCondition(error)) {
        console.error(`❌ Retry: All attempts failed or error not retryable`);
        throw error;
      }

      // Execute retry callback
      opts.onRetry(attempt, error);

      // Wait with exponential backoff
      const delay = Math.min(
        opts.baseDelay * Math.pow(opts.backoffMultiplier, attempt - 1),
        opts.maxDelay
      );
      
      console.log(`⏳ Retry: Waiting ${delay}ms before attempt ${attempt + 1}`);
      await sleep(delay);
    }
  }

  throw lastError;
}

/**
 * Network state monitoring utility
 */
export class NetworkMonitor {
  private static instance: NetworkMonitor;
  private isOnline: boolean = true;
  private listeners: Array<(isOnline: boolean) => void> = [];

  private constructor() {
    if (typeof window !== 'undefined') {
      this.isOnline = navigator.onLine;
      
      window.addEventListener('online', () => {
        console.log('🌐 Network: Connection restored');
        this.isOnline = true;
        this.notifyListeners(true);
      });

      window.addEventListener('offline', () => {
        console.log('📵 Network: Connection lost');
        this.isOnline = false;
        this.notifyListeners(false);
      });
    }
  }

  static getInstance(): NetworkMonitor {
    if (!NetworkMonitor.instance) {
      NetworkMonitor.instance = new NetworkMonitor();
    }
    return NetworkMonitor.instance;
  }

  getIsOnline(): boolean {
    return this.isOnline;
  }

  onNetworkChange(callback: (isOnline: boolean) => void): () => void {
    this.listeners.push(callback);
    
    // Return cleanup function
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private notifyListeners(isOnline: boolean): void {
    this.listeners.forEach(listener => {
      try {
        listener(isOnline);
      } catch (error) {
        console.error('Error in network change listener:', error);
      }
    });
  }
}

/**
 * Special retry configuration for Supabase operations
 */
export const supabaseRetryOptions: RetryOptions = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 8000,
  backoffMultiplier: 2,
  retryCondition: (error: any) => {
    // Network errors
    if (error?.message?.includes('Failed to fetch')) return true;
    if (error?.message?.includes('NetworkError')) return true;
    if (error?.message?.includes('timeout')) return true;
    
    // Supabase-specific errors
    if (error?.code === 'NETWORK_ERROR') return true;
    if (error?.code === 'TIMEOUT') return true;
    
    // HTTP errors
    if (error?.status >= 500) return true;
    if (error?.status === 429) return true;
    if (error?.status === 408) return true; // Request Timeout
    
    return false;
  },
  onRetry: (attempt: number, error: any) => {
    console.log(`🔄 Supabase Retry: Attempt ${attempt} failed, retrying...`, {
      error: error?.message,
      code: error?.code,
      status: error?.status
    });
  }
};

/**
 * Sleep utility function
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Execute Promise with timeout
 */
export function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  timeoutMessage: string = 'Operation timed out'
): Promise<T> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => {
      reject(new Error(`${timeoutMessage} (${timeoutMs}ms)`));
    }, timeoutMs);
  });

  return Promise.race([promise, timeoutPromise]);
}
