/**
 * Environment Configuration
 * 環境別の設定を管理するユーティリティ
 */

export type Environment = 'development' | 'staging' | 'production';

export interface EnvironmentConfig {
  isDevelopment: boolean;
  isStaging: boolean;
  isProduction: boolean;
  environment: Environment;
  appUrl: string;
  supabaseUrl: string;
  supabaseAnonKey: string;
  debugMode: boolean;
  enableTestFeatures: boolean;
}

/**
 * 現在の環境を取得
 */
export function getCurrentEnvironment(): Environment {
  const nodeEnv = process.env.NODE_ENV;
  const appEnv = process.env.NEXT_PUBLIC_APP_ENV;
  
  // NEXT_PUBLIC_APP_ENV が設定されている場合はそれを優先
  if (appEnv === 'staging') return 'staging';
  if (appEnv === 'production') return 'production';
  if (appEnv === 'development') return 'development';
  
  // NODE_ENV をフォールバック
  if (nodeEnv === 'production') return 'production';

  return 'development';
}

/**
 * 環境設定を取得
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  const environment = getCurrentEnvironment();
  
  return {
    isDevelopment: environment === 'development',
    isStaging: environment === 'staging',
    isProduction: environment === 'production',
    environment,
    appUrl: process.env.APP_URL || getDefaultAppUrl(environment),
    supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
    supabaseAnonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
    debugMode: process.env.NEXT_PUBLIC_DEBUG_MODE === 'true',
    enableTestFeatures: process.env.ENABLE_TEST_FEATURES === 'true',
  };
}

/**
 * デフォルトのアプリURLを取得
 */
function getDefaultAppUrl(environment: Environment): string {
  switch (environment) {
    case 'production':
      return 'https://studyshare.com';
    case 'staging':
      return 'https://staging.studyshare.com';
    case 'development':
    default:
      return 'http://localhost:3000';
  }
}

/**
 * 環境別のログ出力
 */
export function envLog(message: string, data?: any): void {
  const config = getEnvironmentConfig();
  
  if (config.debugMode || config.isDevelopment) {
    console.log(`[${config.environment.toUpperCase()}] ${message}`, data || '');
  }
}

/**
 * 環境別のエラーログ出力
 */
export function envError(message: string, error?: any): void {
  const config = getEnvironmentConfig();
  
  console.error(`[${config.environment.toUpperCase()}] ERROR: ${message}`, error || '');
}

/**
 * 本番環境かどうかをチェック
 */
export function isProduction(): boolean {
  return getCurrentEnvironment() === 'production';
}

/**
 * ステージング環境かどうかをチェック
 */
export function isStaging(): boolean {
  return getCurrentEnvironment() === 'staging';
}

/**
 * 開発環境かどうかをチェック
 */
export function isDevelopment(): boolean {
  return getCurrentEnvironment() === 'development';
}

/**
 * 認証リダイレクトURLを取得
 */
export function getAuthRedirectUrl(): string {
  if (typeof window !== 'undefined') {
    return `${window.location.origin}/auth/callback`;
  }

  const config = getEnvironmentConfig();
  return `${config.appUrl}/auth/callback`;
}
