import { supabase } from '@/lib/supabase'
import { StudyMaterial } from '@/types'
import { withRetry, supabaseRetryOptions } from '../utils/retry'

export interface RecipeWithMaterials {
  id: string
  user_id: string
  title: string
  estimated_duration?: string
  tips?: string
  tags: string[]
  upvote_count: number
  comment_count: number
  created_at: string
  updated_at: string
  is_bookmarked?: boolean
  is_upvoted?: boolean
  materials: StudyMaterial[]
  author: {
    username: string
    display_name: string | null
    rank_level?: number
  }
}

export const fetchStudyRecipes = async (sortBy: 'latest' | 'trending' = 'latest'): Promise<RecipeWithMaterials[]> => {
  try {
    console.log('🔄 Recipes: Fetching study recipes with sortBy:', sortBy)

    // Optimized query using JOINs to reduce N+1 problem
    let query = supabase
      .from('study_recipes')
      .select(`
        *,
        profiles!study_recipes_user_id_fkey(
          id,
          username,
          display_name,
          rank_level
        ),
        study_materials(
          id,
          material_name,
          frequency,
          time_per_session,
          time_unit,
          order_index
        ),
        comments(
          id
        )
      `)

    if (sortBy === 'trending') {
      // Sort by upvote count (trending), then by created_at for ties
      query = query.order('upvote_count', { ascending: false }).order('created_at', { ascending: false })
    } else {
      // Sort by latest (created_at)
      query = query.order('created_at', { ascending: false })
    }

    console.log('📤 Recipes: Executing optimized recipe query with JOINs')
    const { data: recipes, error: recipesError } = await query

    if (recipesError) {
      console.error('❌ Recipes: Error fetching recipes:', recipesError)
      throw recipesError
    }

    console.log('✅ Recipes: Fetched recipes count:', recipes?.length || 0)

    if (!recipes || recipes.length === 0) {
      console.log('⚠️  Recipes: No recipes found')
      return []
    }

    // Transform the data to match the expected interface
    console.log('🔄 Recipes: Transforming data for final result')

    const result = recipes.map(recipe => {
      const profile = recipe.profiles
      const materials = recipe.study_materials || []
      const commentCount = recipe.comments?.length || 0

      return {
        ...recipe,
        author: {
          username: profile?.username || 'Unknown',
          display_name: profile?.display_name || null,
          rank_level: profile?.rank_level || 1
        },
        materials: materials.sort((a: any, b: any) => a.order_index - b.order_index),
        comment_count: commentCount
      }
    })

    console.log('✅ Recipes: Successfully fetched and transformed', result.length, 'recipes')
    return result
  } catch (error) {
    console.error('❌ Recipes: Error fetching study recipes:', error)
    throw error
  }
}

export const createStudyRecipe = async (recipeData: {
  title: string
  estimated_duration?: string
  tips?: string
  tags: string[]
  materials: {
    material_name: string
    frequency: string
    time_per_session: number
    time_unit: string
  }[]
}) => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('User not authenticated')

    // Create the recipe
    const { data: recipe, error: recipeError } = await supabase
      .from('study_recipes')
      .insert([
        {
          user_id: user.id,
          title: recipeData.title,
          estimated_duration: recipeData.estimated_duration || null,
          tips: recipeData.tips || null,
          tags: recipeData.tags,
        }
      ])
      .select()
      .single()

    if (recipeError) throw recipeError

    // Create study materials if any
    if (recipeData.materials.length > 0) {
      const materialsToInsert = recipeData.materials.map((material, index) => ({
        recipe_id: recipe.id,
        material_name: material.material_name,
        frequency: material.frequency,
        time_per_session: material.time_per_session,
        time_unit: material.time_unit,
        order_index: index,
      }))

      const { error: materialsError } = await supabase
        .from('study_materials')
        .insert(materialsToInsert)

      if (materialsError) throw materialsError
    }

    return recipe
  } catch (error) {
    console.error('Error creating study recipe:', error)
    throw error
  }
}

export const upvoteRecipe = async (recipeId: string) => {
  try {
    console.log('🔄 API: Starting upvote for recipe:', recipeId)

    // Get current session
    const { data: { session } } = await supabase.auth.getSession()
    if (!session?.access_token) {
      throw new Error('User not authenticated')
    }

    console.log('✅ API: User authenticated, calling API route')

    // Use retry functionality for network resilience
    const result = await withRetry(async () => {
      // Call API route
      const response = await fetch(`/api/recipes/${recipeId}/upvote`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        const error = new Error(errorData.error || 'Failed to upvote recipe')
        // Add status for retry condition checking
        ;(error as any).status = response.status
        throw error
      }

      return await response.json()
    }, {
      ...supabaseRetryOptions,
      maxAttempts: 3,
      onRetry: (attempt, error) => {
        console.log(`🔄 upvoteRecipe retry: attempt ${attempt}`, error?.message)
      }
    })

    console.log('✅ API: Upvote result:', result)
    return result
  } catch (error) {
    console.error('❌ API: Error voting on recipe:', error)

    // Import error handling utilities
    const { captureAPIError, shouldIgnoreError } = await import('../monitoring/global-error-handler')

    // Only send non-ignored errors to Sentry
    if (!shouldIgnoreError(error)) {
      captureAPIError(error, 'upvote', 'POST')
    }

    throw error
  }
}

/**
 * Get user's upvoted recipe IDs
 */
export const getUserUpvotes = async (): Promise<string[]> => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return []

    // Use retry functionality for network resilience
    const upvotes = await withRetry(async () => {
      const { data, error } = await supabase
        .from('votes')
        .select('recipe_id')
        .eq('user_id', user.id)

      if (error) {
        // If table doesn't exist, return empty array
        if (error.code === '42P01') {
          console.warn('Votes table does not exist yet')
          return []
        }
        throw error
      }
      return data?.map(vote => vote.recipe_id) || []
    }, {
      ...supabaseRetryOptions,
      maxAttempts: 3,
      onRetry: (attempt, error) => {
        console.log(`🔄 getUserUpvotes retry: attempt ${attempt}`, error?.message)
      }
    })

    return upvotes
  } catch (error) {
    console.error('Error fetching user upvotes:', error)

    // Import error handling utilities
    const { captureAPIError, shouldIgnoreError } = await import('../monitoring/global-error-handler')

    // Only send non-ignored errors to Sentry
    if (!shouldIgnoreError(error)) {
      captureAPIError(error, 'votes', 'GET')
    }

    // Return empty array to allow UI to continue functioning
    return []
  }
}

/**
 * Check if a recipe is upvoted by the current user
 */
export const isRecipeUpvoted = async (recipeId: string): Promise<boolean> => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return false

    // Use retry functionality for network resilience
    const result = await withRetry(async () => {
      const { data, error } = await supabase
        .from('votes')
        .select('id')
        .eq('user_id', user.id)
        .eq('recipe_id', recipeId)
        .single()

      if (error && error.code !== 'PGRST116') throw error
      return !!data
    }, {
      ...supabaseRetryOptions,
      maxAttempts: 2, // Fewer retries for status checks
      onRetry: (attempt, error) => {
        console.log(`🔄 isRecipeUpvoted retry: attempt ${attempt}`, error?.message)
      }
    })

    return result
  } catch (error) {
    console.error('Error checking upvote status:', error)

    // Import error handling utilities
    const { shouldIgnoreError } = await import('../monitoring/global-error-handler')

    // Only log non-ignored errors
    if (!shouldIgnoreError(error)) {
      console.error('❌ Upvote status check failed:', error)
    }

    return false
  }
}

/**
 * @deprecated Use toggleBookmark from lib/api/bookmarks.ts instead
 * This function is kept for backward compatibility but will be removed in future versions
 */
export const bookmarkRecipe = async (recipeId: string) => {
  console.warn('⚠️ bookmarkRecipe is deprecated. Use toggleBookmark from lib/api/bookmarks.ts instead')

  try {
    // Import the new bookmark functions
    const { isRecipeBookmarked, toggleBookmark } = await import('./bookmarks')

    // Check current status and toggle
    const isCurrentlyBookmarked = await isRecipeBookmarked(recipeId)
    const newStatus = await toggleBookmark(recipeId, isCurrentlyBookmarked)

    return { bookmarked: newStatus }
  } catch (error) {
    console.error('Error bookmarking recipe:', error)
    throw error
  }
}
