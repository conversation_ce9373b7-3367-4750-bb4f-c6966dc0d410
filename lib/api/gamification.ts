// StudyShare Gamification API Functions

import {
  Achievement,
  checkAchievementEligibility
} from '@/lib/gamification/achievements'
import {
  calculateStudyExperience,
  getLevelProgress,
  getUserLevel
} from '@/lib/gamification/rankingSystem'
import { captureAPIError, captureGamificationError } from '@/lib/monitoring/global-error-handler'
import { supabase } from '@/lib/supabase'
import { supabaseRetryOptions, withRetry } from '@/lib/utils/retry'
import {
  LeaderboardEntry,
  UserStats,
  UserWithStats
} from '@/types'

// Types are now imported from @/types

/**
 * Initialize user stats when a new user is created
 */
export async function initializeUserStats(userId: string): Promise<UserStats | null> {
  try {
    const data = await withRetry(async () => {
      const { data: statsData, error } = await supabase
        .from('user_stats')
        .insert([{ user_id: userId }])
        .select()
        .single()

      if (error) {
        console.error('❌ Gamification: Error initializing user stats:', error)
        captureAPIError(error, 'user_stats', 'POST')
        throw error
      }
      return statsData
    }, supabaseRetryOptions)

    return data
  } catch (error) {
    console.error('Error initializing user stats:', error)
    captureGamificationError(error, 'initializeUserStats', userId)
    return null
  }
}

/**
 * Get user stats with profile information
 */
export async function getUserWithStats(userId: string): Promise<UserWithStats | null> {
  try {
    console.log('🔄 Gamification: Getting user with stats for:', userId)

    // Get profile and stats with retry
    const profile = await withRetry(async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('❌ Gamification: Profile error:', error)
        captureAPIError(error, 'profiles', 'GET')
        throw error
      }
      return data
    }, supabaseRetryOptions)

    console.log('✅ Gamification: Profile loaded:', profile.username)

    // Try to get user stats with retry, but handle gracefully if table doesn't exist
    let stats = null
    try {
      stats = await withRetry(async () => {
        const { data: statsData, error: statsError } = await supabase
          .from('user_stats')
          .select('*')
          .eq('user_id', userId)
          .single()

        if (statsError) {
          if (statsError.message.includes('relation "public.user_stats" does not exist')) {
            console.log('⚠️  Gamification: user_stats table does not exist, using default stats')
            return createDefaultStats(userId)
          } else if (statsError.code === 'PGRST116') {
            console.log('⚠️  Gamification: No stats found for user, creating default stats')
            return createDefaultStats(userId)
          } else {
            console.error('❌ Gamification: Stats error:', statsError)
            captureAPIError(statsError, 'user_stats', 'GET')
            throw statsError
          }
        }
        console.log('✅ Gamification: Stats loaded')
        return statsData
      }, supabaseRetryOptions)
    } catch (error) {
      console.error('❌ Gamification: Exception getting stats:', error)
      captureAPIError(error, 'user_stats', 'GET')
      stats = createDefaultStats(userId)
    }

    // Try to get user achievements with retry, but handle gracefully if table doesn't exist
    let achievementDetails = []
    try {
      achievementDetails = await withRetry(async () => {
        const { data: userAchievements, error: achievementsError } = await supabase
          .from('user_achievements')
          .select('achievement_id')
          .eq('user_id', userId)

        if (achievementsError) {
          if (achievementsError.message.includes('relation "public.user_achievements" does not exist')) {
            console.log('⚠️  Gamification: user_achievements table does not exist')
            return []
          } else {
            console.error('❌ Gamification: Achievements error:', achievementsError)
            captureAPIError(achievementsError, 'user_achievements', 'GET')
            throw achievementsError
          }
        }

        if (!userAchievements || userAchievements.length === 0) {
          return []
        }

        // Get achievement details from database with retry
        const { data: details, error: achievementDetailsError } = await supabase
          .from('achievements')
          .select('*')
          .in('id', userAchievements.map((ua: any) => ua.achievement_id))

        if (achievementDetailsError) {
          if (!achievementDetailsError.message.includes('relation "public.achievements" does not exist')) {
            console.error('❌ Gamification: Achievement details error:', achievementDetailsError)
            captureAPIError(achievementDetailsError, 'achievements', 'GET')
            throw achievementDetailsError
          }
          return []
        }

        return details || []
      }, supabaseRetryOptions)
    } catch (error) {
      console.error('❌ Gamification: Exception getting achievements:', error)
      captureAPIError(error, 'achievements', 'GET')
      achievementDetails = []
    }

    // Calculate level info
    const levelInfo = getLevelProgress(stats.total_experience_points)

    console.log('✅ Gamification: User with stats completed')
    return {
      ...profile,
      stats,
      achievements: achievementDetails,
      level_info: levelInfo
    }
  } catch (error) {
    console.error('❌ Gamification: Error getting user with stats:', error)
    captureGamificationError(error, 'getUserWithStats', userId)
    return null
  }
}

/**
 * Create default stats when user_stats table doesn't exist
 */
function createDefaultStats(userId: string) {
  return {
    id: `default-${userId}`,
    user_id: userId,
    total_study_sessions: 0,
    current_streak_days: 0,
    longest_streak_days: 0,
    last_study_date: null,
    recipes_created: 0,
    total_upvotes_received: 0,
    total_comments_received: 0,
    total_bookmarks_received: 0,
    upvotes_given: 0,
    comments_made: 0,
    bookmarks_made: 0,
    total_experience_points: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
}

/**
 * Update user stats after a study session
 */
export async function updateStatsAfterStudySession(
  userId: string,
  durationMinutes: number
): Promise<void> {
  try {
    // Calculate experience points
    const experienceGained = calculateStudyExperience(durationMinutes)

    // Get current stats with retry
    const currentStats = await withRetry(async () => {
      const { data, error } = await supabase
        .from('user_stats')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error) {
        console.error('❌ Gamification: Error fetching current stats:', error)
        captureAPIError(error, 'user_stats', 'GET')
        throw error
      }
      return data
    }, supabaseRetryOptions)

    // Calculate streak
    const today = new Date().toISOString().split('T')[0]
    const lastStudyDate = currentStats.last_study_date
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    
    let newStreak = currentStats.current_streak_days
    if (lastStudyDate === yesterday) {
      newStreak += 1
    } else if (lastStudyDate !== today) {
      newStreak = 1
    }

    // Update stats with retry
    await withRetry(async () => {
      const { error: updateError } = await supabase
        .from('user_stats')
        .update({
          total_study_sessions: currentStats.total_study_sessions + 1,
          current_streak_days: newStreak,
          longest_streak_days: Math.max(currentStats.longest_streak_days, newStreak),
          last_study_date: today,
          total_experience_points: currentStats.total_experience_points + experienceGained,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)

      if (updateError) {
        console.error('❌ Gamification: Error updating stats:', updateError)
        captureAPIError(updateError, 'user_stats', 'PATCH')
        throw updateError
      }
    }, supabaseRetryOptions)

    // Update rank level in profiles table with retry
    const newLevel = getUserLevel(currentStats.total_experience_points + experienceGained)
    await withRetry(async () => {
      const { error: profileUpdateError } = await supabase
        .from('profiles')
        .update({ rank_level: newLevel.level })
        .eq('id', userId)

      if (profileUpdateError) {
        console.error('❌ Gamification: Error updating profile rank:', profileUpdateError)
        captureAPIError(profileUpdateError, 'profiles', 'PATCH')
        throw profileUpdateError
      }
    }, supabaseRetryOptions)

    // Log activity
    await logActivity(userId, 'study_session', experienceGained, null, {
      duration_minutes: durationMinutes,
      streak_days: newStreak
    })

    // Check for new achievements
    await checkAndAwardAchievements(userId)

  } catch (error) {
    console.error('Error updating stats after study session:', error)
    captureGamificationError(error, 'updateStatsAfterStudySession', userId)
    throw error
  }
}

/**
 * Log user activity
 */
export async function logActivity(
  userId: string,
  activityType: string,
  pointsEarned: number,
  referenceId: string | null,
  metadata: any = {}
): Promise<void> {
  try {
    await withRetry(async () => {
      const { error } = await supabase
        .from('activity_log')
        .insert([{
          user_id: userId,
          activity_type: activityType,
          points_earned: pointsEarned,
          reference_id: referenceId,
          metadata
        }])

      if (error) {
        console.error('❌ Gamification: Error logging activity:', error)
        captureAPIError(error, 'activity_log', 'POST')
        throw error
      }
    }, supabaseRetryOptions)
  } catch (error) {
    console.error('Error logging activity:', error)
    // Activity logging is non-critical, so we don't re-throw the error
    captureGamificationError(error, 'logActivity', userId)
  }
}

/**
 * Check and award new achievements
 */
export async function checkAndAwardAchievements(userId: string): Promise<Achievement[]> {
  try {
    const userWithStats = await getUserWithStats(userId)
    if (!userWithStats) return []

    // Get existing achievements with retry
    const existingAchievements = await withRetry(async () => {
      const { data, error } = await supabase
        .from('user_achievements')
        .select('achievement_id')
        .eq('user_id', userId)

      if (error) {
        console.error('❌ Gamification: Error getting existing achievements:', error)
        captureAPIError(error, 'user_achievements', 'GET')
        throw error
      }
      return data || []
    }, supabaseRetryOptions)

    const existingIds = existingAchievements.map((ua: any) => ua.achievement_id)
    const newAchievements: Achievement[] = []

    // Get all achievements from database with retry
    const allAchievements = await withRetry(async () => {
      const { data, error } = await supabase
        .from('achievements')
        .select('*')

      if (error) {
        console.error('❌ Gamification: Error getting all achievements:', error)
        captureAPIError(error, 'achievements', 'GET')
        throw error
      }
      return data || []
    }, supabaseRetryOptions)

    // Check each achievement
    for (const achievement of allAchievements) {
      if (existingIds.includes(achievement.id)) continue

      if (checkAchievementEligibility(achievement, userWithStats.stats)) {
        try {
          // Award achievement with retry
          await withRetry(async () => {
            const { error: insertError } = await supabase
              .from('user_achievements')
              .insert([{
                user_id: userId,
                achievement_id: achievement.id
              }])

            if (insertError) {
              console.error('❌ Gamification: Error inserting achievement:', insertError)
              captureAPIError(insertError, 'user_achievements', 'POST')
              throw insertError
            }
          }, supabaseRetryOptions)

          newAchievements.push(achievement)

          // Award experience points with retry
          await withRetry(async () => {
            const { error: updateError } = await supabase
              .from('user_stats')
              .update({
                total_experience_points: userWithStats.stats.total_experience_points + achievement.points_reward
              })
              .eq('user_id', userId)

            if (updateError) {
              console.error('❌ Gamification: Error updating experience points:', updateError)
              captureAPIError(updateError, 'user_stats', 'PATCH')
              throw updateError
            }
          }, supabaseRetryOptions)

          // Log achievement (non-critical, don't retry)
          await logActivity(userId, 'achievement_earned', achievement.points_reward, achievement.id, {
            achievement_name: achievement.name,
            achievement_category: achievement.category
          })
        } catch (error) {
          console.error('❌ Gamification: Failed to award achievement:', achievement.name, error)
          captureAPIError(error, 'achievements', 'POST')
          // Continue with other achievements even if one fails
        }
      }
    }

    return newAchievements
  } catch (error) {
    console.error('Error checking achievements:', error)
    captureGamificationError(error, 'checkAndAwardAchievements', userId)
    return []
  }
}

/**
 * Get leaderboard
 */
export async function getLeaderboard(limit: number = 50): Promise<LeaderboardEntry[]> {
  try {
    console.log('🔄 Gamification: Getting leaderboard')

    // Try to refresh materialized view, but handle gracefully if it doesn't exist
    try {
      await withRetry(async () => {
        const { error } = await supabase.rpc('refresh_leaderboard')
        if (error) throw error
      }, supabaseRetryOptions)
      console.log('✅ Gamification: Leaderboard view refreshed')
    } catch (refreshError: any) {
      if (refreshError.message?.includes('function public.refresh_leaderboard() does not exist') ||
          refreshError.message?.includes('function public.refresh_leaderboard without parameters') ||
          refreshError.code === 'PGRST202') {
        console.log('⚠️  Gamification: refresh_leaderboard function access issue, skipping refresh')
        // Don't capture this error to Sentry as it's handled gracefully
      } else {
        console.error('❌ Gamification: Error refreshing leaderboard:', refreshError)
        captureAPIError(refreshError, 'refresh_leaderboard', 'RPC')
      }
    }

    // Try to get leaderboard data with retry
    const data = await withRetry(async () => {
      const { data: leaderboardData, error } = await supabase
        .from('leaderboard')
        .select('*')
        .limit(limit)

      if (error) {
        if (error.message.includes('relation "public.leaderboard" does not exist')) {
          console.log('⚠️  Gamification: leaderboard view does not exist, returning empty array')
          return []
        }
        console.error('❌ Gamification: Error getting leaderboard:', error)
        captureAPIError(error, 'leaderboard', 'GET')
        throw error
      }

      return leaderboardData || []
    }, supabaseRetryOptions)

    console.log('✅ Gamification: Leaderboard loaded:', data.length, 'entries')
    return data
  } catch (error) {
    console.error('❌ Gamification: Error getting leaderboard:', error)
    captureAPIError(error, 'leaderboard', 'GET')
    return []
  }
}

/**
 * Get user's rank on leaderboard
 */
export async function getUserRank(userId: string): Promise<number | null> {
  try {
    console.log('🔄 Gamification: Getting user rank for:', userId)

    const data = await withRetry(async () => {
      const { data: rankData, error } = await supabase
        .from('leaderboard')
        .select('global_rank')
        .eq('id', userId)
        .single()

      if (error) {
        if (error.message.includes('relation "public.leaderboard" does not exist')) {
          console.log('⚠️  Gamification: leaderboard view does not exist, returning null rank')
          return null
        }
        if (error.code === 'PGRST116') {
          console.log('⚠️  Gamification: User not found in leaderboard')
          return null
        }
        console.error('❌ Gamification: Error getting user rank:', error)
        captureAPIError(error, 'leaderboard', 'GET')
        throw error
      }

      return rankData
    }, supabaseRetryOptions)

    console.log('✅ Gamification: User rank loaded:', data?.global_rank)
    return data?.global_rank || null
  } catch (error) {
    console.error('❌ Gamification: Error getting user rank:', error)
    captureAPIError(error, 'leaderboard', 'GET')
    return null
  }
}
