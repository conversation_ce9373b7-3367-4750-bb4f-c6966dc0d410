import { supabase } from '../supabase'
import { StudyCategory, UserStudyCategory } from '../../types'

/**
 * Fetch all active study categories
 */
export const fetchStudyCategories = async (): Promise<StudyCategory[]> => {
  try {
    const { data, error } = await supabase
      .from('study_categories')
      .select('*')
      .eq('is_active', true)
      .order('sort_order', { ascending: true })

    if (error) throw error

    return data || []
  } catch (error) {
    console.error('Error fetching study categories:', error)
    throw error
  }
}

/**
 * Fetch user's selected study categories
 */
export const fetchUserStudyCategories = async (userId: string): Promise<UserStudyCategory[]> => {
  try {
    const { data, error } = await supabase
      .from('user_study_categories')
      .select(`
        *,
        category:study_categories(*)
      `)
      .eq('user_id', userId)

    if (error) throw error

    return data || []
  } catch (error) {
    console.error('Error fetching user study categories:', error)
    throw error
  }
}

/**
 * Update user's study categories (replace all existing selections)
 */
export const updateUserStudyCategories = async (categoryIds: string[]): Promise<void> => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('User not authenticated')

    // First, delete all existing user study categories
    const { error: deleteError } = await supabase
      .from('user_study_categories')
      .delete()
      .eq('user_id', user.id)

    if (deleteError) throw deleteError

    // Then, insert new selections
    if (categoryIds.length > 0) {
      const insertData = categoryIds.map(categoryId => ({
        user_id: user.id,
        category_id: categoryId
      }))

      const { error: insertError } = await supabase
        .from('user_study_categories')
        .insert(insertData)

      if (insertError) throw insertError
    }
  } catch (error) {
    console.error('Error updating user study categories:', error)
    throw error
  }
}

/**
 * Add a study category to user's selections
 */
export const addUserStudyCategory = async (categoryId: string): Promise<void> => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('User not authenticated')

    const { error } = await supabase
      .from('user_study_categories')
      .insert({
        user_id: user.id,
        category_id: categoryId
      })

    if (error) throw error
  } catch (error) {
    console.error('Error adding user study category:', error)
    throw error
  }
}

/**
 * Remove a study category from user's selections
 */
export const removeUserStudyCategory = async (categoryId: string): Promise<void> => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('User not authenticated')

    const { error } = await supabase
      .from('user_study_categories')
      .delete()
      .eq('user_id', user.id)
      .eq('category_id', categoryId)

    if (error) throw error
  } catch (error) {
    console.error('Error removing user study category:', error)
    throw error
  }
}

/**
 * Check if user has selected any study categories
 */
export const hasUserStudyCategories = async (userId: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('user_study_categories')
      .select('id')
      .eq('user_id', userId)
      .limit(1)

    if (error) throw error

    return (data?.length || 0) > 0
  } catch (error) {
    console.error('Error checking user study categories:', error)
    return false
  }
}
