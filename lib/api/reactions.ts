import { supabase } from '@/lib/supabase'
import { Reaction, ReactionSummary, LogReactions } from '@/types'

// Available emoji reactions (limited to 8 for better UI)
export const AVAILABLE_EMOJIS = [
  { emoji: '👏', label: 'Great job!' },
  { emoji: '🔥', label: 'Keep it up!' },
  { emoji: '🎉', label: 'Amazing!' },
  { emoji: '❤️', label: 'Love it!' },
  { emoji: '👍', label: 'Good work!' },
  { emoji: '💪', label: 'Strong!' },
  { emoji: '🚀', label: 'Awesome!' },
  { emoji: '💯', label: 'Perfect!' },
]

export const addReaction = async (logId: string, emoji: string) => {
  try {
    console.log('🔄 API: Starting add reaction for log:', logId, 'emoji:', emoji)

    // Get current session
    const { data: { session } } = await supabase.auth.getSession()
    if (!session?.access_token) {
      throw new Error('User not authenticated')
    }

    console.log('✅ API: User authenticated, calling API route')

    // Call API route
    const response = await fetch(`/api/logs/${logId}/reactions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ emoji, action: 'add' })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to add reaction')
    }

    const result = await response.json()
    console.log('✅ API: Reaction added successfully:', result)
    return result

  } catch (error) {
    console.error('❌ API: Error adding reaction:', error)
    throw error
  }
}

export const removeReaction = async (logId: string, emoji: string) => {
  try {
    console.log('🔄 API: Starting remove reaction for log:', logId, 'emoji:', emoji)

    // Get current session
    const { data: { session } } = await supabase.auth.getSession()
    if (!session?.access_token) {
      throw new Error('User not authenticated')
    }

    console.log('✅ API: User authenticated, calling API route')

    // Call API route
    const response = await fetch(`/api/logs/${logId}/reactions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ emoji, action: 'remove' })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to remove reaction')
    }

    const result = await response.json()
    console.log('✅ API: Reaction removed successfully:', result)
    return result

  } catch (error) {
    console.error('❌ API: Error removing reaction:', error)
    throw error
  }
}

export const getLogReactions = async (logId: string): Promise<LogReactions> => {
  try {
    const { data: reactions, error } = await supabase
      .from('reactions')
      .select('emoji, user_id')
      .eq('log_id', logId)

    if (error) throw error

    // Group reactions by emoji and count them
    const reactionMap = new Map<string, ReactionSummary>()
    const userReactions: string[] = []
    const { data: { user } } = await supabase.auth.getUser()

    reactions?.forEach(reaction => {
      const { emoji, user_id } = reaction
      
      // Track user's own reactions
      if (user && user_id === user.id) {
        userReactions.push(emoji)
      }

      // Group by emoji
      if (reactionMap.has(emoji)) {
        const existing = reactionMap.get(emoji)!
        existing.count += 1
        existing.users.push(user_id)
      } else {
        reactionMap.set(emoji, {
          emoji,
          count: 1,
          users: [user_id]
        })
      }
    })

    return {
      reactions: Array.from(reactionMap.values()),
      userReactions
    }

  } catch (error) {
    console.error('❌ Error fetching log reactions:', error)
    return { reactions: [], userReactions: [] }
  }
}
