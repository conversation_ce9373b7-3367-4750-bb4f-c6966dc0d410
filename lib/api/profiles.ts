import { supabase } from '@/lib/supabase'
import { Profile } from '@/types'
import { RecipeWithMaterials } from './recipes'
import { fetchUserStudyLogs, StudyLogWithRecipe } from './studyLogs'

/**
 * Fetch user profile by username
 */
export const fetchProfileByUsername = async (username: string): Promise<Profile | null> => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('username', username)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        // Profile not found
        return null
      }
      // Convert Supabase error to proper Error instance
      const profileError = new Error(`Failed to fetch profile: ${error.message}`)
      profileError.name = 'ProfileFetchError'
      throw profileError
    }

    return data
  } catch (error) {
    console.error('Error fetching profile by username:', error)
    
    // Convert Supabase error objects to proper Error instances
    if (error && typeof error === 'object' && 'code' in error) {
      const supabaseError = error as any
      const convertedError = new Error(`Supabase Error: ${supabaseError.message}`)
      convertedError.name = `SupabaseError_${supabaseError.code}`
      throw convertedError
    }
    
    throw error
  }
}

/**
 * Fetch user's study recipes
 */
export const fetchUserRecipes = async (userId: string, limit = 20, offset = 0): Promise<RecipeWithMaterials[]> => {
  try {
    const { data: recipes, error } = await supabase
      .from('study_recipes')
      .select(`
        *,
        study_materials(*)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      // Convert Supabase error to proper Error instance
      const recipesError = new Error(`Failed to fetch user recipes: ${error.message}`)
      recipesError.name = 'UserRecipesFetchError'
      throw recipesError
    }

    if (!recipes || recipes.length === 0) {
      return []
    }

    // Get user profile for the author
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, username, display_name, rank_level')
      .eq('id', userId)
      .single()

    if (profileError) {
      const convertedProfileError = new Error(`Failed to fetch profile for recipes: ${profileError.message}`)
      convertedProfileError.name = 'ProfileForRecipesFetchError'
      throw convertedProfileError
    }

    // Get comment counts for all recipes
    const recipeIds = recipes.map(recipe => recipe.id)
    const { data: commentCounts, error: commentError } = await supabase
      .from('comments')
      .select('recipe_id')
      .in('recipe_id', recipeIds)

    if (commentError) {
      console.warn('Error fetching comment counts:', commentError)
    }

    // Count comments per recipe
    const commentCountMap = new Map<string, number>()
    if (commentCounts) {
      commentCounts.forEach(comment => {
        const count = commentCountMap.get(comment.recipe_id) || 0
        commentCountMap.set(comment.recipe_id, count + 1)
      })
    }

    // Transform the data to match RecipeWithMaterials format
    return recipes.map(recipe => ({
      ...recipe,
      author: {
        username: profile?.username || 'Unknown',
        display_name: profile?.display_name || null,
        rank_level: profile?.rank_level || 1
      },
      materials: recipe.study_materials || [],
      comment_count: commentCountMap.get(recipe.id) || 0
    }))
  } catch (error) {
    console.error('Error fetching user recipes:', error)
    
    // Convert Supabase error objects to proper Error instances
    if (error && typeof error === 'object' && 'code' in error) {
      const supabaseError = error as any
      const convertedError = new Error(`Supabase Error: ${supabaseError.message}`)
      convertedError.name = `SupabaseError_${supabaseError.code}`
      throw convertedError
    }
    
    throw error
  }
}

/**
 * Fetch user's study logs
 */
export const fetchUserLogs = async (userId: string, limit = 20, offset = 0): Promise<StudyLogWithRecipe[]> => {
  return fetchUserStudyLogs(userId, limit, offset)
}

/**
 * Fetch user's upvoted recipes
 */
export const fetchUserUpvotedRecipes = async (userId: string, limit = 20, offset = 0): Promise<RecipeWithMaterials[]> => {
  try {
    // First get the recipe IDs from votes
    const { data: votes, error: votesError } = await supabase
      .from('votes')
      .select('recipe_id')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (votesError) {
      const convertedVotesError = new Error(`Failed to fetch user votes: ${votesError.message}`)
      convertedVotesError.name = 'UserVotesFetchError'
      throw convertedVotesError
    }

    if (!votes || votes.length === 0) {
      return []
    }

    const recipeIds = votes.map(vote => vote.recipe_id)

    // Then get the full recipe data
    const { data: recipes, error: recipesError } = await supabase
      .from('study_recipes')
      .select(`
        *,
        comment_count,
        study_materials(*)
      `)
      .in('id', recipeIds)

    if (recipesError) {
      const convertedRecipesError = new Error(`Failed to fetch upvoted recipes: ${recipesError.message}`)
      convertedRecipesError.name = 'UpvotedRecipesFetchError'
      throw convertedRecipesError
    }

    if (!recipes || recipes.length === 0) {
      return []
    }

    // Get user profiles for all recipes
    const userIds = [...new Set(recipes.map(recipe => recipe.user_id))]
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, username, display_name')
      .in('id', userIds)

    if (profilesError) {
      const convertedProfilesError = new Error(`Failed to fetch profiles for upvoted recipes: ${profilesError.message}`)
      convertedProfilesError.name = 'ProfilesForUpvotedRecipesFetchError'
      throw convertedProfilesError
    }

    // Transform the data to match RecipeWithMaterials format
    return recipes.map(recipe => {
      const profile = profiles?.find(p => p.id === recipe.user_id)
      return {
        ...recipe,
        author: {
          username: profile?.username || 'Unknown',
          display_name: profile?.display_name || null
        },
        materials: recipe.study_materials || []
      }
    })
  } catch (error) {
    console.error('Error fetching user upvoted recipes:', error)

    // Convert Supabase error objects to proper Error instances
    if (error && typeof error === 'object' && 'code' in error) {
      const supabaseError = error as any
      const convertedError = new Error(`Supabase Error: ${supabaseError.message}`)
      convertedError.name = `SupabaseError_${supabaseError.code}`
      throw convertedError
    }

    // Send critical database errors to Sentry
    if (error && typeof error === 'object' && 'message' in error) {
      const errorMessage = (error as any).message
      if (errorMessage.includes('does not exist') || errorMessage.includes('column')) {
        // Import Sentry dynamically to avoid SSR issues
        import('@sentry/nextjs').then(Sentry => {
          Sentry.captureException(error, {
            tags: {
              component: 'fetchUserUpvotedRecipes',
              errorType: 'database_schema_error'
            },
            extra: {
              function: 'fetchUserUpvotedRecipes',
              errorMessage,
              userId: userId
            }
          })
        })
      }
    }

    throw error
  }
}

/**
 * Fetch user's bookmarked recipes
 */
export const fetchUserBookmarkedRecipes = async (userId: string, limit = 20, offset = 0): Promise<RecipeWithMaterials[]> => {
  try {
    // First get the recipe IDs from bookmarks
    const { data: bookmarks, error: bookmarksError } = await supabase
      .from('bookmarks')
      .select('recipe_id')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (bookmarksError) {
      const convertedBookmarksError = new Error(`Failed to fetch user bookmarks: ${bookmarksError.message}`)
      convertedBookmarksError.name = 'UserBookmarksFetchError'
      throw convertedBookmarksError
    }

    if (!bookmarks || bookmarks.length === 0) {
      return []
    }

    const recipeIds = bookmarks.map(bookmark => bookmark.recipe_id)

    // Then get the full recipe data
    const { data: recipes, error: recipesError } = await supabase
      .from('study_recipes')
      .select(`
        *,
        comment_count,
        study_materials(*)
      `)
      .in('id', recipeIds)

    if (recipesError) {
      const convertedRecipesError = new Error(`Failed to fetch bookmarked recipes: ${recipesError.message}`)
      convertedRecipesError.name = 'BookmarkedRecipesFetchError'
      throw convertedRecipesError
    }

    if (!recipes || recipes.length === 0) {
      return []
    }

    // Get user profiles for all recipes
    const userIds = [...new Set(recipes.map(recipe => recipe.user_id))]
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, username, display_name')
      .in('id', userIds)

    if (profilesError) {
      const convertedProfilesError = new Error(`Failed to fetch profiles for bookmarked recipes: ${profilesError.message}`)
      convertedProfilesError.name = 'ProfilesForBookmarkedRecipesFetchError'
      throw convertedProfilesError
    }

    // Transform the data to match RecipeWithMaterials format
    return recipes.map(recipe => {
      const profile = profiles?.find(p => p.id === recipe.user_id)
      return {
        ...recipe,
        author: {
          username: profile?.username || 'Unknown',
          display_name: profile?.display_name || null
        },
        materials: recipe.study_materials || []
      }
    })
  } catch (error) {
    console.error('Error fetching user bookmarked recipes:', error)
    
    // Convert Supabase error objects to proper Error instances
    if (error && typeof error === 'object' && 'code' in error) {
      const supabaseError = error as any
      const convertedError = new Error(`Supabase Error: ${supabaseError.message}`)
      convertedError.name = `SupabaseError_${supabaseError.code}`
      throw convertedError
    }
    
    throw error
  }
}

/**
 * Update user profile
 */
export const updateProfile = async (profileData: {
  display_name?: string
  bio?: string
  username?: string
}): Promise<Profile> => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('User not authenticated')

    // Prepare update data, filtering out undefined values
    const updateData: any = {
      updated_at: new Date().toISOString()
    }

    if (profileData.display_name !== undefined) {
      updateData.display_name = profileData.display_name
    }
    if (profileData.bio !== undefined) {
      updateData.bio = profileData.bio || null
    }
    if (profileData.username !== undefined) {
      updateData.username = profileData.username
    }

    const { data, error } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', user.id)
      .select()
      .single()

    if (error) {
      // Convert Supabase error to proper Error instance
      const updateError = new Error(`Failed to update profile: ${error.message}`)
      updateError.name = 'ProfileUpdateError'
      throw updateError
    }
    
    return data
  } catch (error) {
    console.error('Error updating profile:', error)
    
    // Convert Supabase error objects to proper Error instances
    if (error && typeof error === 'object' && 'code' in error) {
      const supabaseError = error as any
      const convertedError = new Error(`Supabase Error: ${supabaseError.message}`)
      convertedError.name = `SupabaseError_${supabaseError.code}`
      throw convertedError
    }
    
    throw error
  }
}

/**
 * Check if username is available
 */
export const checkUsernameAvailability = async (username: string, currentUserId?: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id')
      .eq('username', username)
      .single()

    if (error && error.code === 'PGRST116') {
      // Username not found, so it's available
      return true
    }

    if (error) {
      // Convert Supabase error to proper Error instance
      const availabilityError = new Error(`Failed to check username availability: ${error.message}`)
      availabilityError.name = 'UsernameAvailabilityCheckError'
      throw availabilityError
    }

    // If username exists but belongs to current user, it's available
    return data.id === currentUserId
  } catch (error) {
    console.error('Error checking username availability:', error)
    
    // Convert Supabase error objects to proper Error instances
    if (error && typeof error === 'object' && 'code' in error) {
      const supabaseError = error as any
      const convertedError = new Error(`Supabase Error: ${supabaseError.message}`)
      convertedError.name = `SupabaseError_${supabaseError.code}`
      throw convertedError
    }
    
    throw error
  }
}





