import { supabase } from '@/lib/supabase'
import { supabaseRetryOptions, withRetry } from '@/lib/utils/retry'
import { useCallback, useEffect, useState } from 'react'

export interface Bookmark {
  id: string
  user_id: string
  recipe_id: string
  created_at: string
}

// ローカルキャッシュでブックマーク状態を管理
class BookmarkCache {
  private static instance: BookmarkCache
  private cache = new Set<string>()
  private pendingOperations = new Map<string, 'add' | 'remove'>()

  static getInstance(): BookmarkCache {
    if (!BookmarkCache.instance) {
      BookmarkCache.instance = new BookmarkCache()
    }
    return BookmarkCache.instance
  }

  // Optimistic update: immediately update cache
  optimisticAdd(recipeId: string): void {
    this.cache.add(recipeId)
    this.pendingOperations.set(recipeId, 'add')
    console.log('📦 Optimistic update: bookmark added', recipeId)
  }

  optimisticRemove(recipeId: string): void {
    this.cache.delete(recipeId)
    this.pendingOperations.set(recipeId, 'remove')
    console.log('📦 Optimistic update: bookmark removed', recipeId)
  }

  // Clear pending state when operation completes
  confirmOperation(recipeId: string): void {
    this.pendingOperations.delete(recipeId)
    console.log('✅ Operation confirmed: bookmark operation completed', recipeId)
  }

  // Rollback on operation failure
  rollbackOperation(recipeId: string): void {
    const operation = this.pendingOperations.get(recipeId)
    if (operation === 'add') {
      this.cache.delete(recipeId)
      console.log('🔄 Rollback: bookmark addition cancelled', recipeId)
    } else if (operation === 'remove') {
      this.cache.add(recipeId)
      console.log('🔄 Rollback: bookmark removal cancelled', recipeId)
    }
    this.pendingOperations.delete(recipeId)
  }

  has(recipeId: string): boolean {
    return this.cache.has(recipeId)
  }

  getAll(): string[] {
    return Array.from(this.cache)
  }

  isPending(recipeId: string): boolean {
    return this.pendingOperations.has(recipeId)
  }

  // Sync cache with server data
  sync(serverBookmarks: string[]): void {
    this.cache.clear()
    serverBookmarks.forEach(id => this.cache.add(id))
    console.log('🔄 Cache sync completed:', serverBookmarks.length, 'items')
  }
}

/**
 * Add a bookmark for a recipe with guaranteed display
 */
export const addBookmark = async (recipeId: string): Promise<void> => {
  const cache = BookmarkCache.getInstance()

  try {
    // 1. 楽観的更新: 即座にUIに反映
    cache.optimisticAdd(recipeId)

    // 2. 確実なサーバー更新
    await withRetry(async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const { error } = await supabase
        .from('bookmarks')
        .insert([
          {
            user_id: user.id,
            recipe_id: recipeId
          }
        ])

      if (error) {
        // Treat duplicate errors as success
        if (error.code === '23505') {
          console.log('✅ Bookmark: already exists (duplicate error)')
          return
        }
        throw error
      }
    }, {
      ...supabaseRetryOptions,
      maxAttempts: 5, // More retries for reliability
      onRetry: (attempt, error) => {
        console.log(`🔄 Bookmark add retry: attempt ${attempt}`, error?.message)
      }
    })

    // 3. Confirm operation completion
    cache.confirmOperation(recipeId)
    console.log('✅ Bookmark added successfully:', recipeId)

  } catch (error) {
    // 4. Rollback on failure
    cache.rollbackOperation(recipeId)
    console.error('❌ Bookmark add failed:', error)

    // Sentryに詳細報告
    import('@sentry/nextjs').then(Sentry => {
      Sentry.captureException(error, {
        tags: {
          component: 'BookmarksAPI',
          errorType: 'add_bookmark_error'
        },
        extra: {
          function: 'addBookmark',
          recipeId,
          errorMessage: error instanceof Error ? error.message : String(error)
        }
      })
    })

    throw new Error('Failed to add bookmark. Please check your network connection.')
  }
}

/**
 * Remove a bookmark for a recipe with guaranteed removal
 */
export const removeBookmark = async (recipeId: string): Promise<void> => {
  const cache = BookmarkCache.getInstance()

  try {
    // 1. 楽観的更新: 即座にUIから削除
    cache.optimisticRemove(recipeId)

    // 2. 確実なサーバー更新
    await withRetry(async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const { error } = await supabase
        .from('bookmarks')
        .delete()
        .eq('user_id', user.id)
        .eq('recipe_id', recipeId)

      if (error) throw error
    }, {
      ...supabaseRetryOptions,
      maxAttempts: 5,
      onRetry: (attempt, error) => {
        console.log(`🔄 Bookmark remove retry: attempt ${attempt}`, error?.message)
      }
    })

    // 3. Confirm operation completion
    cache.confirmOperation(recipeId)
    console.log('✅ Bookmark removed successfully:', recipeId)

  } catch (error) {
    // 4. Rollback on failure
    cache.rollbackOperation(recipeId)
    console.error('❌ Bookmark remove failed:', error)

    // Sentryに詳細報告
    import('@sentry/nextjs').then(Sentry => {
      Sentry.captureException(error, {
        tags: {
          component: 'BookmarksAPI',
          errorType: 'remove_bookmark_error'
        },
        extra: {
          function: 'removeBookmark',
          recipeId,
          errorMessage: error instanceof Error ? error.message : String(error)
        }
      })
    })

    throw new Error('Failed to remove bookmark. Please check your network connection.')
  }
}

/**
 * Toggle bookmark status for a recipe (backward compatible)
 */
export const toggleBookmark = async (recipeId: string, isBookmarked: boolean): Promise<boolean> => {
  const cache = BookmarkCache.getInstance()

  try {
    if (isBookmarked) {
      await removeBookmark(recipeId)
      // 既存コンポーネントとの互換性のため、実際のキャッシュ状態を確認
      return !cache.has(recipeId)
    } else {
      await addBookmark(recipeId)
      // 既存コンポーネントとの互換性のため、実際のキャッシュ状態を確認
      return cache.has(recipeId)
    }
  } catch (error) {
    console.error('Error toggling bookmark:', error)
    // エラー時は楽観的更新がロールバックされているので、キャッシュ状態を返す
    return cache.has(recipeId)
  }
}

/**
 * Get user's bookmarked recipe IDs with guaranteed availability
 */
export const getUserBookmarks = async (): Promise<string[]> => {
  const cache = BookmarkCache.getInstance()

  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return []

    // サーバーからデータを取得（リトライ付き）
    const serverBookmarks = await withRetry(async () => {
      const { data, error } = await supabase
        .from('bookmarks')
        .select('recipe_id')
        .eq('user_id', user.id)

      if (error) {
        // If table doesn't exist, return empty array
        if (error.code === '42P01') {
          console.warn('Bookmarks table does not exist yet')
          return []
        }
        throw error
      }
      return data?.map(bookmark => bookmark.recipe_id) || []
    }, {
      ...supabaseRetryOptions,
      maxAttempts: 3,
      onRetry: (attempt, error) => {
        console.log(`🔄 Bookmark fetch retry: attempt ${attempt}`, error?.message)
      }
    })

    // Sync cache
    cache.sync(serverBookmarks)
    console.log('✅ Bookmarks fetched successfully:', serverBookmarks.length, 'items')
    return serverBookmarks

  } catch (error) {
    console.error('❌ Bookmark fetch failed, returning from cache:', error)

    // Sentryに詳細報告
    import('@sentry/nextjs').then(Sentry => {
      Sentry.captureException(error, {
        tags: {
          component: 'BookmarksAPI',
          errorType: 'fetch_bookmarks_error'
        },
        extra: {
          function: 'getUserBookmarks',
          errorMessage: error instanceof Error ? error.message : String(error),
          cacheSize: cache.getAll().length
        }
      })
    })

    // Return from cache on network error
    const cachedBookmarksCount = cache.getAll().length
    console.log(`📦 Returning ${cachedBookmarksCount} bookmarks from cache`)
    return cache.getAll()
  }
}

/**
 * Get bookmarked recipes with full recipe data (with cache integration)
 */
export const getBookmarkedRecipes = async () => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return []

    // まずキャッシュからブックマークIDを取得
    const bookmarkedIds = await getUserBookmarks()
    if (bookmarkedIds.length === 0) return []

    // Get full recipe data with retry
    const recipes = await withRetry(async () => {
      const { data, error } = await supabase
        .from('study_recipes')
        .select('*')
        .in('id', bookmarkedIds)

      if (error) throw error
      return data || []
    }, supabaseRetryOptions)

    if (recipes.length === 0) return []

    // Get user profiles for recipes
    const userIds = [...new Set(recipes.map(r => r.user_id))]
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, username, display_name')
      .in('id', userIds)

    if (profilesError) throw profilesError

    // Get materials for recipes
    const { data: materials, error: materialsError } = await supabase
      .from('study_materials')
      .select('*')
      .in('recipe_id', bookmarkedIds)
      .order('order_index')

    if (materialsError) throw materialsError

    // Combine data
    return recipes.map(recipe => {
      const profile = profiles?.find(p => p.id === recipe.user_id)
      const recipeMaterials = materials?.filter(m => m.recipe_id === recipe.id) || []

      return {
        ...recipe,
        author: {
          username: profile?.username || 'Unknown',
          display_name: profile?.display_name || null
        },
        materials: recipeMaterials.sort((a, b) => a.order_index - b.order_index)
      }
    })
  } catch (error) {
    console.error('Error fetching bookmarked recipes:', error)
    return []
  }
}

/**
 * Check if a recipe is bookmarked by the current user with cache priority
 */
export const isRecipeBookmarked = async (recipeId: string): Promise<boolean> => {
  const cache = BookmarkCache.getInstance()

  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return false

    // First check cache (immediate response)
    const cachedResult = cache.has(recipeId)

    // Trust cache value if there's a pending operation
    if (cache.isPending(recipeId)) {
      console.log('📦 Pending operation, returning from cache:', recipeId, cachedResult)
      return cachedResult
    }

    // Background server check (return cache value even on error)
    try {
      const { data, error } = await supabase
        .from('bookmarks')
        .select('id')
        .eq('user_id', user.id)
        .eq('recipe_id', recipeId)
        .single()

      const serverResult = !!data && !error

      // Sync cache if server result differs from cache
      if (serverResult !== cachedResult) {
        if (serverResult) {
          cache.optimisticAdd(recipeId)
          cache.confirmOperation(recipeId)
        } else {
          cache.optimisticRemove(recipeId)
          cache.confirmOperation(recipeId)
        }
        console.log('🔄 Cache sync:', recipeId, 'server:', serverResult)
      }

      return serverResult
    } catch (serverError) {
      // Return cache value on server error
      console.log('📦 Server error, returning from cache:', recipeId, cachedResult)
      return cachedResult
    }
  } catch (error) {
    console.error('Error checking bookmark status:', error)
    return cache.has(recipeId) // Return from cache even on error
  }
}

/**
 * React Hook for bookmark management with guaranteed display
 */
export const useBookmarks = () => {
  const [bookmarks, setBookmarks] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const cache = BookmarkCache.getInstance()

  // Fetch bookmarks list
  const fetchBookmarks = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // First display from cache immediately
      const cachedBookmarks = cache.getAll()
      if (cachedBookmarks.length > 0) {
        setBookmarks(cachedBookmarks)
        setLoading(false)
      }

      // Fetch latest data from server
      const serverBookmarks = await getUserBookmarks()
      setBookmarks(serverBookmarks)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch bookmarks'
      setError(errorMessage)

      // Display from cache even on error
      const cachedBookmarks = cache.getAll()
      setBookmarks(cachedBookmarks)
    } finally {
      setLoading(false)
    }
  }, [cache])

  // Add bookmark (optimistic update)
  const addBookmarkOptimistic = useCallback(async (recipeId: string) => {
    try {
      await addBookmark(recipeId)
      // Cache is auto-updated, sync state
      setBookmarks(cache.getAll())
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add bookmark'
      setError(errorMessage)
      // Reflect state after rollback
      setBookmarks(cache.getAll())
    }
  }, [cache])

  // Remove bookmark (optimistic update)
  const removeBookmarkOptimistic = useCallback(async (recipeId: string) => {
    try {
      await removeBookmark(recipeId)
      // Cache is auto-updated, sync state
      setBookmarks(cache.getAll())
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove bookmark'
      setError(errorMessage)
      // Reflect state after rollback
      setBookmarks(cache.getAll())
    }
  }, [cache])

  // Check bookmark status
  const checkBookmarkStatus = useCallback(async (recipeId: string): Promise<boolean> => {
    return await isRecipeBookmarked(recipeId)
  }, [])

  // Initial load
  useEffect(() => {
    fetchBookmarks()
  }, [fetchBookmarks])

  return {
    bookmarks,
    loading,
    error,
    addBookmark: addBookmarkOptimistic,
    removeBookmark: removeBookmarkOptimistic,
    checkBookmarkStatus,
    refetch: fetchBookmarks,
    clearError: () => setError(null)
  }
}
