import { supabase } from '@/lib/supabase'
import { Comment } from '@/types'

/**
 * Add a comment to a recipe or study log
 */
export const addComment = async (
  content: string,
  recipeId?: string,
  logId?: string,
  parentId?: string
): Promise<Comment> => {
  try {
    // Validate inputs
    if (!content || content.trim().length === 0) {
      throw new Error('Comment content is required')
    }

    if (!recipeId && !logId) {
      throw new Error('Either recipeId or logId must be provided')
    }

    // Get current session
    const { data: { session } } = await supabase.auth.getSession()
    if (!session?.access_token) {
      throw new Error('User not authenticated')
    }

    // Determine the API endpoint
    const endpoint = recipeId
      ? `/api/recipes/${recipeId}/comments`
      : `/api/logs/${logId}/comments`

    // Call API route
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ content, parentId })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to add comment')
    }

    const result = await response.json()
    return result.comment
  } catch (error) {
    console.error('Error adding comment:', error)

    // Send API errors to Sentry
    import('@sentry/nextjs').then(Sentry => {
      Sentry.captureException(error, {
        tags: {
          component: 'CommentsAPI',
          errorType: 'add_comment_error'
        },
        extra: {
          function: 'addComment',
          recipeId,
          logId,
          parentId,
          contentLength: content?.length
        }
      })
    })

    throw error
  }
}

/**
 * Get comments for a recipe
 */
export const getRecipeComments = async (recipeId: string): Promise<Comment[]> => {
  try {
    // Get all comments for this recipe in one query (including replies)
    const { data: allComments, error: allError } = await supabase
      .from('comments')
      .select(`
        *,
        author:profiles(id, username, display_name),
        parent_comment:comments!parent_id(
          author:profiles(id, username, display_name)
        )
      `)
      .eq('recipe_id', recipeId)
      .order('created_at', { ascending: true })

    if (allError) throw allError

    // Organize comments into a tree structure
    const commentsMap = new Map<string, Comment>()
    const topLevelComments: Comment[] = []

    // First pass: create all comment objects
    allComments?.forEach(comment => {
      commentsMap.set(comment.id, { ...comment, replies: [] })
    })

    // Second pass: organize into tree structure
    allComments?.forEach(comment => {
      const commentObj = commentsMap.get(comment.id)!

      if (comment.parent_id) {
        // This is a reply
        const parentComment = commentsMap.get(comment.parent_id)
        if (parentComment) {
          parentComment.replies = parentComment.replies || []
          parentComment.replies.push(commentObj)
        }
      } else {
        // This is a top-level comment
        topLevelComments.push(commentObj)
      }
    })

    return topLevelComments
  } catch (error) {
    console.error('Error fetching recipe comments:', error)
    throw error
  }
}

/**
 * Get comments for a study log
 */
export const getLogComments = async (logId: string): Promise<Comment[]> => {
  try {
    // Get all comments for this log in one query (including replies)
    const { data: allComments, error: allError } = await supabase
      .from('comments')
      .select(`
        *,
        author:profiles(id, username, display_name),
        parent_comment:comments!parent_id(
          author:profiles(id, username, display_name)
        )
      `)
      .eq('log_id', logId)
      .order('created_at', { ascending: true })

    if (allError) throw allError

    // Organize comments into a tree structure
    const commentsMap = new Map<string, Comment>()
    const topLevelComments: Comment[] = []

    // First pass: create all comment objects
    allComments?.forEach(comment => {
      commentsMap.set(comment.id, { ...comment, replies: [] })
    })

    // Second pass: organize into tree structure
    allComments?.forEach(comment => {
      const commentObj = commentsMap.get(comment.id)!

      if (comment.parent_id) {
        // This is a reply
        const parentComment = commentsMap.get(comment.parent_id)
        if (parentComment) {
          parentComment.replies = parentComment.replies || []
          parentComment.replies.push(commentObj)
        }
      } else {
        // This is a top-level comment
        topLevelComments.push(commentObj)
      }
    })

    return topLevelComments
  } catch (error) {
    console.error('Error fetching log comments:', error)
    throw error
  }
}



/**
 * Delete a comment
 */
export const deleteComment = async (commentId: string): Promise<void> => {
  try {
    // Validate input
    if (!commentId || commentId.trim().length === 0) {
      throw new Error('Comment ID is required')
    }

    // Get current session
    const { data: { session } } = await supabase.auth.getSession()
    if (!session?.access_token) {
      throw new Error('User not authenticated')
    }

    // Call API route
    const response = await fetch(`/api/comments/${commentId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))

      const errorMessage = errorData.details
        ? `${errorData.error}: ${errorData.details}`
        : errorData.error || 'Failed to delete comment'

      throw new Error(errorMessage)
    }
  } catch (error) {
    console.error('Error deleting comment:', error)

    // Send delete errors to Sentry with more context
    import('@sentry/nextjs').then(Sentry => {
      Sentry.captureException(error, {
        tags: {
          component: 'CommentsAPI',
          errorType: 'delete_comment_error'
        },
        extra: {
          function: 'deleteComment',
          commentId,
          errorMessage: error instanceof Error ? error.message : String(error)
        }
      })
    })

    throw error
  }
}

/**
 * Update a comment
 */
export const updateComment = async (commentId: string, content: string): Promise<Comment> => {
  try {
    // Get current session
    const { data: { session } } = await supabase.auth.getSession()
    if (!session?.access_token) {
      throw new Error('User not authenticated')
    }

    // Call API route
    const response = await fetch(`/api/comments/${commentId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ content })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to update comment')
    }

    const result = await response.json()
    return result.comment
  } catch (error) {
    console.error('Error updating comment:', error)

    // Send update errors to Sentry
    import('@sentry/nextjs').then(Sentry => {
      Sentry.captureException(error, {
        tags: {
          component: 'CommentsAPI',
          errorType: 'update_comment_error'
        },
        extra: {
          function: 'updateComment',
          commentId,
          contentLength: content?.length
        }
      })
    })

    throw error
  }
}

/**
 * Parse mentions from comment content
 */
export const parseMentions = (content: string): string[] => {
  const mentionRegex = /@(\w+)/g
  const mentions: string[] = []
  let match

  while ((match = mentionRegex.exec(content)) !== null) {
    mentions.push(match[1])
  }

  return [...new Set(mentions)] // Remove duplicates
}

/**
 * Search users for mention autocomplete
 */
export const searchUsersForMention = async (query: string): Promise<{ username: string; display_name?: string }[]> => {
  try {
    if (query.length < 2) return []

    const { data, error } = await supabase
      .from('profiles')
      .select('username, display_name')
      .ilike('username', `%${query}%`)
      .limit(10)

    if (error) throw error

    return data || []
  } catch (error) {
    console.error('❌ API: Error searching users for mention:', error)
    return []
  }
}
