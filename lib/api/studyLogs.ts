import { supabase } from '@/lib/supabase'

export interface StudyLog {
  id: string
  user_id: string
  recipe_id: string
  material_id?: string
  title: string
  duration_minutes: number
  duration_seconds: number
  notes?: string
  timer_type: 'countdown' | 'stopwatch'
  completed_at: string
  recipe?: {
    id: string
    title: string
    tags: string[]
  }
  material?: {
    id: string
    material_name: string
    frequency: string
    time_per_session: number
    time_unit: string
  }
  author?: {
    id: string
    username: string
    display_name: string | null
    rank_level?: number
  }
}

export interface StudyLogWithRecipe extends StudyLog {
  title: string
  recipe: {
    id: string
    title: string
    tags: string[]
  }
  material?: {
    id: string
    material_name: string
    frequency: string
    time_per_session: number
    time_unit: string
  }
  author: {
    id: string
    username: string
    display_name: string | null
    rank_level?: number
  }
  comment_count: number
}

export const createStudyLog = async (logData: {
  recipe_id: string
  material_id: string | null
  title: string
  duration_minutes: number
  duration_seconds: number
  notes?: string
  timer_type: 'countdown' | 'stopwatch'
}) => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('User not authenticated')

    const { data, error } = await supabase
      .from('study_logs')
      .insert([
        {
          user_id: user.id,
          ...logData
        }
      ])
      .select()
      .single()

    if (error) throw error

    // Temporarily disable gamification updates to avoid RLS issues
    // TODO: Fix RLS policies for user_stats table
    // try {
    //   await updateStatsAfterStudySession(user.id, logData.duration_minutes)
    // } catch (gamificationError) {
    //   console.error('Error updating gamification stats:', gamificationError)
    //   // Don't throw error here - study log was created successfully
    // }

    return data
  } catch (error) {
    console.error('Error creating study log:', error)
    throw error
  }
}

export const fetchStudyLogs = async (limit = 20, offset = 0): Promise<StudyLogWithRecipe[]> => {
  try {
    // Optimized query using JOINs to reduce N+1 problem
    const { data: logsData, error: logsError } = await supabase
      .from('study_logs')
      .select(`
        *,
        study_recipes(
          id,
          title,
          tags
        ),
        study_materials(
          id,
          material_name,
          frequency,
          time_per_session,
          time_unit
        ),
        profiles!study_logs_user_id_fkey(
          id,
          username,
          display_name,
          rank_level
        ),
        comments(
          id
        )
      `)
      .order('completed_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (logsError) throw logsError

    if (!logsData || logsData.length === 0) {
      return []
    }

    // Transform the data to match the expected interface
    const transformedData = logsData.map(log => ({
      ...log,
      recipe: log.study_recipes,
      material: log.study_materials,
      author: log.profiles || {
        id: log.user_id,
        username: 'Unknown User',
        display_name: null,
        rank_level: 1
      },
      comment_count: log.comments?.length || 0
    }))

    return transformedData
  } catch (error) {
    console.error('Error fetching study logs:', error)
    throw error
  }
}

export const fetchUserStudyLogs = async (userId: string, limit = 20, offset = 0): Promise<StudyLogWithRecipe[]> => {
  try {
    // Optimized query using JOINs to reduce N+1 problem
    const { data: logsData, error: logsError } = await supabase
      .from('study_logs')
      .select(`
        *,
        study_recipes(
          id,
          title,
          tags
        ),
        study_materials(
          id,
          material_name,
          frequency,
          time_per_session,
          time_unit
        ),
        profiles!study_logs_user_id_fkey(
          id,
          username,
          display_name,
          rank_level
        ),
        comments(
          id
        )
      `)
      .eq('user_id', userId)
      .order('completed_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (logsError) throw logsError

    if (!logsData || logsData.length === 0) {
      return []
    }

    // Transform the data to match the expected interface
    const transformedData = logsData.map(log => ({
      ...log,
      recipe: log.study_recipes,
      material: log.study_materials,
      author: log.profiles || {
        id: userId,
        username: 'Unknown User',
        display_name: null,
        rank_level: 1
      },
      comment_count: log.comments?.length || 0
    }))

    return transformedData
  } catch (error) {
    console.error('Error fetching user study logs:', error)
    throw error
  }
}

export const deleteStudyLog = async (logId: string) => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('User not authenticated')

    const { error } = await supabase
      .from('study_logs')
      .delete()
      .eq('id', logId)
      .eq('user_id', user.id) // Ensure user can only delete their own logs

    if (error) throw error
  } catch (error) {
    console.error('Error deleting study log:', error)
    throw error
  }
}

export const updateStudyLog = async (logId: string, updates: {
  title?: string
  duration_minutes?: number
  notes?: string
}) => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('User not authenticated')

    const { data, error } = await supabase
      .from('study_logs')
      .update(updates)
      .eq('id', logId)
      .eq('user_id', user.id) // Ensure user can only update their own logs
      .select()
      .single()

    if (error) throw error
    return data
  } catch (error) {
    console.error('Error updating study log:', error)
    throw error
  }
}

// Get study statistics for a user
export const getUserStudyStats = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from('study_logs')
      .select('duration_minutes, completed_at')
      .eq('user_id', userId)

    if (error) throw error

    const logs = data || []
    const totalMinutes = logs.reduce((sum, log) => sum + log.duration_minutes, 0)
    const totalSessions = logs.length
    
    // Calculate streak (consecutive days with study sessions)
    const today = new Date()
    const dates = logs.map(log => new Date(log.completed_at).toDateString())
    const uniqueDates = [...new Set(dates)].sort()
    
    let currentStreak = 0
    for (let i = 0; i < uniqueDates.length; i++) {
      const date = new Date(uniqueDates[uniqueDates.length - 1 - i])
      const expectedDate = new Date(today)
      expectedDate.setDate(today.getDate() - i)
      
      if (date.toDateString() === expectedDate.toDateString()) {
        currentStreak++
      } else {
        break
      }
    }

    return {
      totalMinutes,
      totalHours: Math.floor(totalMinutes / 60),
      totalSessions,
      currentStreak,
      averageSessionLength: totalSessions > 0 ? Math.round(totalMinutes / totalSessions) : 0
    }
  } catch (error) {
    console.error('Error fetching user study stats:', error)
    throw error
  }
}
