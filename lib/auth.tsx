import { Profile } from '@/types'
import { Session, User } from '@supabase/supabase-js'
import { createContext, useContext, useEffect, useState } from 'react'
import { supabase } from './supabase'

interface AuthContextType {
  user: User | null
  profile: Profile | null
  session: Session | null
  loading: boolean
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  profile: null,
  session: null,
  loading: true,
  signOut: async () => {},
})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [fetchingProfile, setFetchingProfile] = useState(false)

  // Retry utility function with exponential backoff
  const retryWithBackoff = async <T,>(
    operation: () => Promise<T>,
    options: {
      maxAttempts: number
      baseDelay: number
      maxDelay: number
      shouldRetry: (error: any) => boolean
    }
  ): Promise<T> => {
    let lastError: any

    for (let attempt = 1; attempt <= options.maxAttempts; attempt++) {
      try {
        console.log(`🔄 Retry: Attempt ${attempt}/${options.maxAttempts}`)
        const result = await operation()
        
        if (attempt > 1) {
          console.log(`✅ Retry: Success on attempt ${attempt}`)
        }
        
        return result
      } catch (error) {
        lastError = error
        console.warn(`❌ Retry: Attempt ${attempt} failed:`, error)

        // Throw error if this is the last attempt or retry condition is not met
        if (attempt === options.maxAttempts || !options.shouldRetry(error)) {
          console.error(`❌ Retry: All attempts failed or error not retryable`)
          throw error
        }

        // Wait with exponential backoff
        const delay = Math.min(
          options.baseDelay * Math.pow(2, attempt - 1),
          options.maxDelay
        )
        
        console.log(`⏳ Retry: Waiting ${delay}ms before attempt ${attempt + 1}`)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    throw lastError
  }

  const createProfile = async (userId: string) => {
    try {
      console.log('🔄 Auth: Creating profile for userId:', userId)

      const { data: userData, error: userError } = await supabase.auth.getUser()

      if (userError || !userData.user) {
        console.error('❌ Auth: Error getting user for profile creation:', userError)
        return
      }

      const user = userData.user

      // Import username generation utility
      const { generateUniqueUsername } = await import('../utils/username')

      // Generate unique username if not provided
      let username = user.user_metadata?.username
      if (!username) {
        if (user.email) {
          const emailUsername = user.email.split('@')[0]
          // Check if email-based username is available
          const { isUsernameAvailable } = await import('../utils/username')
          const isAvailable = await isUsernameAvailable(emailUsername)
          username = isAvailable ? emailUsername : await generateUniqueUsername()
        } else {
          username = await generateUniqueUsername()
        }
      }

      const displayName = user.user_metadata?.display_name ||
                         user.user_metadata?.full_name ||
                         username

      console.log('🔄 Auth: Creating profile with username:', username)

      const { data, error } = await supabase
        .from('profiles')
        .insert([
          {
            id: userId,
            username,
            display_name: displayName,
            bio: null,
            avatar_url: user.user_metadata?.avatar_url || null,
            nationality: null,
          }
        ])
        .select()
        .single()

      if (error) {
        console.error('❌ Auth: Error creating profile:', error)
      } else {
        console.log('✅ Auth: Profile created successfully:', data?.username)
        setProfile(data)
      }
    } catch (error) {
      console.error('❌ Auth: Error in createProfile:', error)
    }
  }

  const fetchProfile = async (userId: string) => {
    // Prevent duplicate profile fetching
    if (fetchingProfile) {
      console.log('⏳ Auth: Profile fetch already in progress, skipping...')
      return
    }

    try {
      setFetchingProfile(true)
      console.log('🔄 Auth: Fetching profile for userId:', userId)

      // Fetch profile with retry functionality
      const result = await retryWithBackoff(
        async () => {
          console.log('🔄 Auth: Attempting to fetch profile...')
          
          // Fetch profile with timeout
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Profile fetch timeout (15s)')), 15000)
          )

          const profilePromise = supabase
            .from('profiles')
            .select('*')
            .eq('id', userId)
            .single()

          return Promise.race([profilePromise, timeoutPromise])
        },
        {
          maxAttempts: 3,
          baseDelay: 1000,
          maxDelay: 8000,
          shouldRetry: (error: any) => {
            // Retry for network errors and temporary failures
            if (error?.message?.includes('Failed to fetch')) return true
            if (error?.message?.includes('NetworkError')) return true
            if (error?.message?.includes('timeout')) return true
            if (error?.code === 'NETWORK_ERROR') return true
            if (error?.status >= 500) return true
            if (error?.status === 429) return true
            if (error?.status === 408) return true
            return false
          }
        }
      )

      const { data, error } = result as any

      if (error) {
        console.error('❌ Auth: Error fetching profile after retries:', error)
        // If profile doesn't exist, create one
        if (error.code === 'PGRST116') {
          console.log('🔄 Auth: Profile not found, creating new profile...')
          await createProfile(userId)
        } else {
          console.error('❌ Auth: Profile fetch error:', error)
          // For timeout or other errors, don't block the app
          console.log('⚠️ Auth: Continuing without profile due to error')
        }
      } else {
        console.log('✅ Auth: Profile fetched successfully:', data?.username)
        setProfile(data)
      }
    } catch (error) {
      console.error('❌ Auth: Error in fetchProfile:', error)
      console.log('⚠️ Auth: Continuing without profile due to error')
    } finally {
      setFetchingProfile(false)
    }
  }

  useEffect(() => {
    let mounted = true

    // Get initial session with enhanced error handling
    const getInitialSession = async () => {
      try {
        console.log('🔄 Auth: Getting initial session...')

        // Debug: Check localStorage for existing session
        if (typeof window !== 'undefined') {
          const localStorageKeys = Object.keys(localStorage).filter(key =>
            key.includes('supabase') || key.includes('auth')
          )
          console.log('🔍 Auth: LocalStorage keys:', localStorageKeys)
        }

        const { data: { session }, error } = await supabase.auth.getSession()

        if (error) {
          console.error('❌ Auth: Error getting session:', error)
          if (mounted) {
            setLoading(false)
          }
          return
        }

        console.log('✅ Auth: Initial session retrieved:', {
          hasSession: !!session,
          hasUser: !!session?.user,
          userId: session?.user?.id,
          email: session?.user?.email,
          expiresAt: session?.expires_at
        })

        if (mounted) {
          setSession(session)
          setUser(session?.user ?? null)

          if (session?.user) {
            console.log('🔄 Auth: Fetching profile for user:', session.user.id)
            // Fetch profile asynchronously without blocking initial load
            fetchProfile(session.user.id).finally(() => {
              if (mounted) setLoading(false)
            })
          } else {
            console.log('ℹ️ Auth: No user in session')
            setLoading(false)
          }
        }
      } catch (error) {
        console.error('❌ Auth: Unexpected error in getInitialSession:', error)
        if (mounted) {
          setLoading(false)
        }
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 Auth: Auth state changed:', event, !!session)

        if (mounted) {
          setSession(session)
          setUser(session?.user ?? null)

          if (session?.user) {
            // Fetch profile for the authenticated user
            fetchProfile(session.user.id)
          } else {
            setProfile(null)
          }

          setLoading(false)
        }
      }
    )

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, []) // Empty dependency array to prevent infinite loops

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const value = {
    user,
    profile,
    session,
    loading,
    signOut,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
