// StudyShare Ranking System - Experience Points Calculation

export interface ExperiencePoints {
  study: number
  community: number
  consistency: number
  quality: number
}

export interface UserActivity {
  studySessionMinutes: number
  recipesCreated: number
  upvotesReceived: number
  commentsReceived: number
  bookmarksReceived: number
  upvotesGiven: number
  commentsMade: number
  currentStreak: number
  longestStreak: number
}

// Experience Points Configuration
export const EXPERIENCE_CONFIG = {
  // Study Activities
  STUDY_SESSION_BASE: 10, // Base points per study session
  STUDY_MINUTE_MULTIPLIER: 0.5, // Points per minute studied
  STUDY_SESSION_BONUS_THRESHOLD: 60, // Bonus for sessions over 1 hour
  STUDY_SESSION_BONUS: 20,

  // Community Activities
  RECIPE_CREATED: 50, // Points for creating a recipe
  UPVOTE_RECEIVED: 5, // Points when someone upvotes your content
  COMMENT_RECEIVED: 3, // Points when someone comments on your content
  BOOKMARK_RECEIVED: 8, // Points when someone bookmarks your recipe
  UPVOTE_GIVEN: 1, // Points for engaging with community
  COMMENT_MADE: 2, // Points for making thoughtful comments

  // Consistency Bonuses
  STREAK_DAILY_BONUS: 5, // Bonus per day in current streak
  STREAK_WEEKLY_BONUS: 50, // Bonus for 7-day streak
  STREAK_MONTHLY_BONUS: 200, // Bonus for 30-day streak

  // Quality Multipliers
  POPULAR_RECIPE_THRESHOLD: 10, // Upvotes needed for "popular" status
  POPULAR_RECIPE_MULTIPLIER: 1.5, // Multiplier for popular recipes
  VIRAL_RECIPE_THRESHOLD: 50, // Upvotes needed for "viral" status
  VIRAL_RECIPE_MULTIPLIER: 2.0, // Multiplier for viral recipes
} as const

// Level System Configuration
export const LEVEL_THRESHOLDS = [
  { level: 1, name: "Beginner", minXP: 0, maxXP: 99, color: "#6B7280" },
  { level: 2, name: "Learner", minXP: 100, maxXP: 299, color: "#6B7280" },
  { level: 3, name: "Student", minXP: 300, maxXP: 599, color: "#6B7280" },
  { level: 4, name: "Scholar", minXP: 600, maxXP: 999, color: "#374151" },
  { level: 5, name: "Dedicated", minXP: 1000, maxXP: 1599, color: "#374151" },
  { level: 6, name: "Committed", minXP: 1600, maxXP: 2499, color: "#374151" },
  { level: 7, name: "Expert", minXP: 2500, maxXP: 3999, color: "#1F2937" },
  { level: 8, name: "Master", minXP: 4000, maxXP: 6499, color: "#1F2937" },
  { level: 9, name: "Guru", minXP: 6500, maxXP: 9999, color: "#111827" },
  { level: 10, name: "Legend", minXP: 10000, maxXP: null, color: "#000000" },
] as const

/**
 * Calculate experience points for a study session
 */
export function calculateStudyExperience(durationMinutes: number): number {
  let points = EXPERIENCE_CONFIG.STUDY_SESSION_BASE
  points += durationMinutes * EXPERIENCE_CONFIG.STUDY_MINUTE_MULTIPLIER
  
  // Bonus for longer sessions
  if (durationMinutes >= EXPERIENCE_CONFIG.STUDY_SESSION_BONUS_THRESHOLD) {
    points += EXPERIENCE_CONFIG.STUDY_SESSION_BONUS
  }
  
  return Math.floor(points)
}

/**
 * Calculate experience points for community activities
 */
export function calculateCommunityExperience(activity: Partial<UserActivity>): number {
  let points = 0
  
  if (activity.recipesCreated) {
    points += activity.recipesCreated * EXPERIENCE_CONFIG.RECIPE_CREATED
  }
  
  if (activity.upvotesReceived) {
    points += activity.upvotesReceived * EXPERIENCE_CONFIG.UPVOTE_RECEIVED
  }
  
  if (activity.commentsReceived) {
    points += activity.commentsReceived * EXPERIENCE_CONFIG.COMMENT_RECEIVED
  }
  
  if (activity.bookmarksReceived) {
    points += activity.bookmarksReceived * EXPERIENCE_CONFIG.BOOKMARK_RECEIVED
  }
  
  if (activity.upvotesGiven) {
    points += activity.upvotesGiven * EXPERIENCE_CONFIG.UPVOTE_GIVEN
  }
  
  if (activity.commentsMade) {
    points += activity.commentsMade * EXPERIENCE_CONFIG.COMMENT_MADE
  }
  
  return Math.floor(points)
}

/**
 * Calculate consistency bonus points
 */
export function calculateConsistencyBonus(currentStreak: number, longestStreak: number): number {
  let points = 0
  
  // Daily streak bonus
  points += currentStreak * EXPERIENCE_CONFIG.STREAK_DAILY_BONUS
  
  // Weekly streak bonuses
  const weeklyBonuses = Math.floor(currentStreak / 7)
  points += weeklyBonuses * EXPERIENCE_CONFIG.STREAK_WEEKLY_BONUS
  
  // Monthly streak bonuses
  const monthlyBonuses = Math.floor(currentStreak / 30)
  points += monthlyBonuses * EXPERIENCE_CONFIG.STREAK_MONTHLY_BONUS
  
  return Math.floor(points)
}

/**
 * Calculate quality multiplier for recipes
 */
export function calculateQualityMultiplier(upvoteCount: number): number {
  if (upvoteCount >= EXPERIENCE_CONFIG.VIRAL_RECIPE_THRESHOLD) {
    return EXPERIENCE_CONFIG.VIRAL_RECIPE_MULTIPLIER
  } else if (upvoteCount >= EXPERIENCE_CONFIG.POPULAR_RECIPE_THRESHOLD) {
    return EXPERIENCE_CONFIG.POPULAR_RECIPE_MULTIPLIER
  }
  return 1.0
}

/**
 * Get user level based on experience points
 */
export function getUserLevel(experiencePoints: number): typeof LEVEL_THRESHOLDS[number] {
  for (let i = LEVEL_THRESHOLDS.length - 1; i >= 0; i--) {
    const level = LEVEL_THRESHOLDS[i]
    if (experiencePoints >= level.minXP) {
      return level
    }
  }
  return LEVEL_THRESHOLDS[0] // Default to level 1
}

/**
 * Calculate progress to next level
 */
export function getLevelProgress(experiencePoints: number): {
  currentLevel: typeof LEVEL_THRESHOLDS[number]
  nextLevel: typeof LEVEL_THRESHOLDS[number] | null
  progress: number // 0-100 percentage
  pointsToNext: number
} {
  const currentLevel = getUserLevel(experiencePoints)
  const nextLevelIndex = LEVEL_THRESHOLDS.findIndex(l => l.level === currentLevel.level) + 1
  const nextLevel = nextLevelIndex < LEVEL_THRESHOLDS.length ? LEVEL_THRESHOLDS[nextLevelIndex] : null
  
  if (!nextLevel) {
    return {
      currentLevel,
      nextLevel: null,
      progress: 100,
      pointsToNext: 0
    }
  }
  
  const pointsInCurrentLevel = experiencePoints - currentLevel.minXP
  const pointsNeededForNext = nextLevel.minXP - currentLevel.minXP
  const progress = Math.floor((pointsInCurrentLevel / pointsNeededForNext) * 100)
  const pointsToNext = nextLevel.minXP - experiencePoints
  
  return {
    currentLevel,
    nextLevel,
    progress,
    pointsToNext
  }
}

/**
 * Calculate total experience points for a user
 */
export function calculateTotalExperience(activity: UserActivity): ExperiencePoints {
  const study = calculateStudyExperience(activity.studySessionMinutes)
  const community = calculateCommunityExperience(activity)
  const consistency = calculateConsistencyBonus(activity.currentStreak, activity.longestStreak)
  
  // Apply quality multipliers to community points
  const qualityMultiplier = calculateQualityMultiplier(activity.upvotesReceived)
  const quality = Math.floor(community * (qualityMultiplier - 1))
  
  return {
    study,
    community,
    consistency,
    quality
  }
}
