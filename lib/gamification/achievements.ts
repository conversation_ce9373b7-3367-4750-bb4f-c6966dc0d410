// StudyShare Achievement System

export interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  category: 'study' | 'community' | 'consistency' | 'milestone'
  requirementType: string
  requirementValue: number
  pointsReward: number
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
}

// Predefined Achievements
export const ACHIEVEMENTS: Achievement[] = [
  // Study Achievements
  {
    id: 'first-study',
    name: 'First Steps',
    description: 'Complete your first study session',
    icon: '🎯',
    category: 'study',
    requirementType: 'study_sessions',
    requirementValue: 1,
    pointsReward: 50,
    rarity: 'common'
  },
  {
    id: 'study-10-sessions',
    name: 'Getting Started',
    description: 'Complete 10 study sessions',
    icon: '📚',
    category: 'study',
    requirementType: 'study_sessions',
    requirementValue: 10,
    pointsReward: 100,
    rarity: 'common'
  },
  {
    id: 'study-50-sessions',
    name: 'Dedicated Learner',
    description: 'Complete 50 study sessions',
    icon: '🎓',
    category: 'study',
    requirementType: 'study_sessions',
    requirementValue: 50,
    pointsReward: 300,
    rarity: 'rare'
  },
  {
    id: 'study-100-sessions',
    name: 'Study Master',
    description: 'Complete 100 study sessions',
    icon: '👑',
    category: 'study',
    requirementType: 'study_sessions',
    requirementValue: 100,
    pointsReward: 500,
    rarity: 'epic'
  },
  {
    id: 'marathon-session',
    name: 'Marathon Learner',
    description: 'Complete a 3-hour study session',
    icon: '⏰',
    category: 'study',
    requirementType: 'single_session_minutes',
    requirementValue: 180,
    pointsReward: 200,
    rarity: 'rare'
  },
  {
    id: 'study-time-10h',
    name: 'Time Investor',
    description: 'Study for a total of 10 hours',
    icon: '⏱️',
    category: 'study',
    requirementType: 'total_study_time',
    requirementValue: 600, // 10 hours in minutes
    pointsReward: 150,
    rarity: 'common'
  },
  {
    id: 'study-time-100h',
    name: 'Committed Scholar',
    description: 'Study for a total of 100 hours',
    icon: '🔥',
    category: 'study',
    requirementType: 'total_study_time',
    requirementValue: 6000, // 100 hours in minutes
    pointsReward: 800,
    rarity: 'epic'
  },

  // Consistency Achievements
  {
    id: 'streak-3',
    name: 'Building Habits',
    description: 'Study for 3 consecutive days',
    icon: '🌱',
    category: 'consistency',
    requirementType: 'current_streak',
    requirementValue: 3,
    pointsReward: 75,
    rarity: 'common'
  },
  {
    id: 'streak-7',
    name: 'Week Warrior',
    description: 'Study for 7 consecutive days',
    icon: '🗓️',
    category: 'consistency',
    requirementType: 'current_streak',
    requirementValue: 7,
    pointsReward: 200,
    rarity: 'rare'
  },
  {
    id: 'streak-30',
    name: 'Monthly Master',
    description: 'Study for 30 consecutive days',
    icon: '📅',
    category: 'consistency',
    requirementType: 'current_streak',
    requirementValue: 30,
    pointsReward: 1000,
    rarity: 'epic'
  },
  {
    id: 'streak-100',
    name: 'Consistency Legend',
    description: 'Study for 100 consecutive days',
    icon: '🏆',
    category: 'consistency',
    requirementType: 'current_streak',
    requirementValue: 100,
    pointsReward: 2500,
    rarity: 'legendary'
  },

  // Community Achievements
  {
    id: 'first-recipe',
    name: 'Recipe Creator',
    description: 'Create your first study recipe',
    icon: '📝',
    category: 'community',
    requirementType: 'recipes_created',
    requirementValue: 1,
    pointsReward: 100,
    rarity: 'common'
  },
  {
    id: 'recipe-5',
    name: 'Content Creator',
    description: 'Create 5 study recipes',
    icon: '✍️',
    category: 'community',
    requirementType: 'recipes_created',
    requirementValue: 5,
    pointsReward: 300,
    rarity: 'rare'
  },
  {
    id: 'first-upvote',
    name: 'Community Approved',
    description: 'Receive your first upvote',
    icon: '👍',
    category: 'community',
    requirementType: 'upvotes_received',
    requirementValue: 1,
    pointsReward: 50,
    rarity: 'common'
  },
  {
    id: 'popular-recipe',
    name: 'Popular Creator',
    description: 'Get 10 upvotes on a single recipe',
    icon: '⭐',
    category: 'community',
    requirementType: 'single_recipe_upvotes',
    requirementValue: 10,
    pointsReward: 250,
    rarity: 'rare'
  },
  {
    id: 'viral-recipe',
    name: 'Viral Creator',
    description: 'Get 50 upvotes on a single recipe',
    icon: '🌟',
    category: 'community',
    requirementType: 'single_recipe_upvotes',
    requirementValue: 50,
    pointsReward: 1000,
    rarity: 'epic'
  },
  {
    id: 'helpful-commenter',
    name: 'Helpful Community Member',
    description: 'Make 25 comments',
    icon: '💬',
    category: 'community',
    requirementType: 'comments_made',
    requirementValue: 25,
    pointsReward: 200,
    rarity: 'rare'
  },
  {
    id: 'bookmark-collector',
    name: 'Knowledge Collector',
    description: 'Bookmark 20 recipes',
    icon: '🔖',
    category: 'community',
    requirementType: 'bookmarks_made',
    requirementValue: 20,
    pointsReward: 150,
    rarity: 'common'
  },

  // Milestone Achievements
  {
    id: 'level-5',
    name: 'Rising Star',
    description: 'Reach Level 5',
    icon: '🌟',
    category: 'milestone',
    requirementType: 'level',
    requirementValue: 5,
    pointsReward: 500,
    rarity: 'rare'
  },
  {
    id: 'level-10',
    name: 'StudyShare Legend',
    description: 'Reach the maximum level',
    icon: '👑',
    category: 'milestone',
    requirementType: 'level',
    requirementValue: 10,
    pointsReward: 2000,
    rarity: 'legendary'
  },
  {
    id: 'top-10-leaderboard',
    name: 'Elite Member',
    description: 'Reach top 10 on the leaderboard',
    icon: '🏅',
    category: 'milestone',
    requirementType: 'leaderboard_rank',
    requirementValue: 10,
    pointsReward: 1500,
    rarity: 'epic'
  }
]

// Achievement Categories for UI
export const ACHIEVEMENT_CATEGORIES = {
  study: {
    name: 'Study',
    description: 'Achievements for learning activities',
    icon: '📚',
    color: '#374151'
  },
  community: {
    name: 'Community',
    description: 'Achievements for community engagement',
    icon: '👥',
    color: '#374151'
  },
  consistency: {
    name: 'Consistency',
    description: 'Achievements for regular study habits',
    icon: '🔥',
    color: '#374151'
  },
  milestone: {
    name: 'Milestones',
    description: 'Major accomplishments and levels',
    icon: '🏆',
    color: '#000000'
  }
} as const

// Rarity Colors for UI
export const RARITY_COLORS = {
  common: '#6B7280',
  rare: '#374151',
  epic: '#1F2937',
  legendary: '#000000'
} as const

/**
 * Check if user qualifies for an achievement
 */
export function checkAchievementEligibility(
  achievement: Achievement,
  userStats: any
): boolean {
  switch (achievement.requirementType) {
    case 'study_sessions':
      return userStats.total_study_sessions >= achievement.requirementValue
    case 'total_study_time':
      return userStats.total_study_time >= achievement.requirementValue
    case 'current_streak':
      return userStats.current_streak_days >= achievement.requirementValue
    case 'recipes_created':
      return userStats.recipes_created >= achievement.requirementValue
    case 'upvotes_received':
      return userStats.total_upvotes_received >= achievement.requirementValue
    case 'comments_made':
      return userStats.comments_made >= achievement.requirementValue
    case 'bookmarks_made':
      return userStats.bookmarks_made >= achievement.requirementValue
    case 'level':
      return userStats.rank_level >= achievement.requirementValue
    default:
      return false
  }
}

/**
 * Get achievements by category
 */
export function getAchievementsByCategory(category: Achievement['category']): Achievement[] {
  return ACHIEVEMENTS.filter(achievement => achievement.category === category)
}

/**
 * Get achievement by ID
 */
export function getAchievementById(id: string): Achievement | undefined {
  return ACHIEVEMENTS.find(achievement => achievement.id === id)
}
