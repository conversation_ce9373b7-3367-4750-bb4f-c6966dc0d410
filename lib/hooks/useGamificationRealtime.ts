import { getLeaderboard, getUserWithStats } from '@/lib/api/gamification'
import { Achievement, LeaderboardEntry, UserStats } from '@/types'
import { useCallback, useEffect, useState } from 'react'

interface GamificationState {
  userStats: UserStats | null
  leaderboard: LeaderboardEntry[]
  newAchievements: Achievement[]
  levelUpNotification: {
    show: boolean
    oldLevel: number
    newLevel: number
  } | null
  loading: boolean
  error: string | null
}

export function useGamification(userId?: string) {
  const [state, setState] = useState<GamificationState>({
    userStats: null,
    leaderboard: [],
    newAchievements: [],
    levelUpNotification: null,
    loading: false,
    error: null
  })

  // Load initial data
  const loadInitialData = useCallback(async () => {
    if (!userId) {
      // Clear state when user logs out
      setState(prev => ({
        ...prev,
        userStats: null,
        leaderboard: [],
        newAchievements: [],
        levelUpNotification: null,
        loading: false,
        error: null
      }))
      return
    }

    try {
      setState(prev => ({ ...prev, loading: true, error: null }))
      
      const [userStatsData, leaderboardData] = await Promise.all([
        getUserWithStats(userId),
        getLeaderboard(10)
      ])

      setState(prev => ({
        ...prev,
        userStats: userStatsData?.stats || null,
        leaderboard: leaderboardData,
        loading: false
      }))
    } catch (error) {
      console.error('Error loading gamification data:', error)
      setState(prev => ({
        ...prev,
        error: 'Failed to load gamification data',
        loading: false
      }))
    }
  }, [userId])

  // Manual refresh function for user-triggered updates
  const refreshData = useCallback(async () => {
    if (!userId) return
    
    try {
      setState(prev => ({ ...prev, loading: true, error: null }))
      
      const [userStatsData, leaderboardData] = await Promise.all([
        getUserWithStats(userId),
        getLeaderboard(10)
      ])

      setState(prev => {
        const oldStats = prev.userStats
        const newStats = userStatsData?.stats || null
        
        // Check for level up
        let levelUpNotification = null
        if (oldStats && newStats && newStats.total_experience_points > oldStats.total_experience_points) {
          const oldLevel = calculateLevelFromXP(oldStats.total_experience_points)
          const newLevel = calculateLevelFromXP(newStats.total_experience_points)
          
          if (newLevel > oldLevel) {
            levelUpNotification = {
              show: true,
              oldLevel,
              newLevel
            }
          }
        }
        
        return {
          ...prev,
          userStats: newStats,
          leaderboard: leaderboardData,
          levelUpNotification: levelUpNotification || prev.levelUpNotification,
          loading: false
        }
      })
    } catch (error) {
      console.error('Error refreshing gamification data:', error)
      setState(prev => ({
        ...prev,
        error: 'Failed to refresh gamification data',
        loading: false
      }))
    }
  }, [userId])

  // Load initial data on mount
  useEffect(() => {
    loadInitialData()
  }, [loadInitialData])

  // Helper function to calculate level from experience points
  const calculateLevelFromXP = (xp: number): number => {
    if (xp >= 10000) return 10
    if (xp >= 6500) return 9
    if (xp >= 4000) return 8
    if (xp >= 2500) return 7
    if (xp >= 1600) return 6
    if (xp >= 1000) return 5
    if (xp >= 600) return 4
    if (xp >= 300) return 3
    if (xp >= 100) return 2
    return 1
  }

  // Actions
  const dismissLevelUpNotification = useCallback(() => {
    setState(prev => ({
      ...prev,
      levelUpNotification: null
    }))
  }, [])

  const dismissAchievement = useCallback((achievementId: string) => {
    setState(prev => ({
      ...prev,
      newAchievements: prev.newAchievements.filter(a => a.id !== achievementId)
    }))
  }, [])

  return {
    ...state,
    refreshData,
    dismissLevelUpNotification,
    dismissAchievement
  }
}

// Simple hook for leaderboard-only updates
export function useLeaderboard(limit: number = 10, userId?: string) {
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadLeaderboard = useCallback(async () => {
    if (!userId) {
      setLeaderboard([])
      setLoading(false)
      setError(null)
      return
    }

    try {
      setLoading(true)
      setError(null)
      const data = await getLeaderboard(limit)
      setLeaderboard(data)
    } catch (err) {
      console.error('Error loading leaderboard:', err)
      setError('Failed to load leaderboard')
    } finally {
      setLoading(false)
    }
  }, [limit, userId])

  useEffect(() => {
    loadLeaderboard()
  }, [loadLeaderboard])

  return {
    leaderboard,
    loading,
    error,
    refresh: loadLeaderboard
  }
}
