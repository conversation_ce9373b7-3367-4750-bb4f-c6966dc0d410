import { createClient } from '@supabase/supabase-js';

/**
 * Environment variable validation and logging with retry mechanism
 * Enhanced to handle build-time vs runtime scenarios (fixes Sentry Issue 6705741701)
 */
function validateEnvironmentVariables() {
  // Detect build time vs runtime
  const isBuildTime = process.env.NEXT_PHASE === 'phase-production-build' || 
                     process.env.NEXT_PHASE === 'phase-development-server' ||
                     (process.env.NODE_ENV === undefined && typeof window === 'undefined');
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  console.log('🔍 Supabase Environment Variables Check:');
  console.log('- NODE_ENV:', process.env.NODE_ENV);
  console.log('- NEXT_PUBLIC_APP_ENV:', process.env.NEXT_PUBLIC_APP_ENV);
  console.log('- VERCEL_ENV:', process.env.VERCEL_ENV);
  console.log('- NEXT_PHASE:', process.env.NEXT_PHASE);
  console.log('- Runtime:', typeof window !== 'undefined' ? 'client' : 'server');
  console.log('- Build time detected:', isBuildTime);
  console.log('- supabaseUrl exists:', !!supabaseUrl);
  console.log('- supabaseUrl length:', supabaseUrl?.length || 0);
  console.log('- supabaseAnonKey exists:', !!supabaseAnonKey);
  console.log('- supabaseAnonKey length:', supabaseAnonKey?.length || 0);
  console.log('- serviceRoleKey exists:', !!serviceRoleKey);
  
  // Enhanced debugging for development and staging
  if (process.env.NODE_ENV !== 'production') {
    console.log('- Available SUPABASE env vars:', 
      Object.keys(process.env)
        .filter(key => key.includes('SUPABASE'))
        .map(key => `${key}: ${process.env[key] ? 'SET' : 'NOT_SET'}`)
    );
  }
  
  // Enhanced error handling with detailed context (fixes Sentry Issue 6705741701)
  if (!supabaseUrl) {
    const errorMessage = `NEXT_PUBLIC_SUPABASE_URL is not defined.
      Environment: ${process.env.NODE_ENV}
      Vercel Env: ${process.env.VERCEL_ENV}
      Next Phase: ${process.env.NEXT_PHASE}
      Runtime: ${typeof window !== 'undefined' ? 'client' : 'server'}
      Build Time: ${isBuildTime}
      Timestamp: ${new Date().toISOString()}`;
    
    console.error('❌', errorMessage);
    
    // In development, provide more helpful debugging information
    if (process.env.NODE_ENV === 'development') {
      console.error('🔧 Debug Info:');
      console.error('- Available env vars:', Object.keys(process.env).filter(key => key.includes('SUPABASE')));
      console.error('- Current working directory:', process.cwd());
      console.error('- Environment files to check: .env.local, .env.development, .env');
    }
    
    // Use more specific error message for production
    const publicErrorMessage = process.env.NODE_ENV === 'production' 
      ? 'NEXT_PUBLIC_SUPABASE_URL is not defined'
      : errorMessage;
    
    throw new Error(publicErrorMessage);
  }
  
  if (!supabaseAnonKey) {
    const errorMessage = `NEXT_PUBLIC_SUPABASE_ANON_KEY is not defined.
      Environment: ${process.env.NODE_ENV}
      Vercel Env: ${process.env.VERCEL_ENV}
      Next Phase: ${process.env.NEXT_PHASE}
      Runtime: ${typeof window !== 'undefined' ? 'client' : 'server'}
      Build Time: ${isBuildTime}
      Timestamp: ${new Date().toISOString()}`;
    
    console.error('❌', errorMessage);
    
    // In development, provide more helpful debugging information
    if (process.env.NODE_ENV === 'development') {
      console.error('🔧 Debug Info:');
      console.error('- Available env vars:', Object.keys(process.env).filter(key => key.includes('SUPABASE')));
    }
    
    // Use more specific error message for production
    const publicErrorMessage = process.env.NODE_ENV === 'production' 
      ? 'NEXT_PUBLIC_SUPABASE_ANON_KEY is not defined'
      : errorMessage;
    
    throw new Error(publicErrorMessage);
  }
  
  // URL format validation
  try {
    new URL(supabaseUrl);
  } catch (error) {
    const errorMessage = `Invalid NEXT_PUBLIC_SUPABASE_URL format: ${supabaseUrl}`;
    console.error('❌', errorMessage);
    throw new Error(errorMessage);
  }
  
  console.log('✅ Supabase environment variables validated successfully');
  
  return {
    supabaseUrl,
    supabaseAnonKey,
    serviceRoleKey
  };
}

// Validate and retrieve environment variables
const { supabaseUrl, supabaseAnonKey, serviceRoleKey } = validateEnvironmentVariables();

/**
 * Create Supabase client
 */
function createSupabaseClient() {
  try {
    console.log('🚀 Creating Supabase client...');
    
    const client = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        flowType: 'pkce'
      },
      global: {
        headers: {
          'X-Client-Info': 'studyshare-web'
        }
      }
    });
    
    console.log('✅ Supabase client created successfully');
    return client;
  } catch (error) {
    console.error('❌ Failed to create Supabase client:', error);
    throw error;
  }
}

/**
 * Create Supabase Admin client
 */
function createSupabaseAdminClient() {
  if (typeof window !== 'undefined') {
    console.log('⚠️ Supabase Admin client not available on client-side');
    return null;
  }
  
  if (!serviceRoleKey) {
    console.log('⚠️ SUPABASE_SERVICE_ROLE_KEY not available, Admin client disabled');
    return null;
  }
  
  try {
    console.log('🚀 Creating Supabase Admin client...');
    
    const adminClient = createClient(supabaseUrl, serviceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    
    console.log('✅ Supabase Admin client created successfully');
    return adminClient;
  } catch (error) {
    console.error('❌ Failed to create Supabase Admin client:', error);
    return null;
  }
}

export const supabase = createSupabaseClient();
export const supabaseAdmin = createSupabaseAdminClient();

// Connection test (development environment only)
if (process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_DEBUG_MODE === 'true') {
  (async () => {
    try {
      // Connection test with retry functionality
      await retryOperation(
        async () => {
          const { error } = await supabase.from('profiles').select('count', { count: 'exact', head: true });
          if (error) {
            throw error;
          }
          return true;
        },
        {
          maxAttempts: 3,
          baseDelay: 1000,
          shouldRetry: (error: any) => {
            return error?.message?.includes('Failed to fetch') ||
                   error?.message?.includes('NetworkError') ||
                   error?.message?.includes('timeout') ||
                   error?.status >= 500;
          }
        }
      );
      console.log('✅ Supabase connection test successful');
    } catch (error: any) {
      console.warn('⚠️ Supabase connection test failed after retries:', error);
    }
  })();
}

/**
 * Execute operation with retry functionality
 */
async function retryOperation<T>(
  operation: () => Promise<T>,
  options: {
    maxAttempts: number;
    baseDelay: number;
    shouldRetry: (error: any) => boolean;
  }
): Promise<T> {
  let lastError: any;

  for (let attempt = 1; attempt <= options.maxAttempts; attempt++) {
    try {
      console.log(`🔄 Supabase Retry: Attempt ${attempt}/${options.maxAttempts}`);
      const result = await operation();
      
      if (attempt > 1) {
        console.log(`✅ Supabase Retry: Success on attempt ${attempt}`);
      }
      
      return result;
    } catch (error) {
      lastError = error;
      console.warn(`❌ Supabase Retry: Attempt ${attempt} failed:`, error);

      if (attempt === options.maxAttempts || !options.shouldRetry(error)) {
        throw error;
      }

      const delay = options.baseDelay * Math.pow(2, attempt - 1);
      console.log(`⏳ Supabase Retry: Waiting ${delay}ms before attempt ${attempt + 1}`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}

/**
 * Helper to execute Supabase operations with retry functionality
 */
export async function executeWithRetry<T>(
  operation: () => Promise<T>,
  context: string = 'Supabase operation'
): Promise<T> {
  return retryOperation(operation, {
    maxAttempts: 3,
    baseDelay: 1000,
    shouldRetry: (error: any) => {
      // Retry for network errors and temporary failures
      if (error?.message?.includes('Failed to fetch')) {
        console.log(`🔄 ${context}: Network error detected, will retry`);
        return true;
      }
      if (error?.message?.includes('NetworkError')) {
        console.log(`🔄 ${context}: Network error detected, will retry`);
        return true;
      }
      if (error?.message?.includes('timeout')) {
        console.log(`🔄 ${context}: Timeout error detected, will retry`);
        return true;
      }
      if (error?.code === 'NETWORK_ERROR') {
        console.log(`🔄 ${context}: Network error code detected, will retry`);
        return true;
      }
      if (error?.status >= 500) {
        console.log(`🔄 ${context}: Server error (${error.status}) detected, will retry`);
        return true;
      }
      if (error?.status === 429) {
        console.log(`🔄 ${context}: Rate limit error detected, will retry`);
        return true;
      }
      if (error?.status === 408) {
        console.log(`🔄 ${context}: Request timeout detected, will retry`);
        return true;
      }
      
      console.log(`❌ ${context}: Error not retryable:`, error?.message || error);
      return false;
    }
  });
}
