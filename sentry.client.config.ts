// This file configures the initialization of Sentry on the browser/client side.
// The config you add here will be used whenever a user loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import { isDevelopment } from "@/lib/config/environment";
import * as Sentry from "@sentry/nextjs";

// Disable Sentry in development environment
if (isDevelopment()) {
  console.log('🔧 Sentry Client: Disabled in development environment');
} else {
  console.log('🔧 Sentry Client: DSN available:', !!process.env.NEXT_PUBLIC_SENTRY_DSN);

  Sentry.init({
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN || "https://<EMAIL>/4509542160596992",

    // Adjust this value in production, or use tracesSampler for greater control
    tracesSampleRate: 1,

    // Setting this option to true will print useful information to the console while you're setting up Sentry.
    debug: true,

    // Add beforeSend to log what's being sent
    beforeSend(event, _hint) {
      console.log('🚀 Sentry: Attempting to send event:', event.event_id, event.exception?.values?.[0]?.value || event.message)
      console.log('🔍 Sentry: Event details:', event)
      return event
    },

    // Reduce replay features that might cause network issues
    replaysOnErrorSampleRate: 0.1,
    replaysSessionSampleRate: 0.0, // Disable session replay for now

    // Simplified integrations
    integrations: [
      // Remove replay integration temporarily to isolate the issue
    ],
  });
}
