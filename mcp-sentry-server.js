#!/usr/bin/env node

/**
 * Sentry API Client
 * Simple script to fetch Sentry issues and data
 */

const https = require('https');
const fs = require('fs');

// Sentry API configuration
const SENTRY_ORG = process.env.SENTRY_ORG || 'forple';
const SENTRY_PROJECT = process.env.SENTRY_PROJECT || 'forple-stg';
const SENTRY_AUTH_TOKEN = process.env.SENTRY_AUTH_TOKEN || '***********************************************************************';
const SENTRY_BASE_URL = 'https://sentry.io/api/0';

// Simple HTTP request function using https
async function makeRequest(url, headers = {}) {
  return new Promise((resolve, reject) => {
    try {
      console.log(`Making request to: ${url}`);
      console.log(`Using token: ${SENTRY_AUTH_TOKEN.substring(0, 10)}...`);

      const urlObj = new URL(url);
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || 443,
        path: urlObj.pathname + urlObj.search,
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${SENTRY_AUTH_TOKEN}`,
          'Content-Type': 'application/json',
          'User-Agent': 'SentryMCPClient/1.0',
          ...headers
        }
      };

      const req = https.request(options, (res) => {
        console.log(`Response status: ${res.statusCode}`);
        console.log(`Response headers:`, res.headers);

        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          try {
            const jsonData = JSON.parse(data);
            if (res.statusCode >= 400) {
              console.error(`HTTP Error ${res.statusCode}:`, jsonData);
            }
            resolve(jsonData);
          } catch (parseError) {
            console.error('Failed to parse JSON:', data);
            reject(parseError);
          }
        });
      });

      req.on('error', (error) => {
        console.error('Request failed:', error.message);
        reject(error);
      });

      req.end();
    } catch (error) {
      console.error('Request setup failed:', error.message);
      reject(error);
    }
  });
}

// Main functions
async function listOrganizations() {
  try {
    const url = `${SENTRY_BASE_URL}/organizations/`;
    console.log(`Requesting organizations: ${url}`);

    const response = await makeRequest(url);
    console.log('Organizations response:', JSON.stringify(response, null, 2));

    return response;
  } catch (error) {
    console.error('Error fetching organizations:', error.message);
    return [];
  }
}

async function listProjects() {
  try {
    const url = `${SENTRY_BASE_URL}/organizations/${SENTRY_ORG}/projects/`;
    console.log(`Requesting projects: ${url}`);

    const response = await makeRequest(url);
    console.log('Projects response:', JSON.stringify(response, null, 2));

    return response;
  } catch (error) {
    console.error('Error fetching projects:', error.message);
    return [];
  }
}

async function listIssues(status = 'unresolved', limit = 25) {
  try {
    // First try organization-level issues
    let url = `${SENTRY_BASE_URL}/organizations/${SENTRY_ORG}/issues/?query=is:${status}&limit=${limit}`;
    console.log(`Requesting org issues: ${url}`);

    let response = await makeRequest(url);
    console.log('Org issues response:', JSON.stringify(response, null, 2));

    // If that fails, try project-specific
    if (response.detail) {
      url = `${SENTRY_BASE_URL}/projects/${SENTRY_ORG}/${SENTRY_PROJECT}/issues/?query=is:${status}&limit=${limit}`;
      console.log(`Requesting project issues: ${url}`);
      response = await makeRequest(url);
      console.log('Project issues response:', JSON.stringify(response, null, 2));
    }

    // Handle different response formats
    const issues = Array.isArray(response) ? response : (response.data || []);

    console.log(`\n=== Sentry Issues (${status}) ===`);
    console.log(`Found ${issues.length} issues:\n`);

    if (issues.length === 0) {
      console.log('No issues found.');
      return issues;
    }

    issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue.title || 'No title'}`);
      console.log(`   ID: ${issue.id}`);
      console.log(`   Status: ${issue.status}`);
      console.log(`   Level: ${issue.level}`);
      console.log(`   Count: ${issue.count}`);
      console.log(`   First Seen: ${issue.firstSeen}`);
      console.log(`   Last Seen: ${issue.lastSeen}`);
      console.log(`   URL: ${issue.permalink}`);
      console.log('');
    });

    return issues;
  } catch (error) {
    console.error('Error fetching issues:', error.message);
    console.error('Full error:', error);
    return [];
  }
}

async function getIssueDetails(issueId) {
  try {
    const url = `${SENTRY_BASE_URL}/issues/${issueId}/`;
    const issue = await makeRequest(url);

    console.log(`\n=== Issue Details: ${issueId} ===`);
    console.log(JSON.stringify(issue, null, 2));

    return issue;
  } catch (error) {
    console.error('Error fetching issue details:', error.message);
    return null;
  }
}

async function getProjectStats() {
  try {
    const url = `${SENTRY_BASE_URL}/projects/${SENTRY_ORG}/${SENTRY_PROJECT}/stats/?stat=received&resolution=1h&since=24h`;
    const stats = await makeRequest(url);

    console.log(`\n=== Project Statistics ===`);
    console.log(JSON.stringify(stats, null, 2));

    return stats;
  } catch (error) {
    console.error('Error fetching project stats:', error.message);
    return null;
  }
}

// Command line interface
async function main() {
  const command = process.argv[2];
  const arg1 = process.argv[3];
  const arg2 = process.argv[4];

  console.log('Sentry API Client');
  console.log('================');

  switch (command) {
    case 'issues':
      const status = arg1 || 'unresolved';
      const limit = parseInt(arg2) || 25;
      await listIssues(status, limit);
      break;

    case 'issue':
      if (!arg1) {
        console.error('Please provide an issue ID');
        process.exit(1);
      }
      await getIssueDetails(arg1);
      break;

    case 'stats':
      await getProjectStats();
      break;

    case 'projects':
      await listProjects();
      break;

    case 'orgs':
      await listOrganizations();
      break;

    default:
      console.log('Usage:');
      console.log('  node mcp-sentry-server.js issues [status] [limit]');
      console.log('  node mcp-sentry-server.js issue <issue_id>');
      console.log('  node mcp-sentry-server.js stats');
      console.log('  node mcp-sentry-server.js projects');
      console.log('');
      console.log('Examples:');
      console.log('  node mcp-sentry-server.js issues unresolved 10');
      console.log('  node mcp-sentry-server.js issue 1234567890');
      console.log('  node mcp-sentry-server.js stats');
      console.log('  node mcp-sentry-server.js projects');
      break;
  }
}

if (require.main === module) {
  main().catch((error) => {
    console.error('Error:', error.message);
    process.exit(1);
  });
}
